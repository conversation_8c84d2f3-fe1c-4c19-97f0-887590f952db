{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { illegalArgument } from '../../../base/common/errors.js';\nimport { AriaLabelProvider, ElectronAcceleratorLabelProvider, UILabelProvider, UserSettingsLabelProvider } from '../../../base/common/keybindingLabels.js';\nimport { ResolvedKeybinding, ResolvedChord } from '../../../base/common/keybindings.js';\nexport class BaseResolvedKeybinding extends ResolvedKeybinding {\n  constructor(os, chords) {\n    super();\n    if (chords.length === 0) {\n      throw illegalArgument(`chords`);\n    }\n    this._os = os;\n    this._chords = chords;\n  }\n  getLabel() {\n    return UILabelProvider.toLabel(this._os, this._chords, keybinding => this._getLabel(keybinding));\n  }\n  getAriaLabel() {\n    return AriaLabelProvider.toLabel(this._os, this._chords, keybinding => this._getAriaLabel(keybinding));\n  }\n  getElectronAccelerator() {\n    if (this._chords.length > 1) {\n      // [Electron Accelerators] Electron cannot handle chords\n      return null;\n    }\n    if (this._chords[0].isDuplicateModifierCase()) {\n      // [Electron Accelerators] Electron cannot handle modifier only keybindings\n      // e.g. \"shift shift\"\n      return null;\n    }\n    return ElectronAcceleratorLabelProvider.toLabel(this._os, this._chords, keybinding => this._getElectronAccelerator(keybinding));\n  }\n  getUserSettingsLabel() {\n    return UserSettingsLabelProvider.toLabel(this._os, this._chords, keybinding => this._getUserSettingsLabel(keybinding));\n  }\n  hasMultipleChords() {\n    return this._chords.length > 1;\n  }\n  getChords() {\n    return this._chords.map(keybinding => this._getChord(keybinding));\n  }\n  _getChord(keybinding) {\n    return new ResolvedChord(keybinding.ctrlKey, keybinding.shiftKey, keybinding.altKey, keybinding.metaKey, this._getLabel(keybinding), this._getAriaLabel(keybinding));\n  }\n  getDispatchChords() {\n    return this._chords.map(keybinding => this._getChordDispatch(keybinding));\n  }\n  getSingleModifierDispatchChords() {\n    return this._chords.map(keybinding => this._getSingleModifierChordDispatch(keybinding));\n  }\n}", "map": {"version": 3, "names": ["illegalArgument", "<PERSON><PERSON><PERSON><PERSON>", "ElectronAcceleratorLabelProvider", "UILabe<PERSON>", "UserSettingsLabelProvider", "ResolvedKeybinding", "ResolvedChord", "BaseResolvedKeybinding", "constructor", "os", "chords", "length", "_os", "_chords", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "keybinding", "_get<PERSON><PERSON>l", "getAriaLabel", "_getAria<PERSON>abel", "getElectronAccelerator", "isDuplicateModifierCase", "_getElectronAccelerator", "getUserSettingsLabel", "_getUserSettingsLabel", "hasMultipleChords", "getChords", "map", "_getChord", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "getDispatchChords", "_getChordDispatch", "getSingleModifierDispatchChords", "_getSingleModifierChordDispatch"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/keybinding/common/baseResolvedKeybinding.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { illegalArgument } from '../../../base/common/errors.js';\nimport { AriaLabelProvider, ElectronAcceleratorLabelProvider, UILabelProvider, UserSettingsLabelProvider } from '../../../base/common/keybindingLabels.js';\nimport { ResolvedKeybinding, ResolvedChord } from '../../../base/common/keybindings.js';\nexport class BaseResolvedKeybinding extends ResolvedKeybinding {\n    constructor(os, chords) {\n        super();\n        if (chords.length === 0) {\n            throw illegalArgument(`chords`);\n        }\n        this._os = os;\n        this._chords = chords;\n    }\n    getLabel() {\n        return UILabelProvider.toLabel(this._os, this._chords, (keybinding) => this._getLabel(keybinding));\n    }\n    getAriaLabel() {\n        return AriaLabelProvider.toLabel(this._os, this._chords, (keybinding) => this._getAriaLabel(keybinding));\n    }\n    getElectronAccelerator() {\n        if (this._chords.length > 1) {\n            // [Electron Accelerators] Electron cannot handle chords\n            return null;\n        }\n        if (this._chords[0].isDuplicateModifierCase()) {\n            // [Electron Accelerators] Electron cannot handle modifier only keybindings\n            // e.g. \"shift shift\"\n            return null;\n        }\n        return ElectronAcceleratorLabelProvider.toLabel(this._os, this._chords, (keybinding) => this._getElectronAccelerator(keybinding));\n    }\n    getUserSettingsLabel() {\n        return UserSettingsLabelProvider.toLabel(this._os, this._chords, (keybinding) => this._getUserSettingsLabel(keybinding));\n    }\n    hasMultipleChords() {\n        return (this._chords.length > 1);\n    }\n    getChords() {\n        return this._chords.map((keybinding) => this._getChord(keybinding));\n    }\n    _getChord(keybinding) {\n        return new ResolvedChord(keybinding.ctrlKey, keybinding.shiftKey, keybinding.altKey, keybinding.metaKey, this._getLabel(keybinding), this._getAriaLabel(keybinding));\n    }\n    getDispatchChords() {\n        return this._chords.map((keybinding) => this._getChordDispatch(keybinding));\n    }\n    getSingleModifierDispatchChords() {\n        return this._chords.map((keybinding) => this._getSingleModifierChordDispatch(keybinding));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,iBAAiB,EAAEC,gCAAgC,EAAEC,eAAe,EAAEC,yBAAyB,QAAQ,0CAA0C;AAC1J,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,qCAAqC;AACvF,OAAO,MAAMC,sBAAsB,SAASF,kBAAkB,CAAC;EAC3DG,WAAWA,CAACC,EAAE,EAAEC,MAAM,EAAE;IACpB,KAAK,CAAC,CAAC;IACP,IAAIA,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MACrB,MAAMX,eAAe,CAAC,QAAQ,CAAC;IACnC;IACA,IAAI,CAACY,GAAG,GAAGH,EAAE;IACb,IAAI,CAACI,OAAO,GAAGH,MAAM;EACzB;EACAI,QAAQA,CAAA,EAAG;IACP,OAAOX,eAAe,CAACY,OAAO,CAAC,IAAI,CAACH,GAAG,EAAE,IAAI,CAACC,OAAO,EAAGG,UAAU,IAAK,IAAI,CAACC,SAAS,CAACD,UAAU,CAAC,CAAC;EACtG;EACAE,YAAYA,CAAA,EAAG;IACX,OAAOjB,iBAAiB,CAACc,OAAO,CAAC,IAAI,CAACH,GAAG,EAAE,IAAI,CAACC,OAAO,EAAGG,UAAU,IAAK,IAAI,CAACG,aAAa,CAACH,UAAU,CAAC,CAAC;EAC5G;EACAI,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACP,OAAO,CAACF,MAAM,GAAG,CAAC,EAAE;MACzB;MACA,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACQ,uBAAuB,CAAC,CAAC,EAAE;MAC3C;MACA;MACA,OAAO,IAAI;IACf;IACA,OAAOnB,gCAAgC,CAACa,OAAO,CAAC,IAAI,CAACH,GAAG,EAAE,IAAI,CAACC,OAAO,EAAGG,UAAU,IAAK,IAAI,CAACM,uBAAuB,CAACN,UAAU,CAAC,CAAC;EACrI;EACAO,oBAAoBA,CAAA,EAAG;IACnB,OAAOnB,yBAAyB,CAACW,OAAO,CAAC,IAAI,CAACH,GAAG,EAAE,IAAI,CAACC,OAAO,EAAGG,UAAU,IAAK,IAAI,CAACQ,qBAAqB,CAACR,UAAU,CAAC,CAAC;EAC5H;EACAS,iBAAiBA,CAAA,EAAG;IAChB,OAAQ,IAAI,CAACZ,OAAO,CAACF,MAAM,GAAG,CAAC;EACnC;EACAe,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACb,OAAO,CAACc,GAAG,CAAEX,UAAU,IAAK,IAAI,CAACY,SAAS,CAACZ,UAAU,CAAC,CAAC;EACvE;EACAY,SAASA,CAACZ,UAAU,EAAE;IAClB,OAAO,IAAIV,aAAa,CAACU,UAAU,CAACa,OAAO,EAAEb,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACe,MAAM,EAAEf,UAAU,CAACgB,OAAO,EAAE,IAAI,CAACf,SAAS,CAACD,UAAU,CAAC,EAAE,IAAI,CAACG,aAAa,CAACH,UAAU,CAAC,CAAC;EACxK;EACAiB,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACpB,OAAO,CAACc,GAAG,CAAEX,UAAU,IAAK,IAAI,CAACkB,iBAAiB,CAAClB,UAAU,CAAC,CAAC;EAC/E;EACAmB,+BAA+BA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAACtB,OAAO,CAACc,GAAG,CAAEX,UAAU,IAAK,IAAI,CAACoB,+BAA+B,CAACpB,UAAU,CAAC,CAAC;EAC7F;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}