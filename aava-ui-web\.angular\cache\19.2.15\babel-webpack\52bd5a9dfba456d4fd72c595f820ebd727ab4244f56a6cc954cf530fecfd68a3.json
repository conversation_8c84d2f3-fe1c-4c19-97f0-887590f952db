{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class LinesDiff {\n  constructor(changes,\n  /**\n   * Sorted by original line ranges.\n   * The original line ranges and the modified line ranges must be disjoint (but can be touching).\n   */\n  moves,\n  /**\n   * Indicates if the time out was reached.\n   * In that case, the diffs might be an approximation and the user should be asked to rerun the diff with more time.\n   */\n  hitTimeout) {\n    this.changes = changes;\n    this.moves = moves;\n    this.hitTimeout = hitTimeout;\n  }\n}\nexport class MovedText {\n  constructor(lineRangeMapping, changes) {\n    this.lineRangeMapping = lineRangeMapping;\n    this.changes = changes;\n  }\n}", "map": {"version": 3, "names": ["LinesDiff", "constructor", "changes", "moves", "hitTimeout", "MovedText", "lineRangeMapping"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/linesDiffComputer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class LinesDiff {\n    constructor(changes, \n    /**\n     * Sorted by original line ranges.\n     * The original line ranges and the modified line ranges must be disjoint (but can be touching).\n     */\n    moves, \n    /**\n     * Indicates if the time out was reached.\n     * In that case, the diffs might be an approximation and the user should be asked to rerun the diff with more time.\n     */\n    hitTimeout) {\n        this.changes = changes;\n        this.moves = moves;\n        this.hitTimeout = hitTimeout;\n    }\n}\nexport class MovedText {\n    constructor(lineRangeMapping, changes) {\n        this.lineRangeMapping = lineRangeMapping;\n        this.changes = changes;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,SAAS,CAAC;EACnBC,WAAWA,CAACC,OAAO;EACnB;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU,EAAE;IACR,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;AACJ;AACA,OAAO,MAAMC,SAAS,CAAC;EACnBJ,WAAWA,CAACK,gBAAgB,EAAEJ,OAAO,EAAE;IACnC,IAAI,CAACI,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACJ,OAAO,GAAGA,OAAO;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}