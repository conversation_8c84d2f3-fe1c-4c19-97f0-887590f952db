{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nexport function isMousePositionWithinElement(element, posx, posy) {\n  const elementRect = dom.getDomNodePagePosition(element);\n  if (posx < elementRect.left || posx > elementRect.left + elementRect.width || posy < elementRect.top || posy > elementRect.top + elementRect.height) {\n    return false;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["dom", "isMousePositionWithinElement", "element", "posx", "posy", "elementRect", "getDomNodePagePosition", "left", "width", "top", "height"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/hoverUtils.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nexport function isMousePositionWithinElement(element, posx, posy) {\n    const elementRect = dom.getDomNodePagePosition(element);\n    if (posx < elementRect.left\n        || posx > elementRect.left + elementRect.width\n        || posy < elementRect.top\n        || posy > elementRect.top + elementRect.height) {\n        return false;\n    }\n    return true;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,iCAAiC;AACtD,OAAO,SAASC,4BAA4BA,CAACC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC9D,MAAMC,WAAW,GAAGL,GAAG,CAACM,sBAAsB,CAACJ,OAAO,CAAC;EACvD,IAAIC,IAAI,GAAGE,WAAW,CAACE,IAAI,IACpBJ,IAAI,GAAGE,WAAW,CAACE,IAAI,GAAGF,WAAW,CAACG,KAAK,IAC3CJ,IAAI,GAAGC,WAAW,CAACI,GAAG,IACtBL,IAAI,GAAGC,WAAW,CAACI,GAAG,GAAGJ,WAAW,CAACK,MAAM,EAAE;IAChD,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}