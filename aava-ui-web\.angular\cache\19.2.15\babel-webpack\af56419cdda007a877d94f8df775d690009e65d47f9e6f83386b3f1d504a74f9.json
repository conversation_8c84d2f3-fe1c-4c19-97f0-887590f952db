{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { onUnexpectedError, transformErrorForSerialization } from '../errors.js';\nimport { Emitter } from '../event.js';\nimport { Disposable } from '../lifecycle.js';\nimport { FileAccess } from '../network.js';\nimport { isWeb } from '../platform.js';\nimport * as strings from '../strings.js';\n// ESM-comment-begin\n// const isESM = false;\n// ESM-comment-end\n// ESM-uncomment-begin\nconst isESM = true;\n// ESM-uncomment-end\nconst DEFAULT_CHANNEL = 'default';\nconst INITIALIZE = '$initialize';\nlet webWorkerWarningLogged = false;\nexport function logOnceWebWorkerWarning(err) {\n  if (!isWeb) {\n    // running tests\n    return;\n  }\n  if (!webWorkerWarningLogged) {\n    webWorkerWarningLogged = true;\n    console.warn('Could not create web worker(s). Falling back to loading web worker code in main thread, which might cause UI freezes. Please see https://github.com/microsoft/monaco-editor#faq');\n  }\n  console.warn(err.message);\n}\nclass RequestMessage {\n  constructor(vsWorker, req, channel, method, args) {\n    this.vsWorker = vsWorker;\n    this.req = req;\n    this.channel = channel;\n    this.method = method;\n    this.args = args;\n    this.type = 0 /* MessageType.Request */;\n  }\n}\nclass ReplyMessage {\n  constructor(vsWorker, seq, res, err) {\n    this.vsWorker = vsWorker;\n    this.seq = seq;\n    this.res = res;\n    this.err = err;\n    this.type = 1 /* MessageType.Reply */;\n  }\n}\nclass SubscribeEventMessage {\n  constructor(vsWorker, req, channel, eventName, arg) {\n    this.vsWorker = vsWorker;\n    this.req = req;\n    this.channel = channel;\n    this.eventName = eventName;\n    this.arg = arg;\n    this.type = 2 /* MessageType.SubscribeEvent */;\n  }\n}\nclass EventMessage {\n  constructor(vsWorker, req, event) {\n    this.vsWorker = vsWorker;\n    this.req = req;\n    this.event = event;\n    this.type = 3 /* MessageType.Event */;\n  }\n}\nclass UnsubscribeEventMessage {\n  constructor(vsWorker, req) {\n    this.vsWorker = vsWorker;\n    this.req = req;\n    this.type = 4 /* MessageType.UnsubscribeEvent */;\n  }\n}\nclass SimpleWorkerProtocol {\n  constructor(handler) {\n    this._workerId = -1;\n    this._handler = handler;\n    this._lastSentReq = 0;\n    this._pendingReplies = Object.create(null);\n    this._pendingEmitters = new Map();\n    this._pendingEvents = new Map();\n  }\n  setWorkerId(workerId) {\n    this._workerId = workerId;\n  }\n  sendMessage(channel, method, args) {\n    const req = String(++this._lastSentReq);\n    return new Promise((resolve, reject) => {\n      this._pendingReplies[req] = {\n        resolve: resolve,\n        reject: reject\n      };\n      this._send(new RequestMessage(this._workerId, req, channel, method, args));\n    });\n  }\n  listen(channel, eventName, arg) {\n    let req = null;\n    const emitter = new Emitter({\n      onWillAddFirstListener: () => {\n        req = String(++this._lastSentReq);\n        this._pendingEmitters.set(req, emitter);\n        this._send(new SubscribeEventMessage(this._workerId, req, channel, eventName, arg));\n      },\n      onDidRemoveLastListener: () => {\n        this._pendingEmitters.delete(req);\n        this._send(new UnsubscribeEventMessage(this._workerId, req));\n        req = null;\n      }\n    });\n    return emitter.event;\n  }\n  handleMessage(message) {\n    if (!message || !message.vsWorker) {\n      return;\n    }\n    if (this._workerId !== -1 && message.vsWorker !== this._workerId) {\n      return;\n    }\n    this._handleMessage(message);\n  }\n  createProxyToRemoteChannel(channel, sendMessageBarrier) {\n    var _this = this;\n    const handler = {\n      get: (target, name) => {\n        if (typeof name === 'string' && !target[name]) {\n          if (propertyIsDynamicEvent(name)) {\n            // onDynamic...\n            target[name] = arg => {\n              return this.listen(channel, name, arg);\n            };\n          } else if (propertyIsEvent(name)) {\n            // on...\n            target[name] = this.listen(channel, name, undefined);\n          } else if (name.charCodeAt(0) === 36 /* CharCode.DollarSign */) {\n            // $...\n            target[name] = /*#__PURE__*/_asyncToGenerator(function* (...myArgs) {\n              yield sendMessageBarrier?.();\n              return _this.sendMessage(channel, name, myArgs);\n            });\n          }\n        }\n        return target[name];\n      }\n    };\n    return new Proxy(Object.create(null), handler);\n  }\n  _handleMessage(msg) {\n    switch (msg.type) {\n      case 1 /* MessageType.Reply */:\n        return this._handleReplyMessage(msg);\n      case 0 /* MessageType.Request */:\n        return this._handleRequestMessage(msg);\n      case 2 /* MessageType.SubscribeEvent */:\n        return this._handleSubscribeEventMessage(msg);\n      case 3 /* MessageType.Event */:\n        return this._handleEventMessage(msg);\n      case 4 /* MessageType.UnsubscribeEvent */:\n        return this._handleUnsubscribeEventMessage(msg);\n    }\n  }\n  _handleReplyMessage(replyMessage) {\n    if (!this._pendingReplies[replyMessage.seq]) {\n      console.warn('Got reply to unknown seq');\n      return;\n    }\n    const reply = this._pendingReplies[replyMessage.seq];\n    delete this._pendingReplies[replyMessage.seq];\n    if (replyMessage.err) {\n      let err = replyMessage.err;\n      if (replyMessage.err.$isError) {\n        err = new Error();\n        err.name = replyMessage.err.name;\n        err.message = replyMessage.err.message;\n        err.stack = replyMessage.err.stack;\n      }\n      reply.reject(err);\n      return;\n    }\n    reply.resolve(replyMessage.res);\n  }\n  _handleRequestMessage(requestMessage) {\n    const req = requestMessage.req;\n    const result = this._handler.handleMessage(requestMessage.channel, requestMessage.method, requestMessage.args);\n    result.then(r => {\n      this._send(new ReplyMessage(this._workerId, req, r, undefined));\n    }, e => {\n      if (e.detail instanceof Error) {\n        // Loading errors have a detail property that points to the actual error\n        e.detail = transformErrorForSerialization(e.detail);\n      }\n      this._send(new ReplyMessage(this._workerId, req, undefined, transformErrorForSerialization(e)));\n    });\n  }\n  _handleSubscribeEventMessage(msg) {\n    const req = msg.req;\n    const disposable = this._handler.handleEvent(msg.channel, msg.eventName, msg.arg)(event => {\n      this._send(new EventMessage(this._workerId, req, event));\n    });\n    this._pendingEvents.set(req, disposable);\n  }\n  _handleEventMessage(msg) {\n    if (!this._pendingEmitters.has(msg.req)) {\n      console.warn('Got event for unknown req');\n      return;\n    }\n    this._pendingEmitters.get(msg.req).fire(msg.event);\n  }\n  _handleUnsubscribeEventMessage(msg) {\n    if (!this._pendingEvents.has(msg.req)) {\n      console.warn('Got unsubscribe for unknown req');\n      return;\n    }\n    this._pendingEvents.get(msg.req).dispose();\n    this._pendingEvents.delete(msg.req);\n  }\n  _send(msg) {\n    const transfer = [];\n    if (msg.type === 0 /* MessageType.Request */) {\n      for (let i = 0; i < msg.args.length; i++) {\n        if (msg.args[i] instanceof ArrayBuffer) {\n          transfer.push(msg.args[i]);\n        }\n      }\n    } else if (msg.type === 1 /* MessageType.Reply */) {\n      if (msg.res instanceof ArrayBuffer) {\n        transfer.push(msg.res);\n      }\n    }\n    this._handler.sendMessage(msg, transfer);\n  }\n}\n/**\n * Main thread side\n */\nexport class SimpleWorkerClient extends Disposable {\n  constructor(workerFactory, workerDescriptor) {\n    var _this2;\n    super();\n    _this2 = this;\n    this._localChannels = new Map();\n    this._worker = this._register(workerFactory.create({\n      amdModuleId: 'vs/base/common/worker/simpleWorker',\n      esmModuleLocation: workerDescriptor.esmModuleLocation,\n      label: workerDescriptor.label\n    }, msg => {\n      this._protocol.handleMessage(msg);\n    }, err => {\n      // in Firefox, web workers fail lazily :(\n      // we will reject the proxy\n      onUnexpectedError(err);\n    }));\n    this._protocol = new SimpleWorkerProtocol({\n      sendMessage: (msg, transfer) => {\n        this._worker.postMessage(msg, transfer);\n      },\n      handleMessage: (channel, method, args) => {\n        return this._handleMessage(channel, method, args);\n      },\n      handleEvent: (channel, eventName, arg) => {\n        return this._handleEvent(channel, eventName, arg);\n      }\n    });\n    this._protocol.setWorkerId(this._worker.getId());\n    // Gather loader configuration\n    let loaderConfiguration = null;\n    const globalRequire = globalThis.require;\n    if (typeof globalRequire !== 'undefined' && typeof globalRequire.getConfig === 'function') {\n      // Get the configuration from the Monaco AMD Loader\n      loaderConfiguration = globalRequire.getConfig();\n    } else if (typeof globalThis.requirejs !== 'undefined') {\n      // Get the configuration from requirejs\n      loaderConfiguration = globalThis.requirejs.s.contexts._.config;\n    }\n    // Send initialize message\n    this._onModuleLoaded = this._protocol.sendMessage(DEFAULT_CHANNEL, INITIALIZE, [this._worker.getId(), JSON.parse(JSON.stringify(loaderConfiguration)), workerDescriptor.amdModuleId]);\n    this.proxy = this._protocol.createProxyToRemoteChannel(DEFAULT_CHANNEL, /*#__PURE__*/_asyncToGenerator(function* () {\n      yield _this2._onModuleLoaded;\n    }));\n    this._onModuleLoaded.catch(e => {\n      this._onError('Worker failed to load ' + workerDescriptor.amdModuleId, e);\n    });\n  }\n  _handleMessage(channelName, method, args) {\n    const channel = this._localChannels.get(channelName);\n    if (!channel) {\n      return Promise.reject(new Error(`Missing channel ${channelName} on main thread`));\n    }\n    if (typeof channel[method] !== 'function') {\n      return Promise.reject(new Error(`Missing method ${method} on main thread channel ${channelName}`));\n    }\n    try {\n      return Promise.resolve(channel[method].apply(channel, args));\n    } catch (e) {\n      return Promise.reject(e);\n    }\n  }\n  _handleEvent(channelName, eventName, arg) {\n    const channel = this._localChannels.get(channelName);\n    if (!channel) {\n      throw new Error(`Missing channel ${channelName} on main thread`);\n    }\n    if (propertyIsDynamicEvent(eventName)) {\n      const event = channel[eventName].call(channel, arg);\n      if (typeof event !== 'function') {\n        throw new Error(`Missing dynamic event ${eventName} on main thread channel ${channelName}.`);\n      }\n      return event;\n    }\n    if (propertyIsEvent(eventName)) {\n      const event = channel[eventName];\n      if (typeof event !== 'function') {\n        throw new Error(`Missing event ${eventName} on main thread channel ${channelName}.`);\n      }\n      return event;\n    }\n    throw new Error(`Malformed event name ${eventName}`);\n  }\n  setChannel(channel, handler) {\n    this._localChannels.set(channel, handler);\n  }\n  _onError(message, error) {\n    console.error(message);\n    console.info(error);\n  }\n}\nfunction propertyIsEvent(name) {\n  // Assume a property is an event if it has a form of \"onSomething\"\n  return name[0] === 'o' && name[1] === 'n' && strings.isUpperAsciiLetter(name.charCodeAt(2));\n}\nfunction propertyIsDynamicEvent(name) {\n  // Assume a property is a dynamic event (a method that returns an event) if it has a form of \"onDynamicSomething\"\n  return /^onDynamic/.test(name) && strings.isUpperAsciiLetter(name.charCodeAt(9));\n}\n/**\n * Worker side\n */\nexport class SimpleWorkerServer {\n  constructor(postMessage, requestHandlerFactory) {\n    this._localChannels = new Map();\n    this._remoteChannels = new Map();\n    this._requestHandlerFactory = requestHandlerFactory;\n    this._requestHandler = null;\n    this._protocol = new SimpleWorkerProtocol({\n      sendMessage: (msg, transfer) => {\n        postMessage(msg, transfer);\n      },\n      handleMessage: (channel, method, args) => this._handleMessage(channel, method, args),\n      handleEvent: (channel, eventName, arg) => this._handleEvent(channel, eventName, arg)\n    });\n  }\n  onmessage(msg) {\n    this._protocol.handleMessage(msg);\n  }\n  _handleMessage(channel, method, args) {\n    if (channel === DEFAULT_CHANNEL && method === INITIALIZE) {\n      return this.initialize(args[0], args[1], args[2]);\n    }\n    const requestHandler = channel === DEFAULT_CHANNEL ? this._requestHandler : this._localChannels.get(channel);\n    if (!requestHandler) {\n      return Promise.reject(new Error(`Missing channel ${channel} on worker thread`));\n    }\n    if (typeof requestHandler[method] !== 'function') {\n      return Promise.reject(new Error(`Missing method ${method} on worker thread channel ${channel}`));\n    }\n    try {\n      return Promise.resolve(requestHandler[method].apply(requestHandler, args));\n    } catch (e) {\n      return Promise.reject(e);\n    }\n  }\n  _handleEvent(channel, eventName, arg) {\n    const requestHandler = channel === DEFAULT_CHANNEL ? this._requestHandler : this._localChannels.get(channel);\n    if (!requestHandler) {\n      throw new Error(`Missing channel ${channel} on worker thread`);\n    }\n    if (propertyIsDynamicEvent(eventName)) {\n      const event = requestHandler[eventName].call(requestHandler, arg);\n      if (typeof event !== 'function') {\n        throw new Error(`Missing dynamic event ${eventName} on request handler.`);\n      }\n      return event;\n    }\n    if (propertyIsEvent(eventName)) {\n      const event = requestHandler[eventName];\n      if (typeof event !== 'function') {\n        throw new Error(`Missing event ${eventName} on request handler.`);\n      }\n      return event;\n    }\n    throw new Error(`Malformed event name ${eventName}`);\n  }\n  getChannel(channel) {\n    if (!this._remoteChannels.has(channel)) {\n      const inst = this._protocol.createProxyToRemoteChannel(channel);\n      this._remoteChannels.set(channel, inst);\n    }\n    return this._remoteChannels.get(channel);\n  }\n  initialize(workerId, loaderConfig, moduleId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3._protocol.setWorkerId(workerId);\n      if (_this3._requestHandlerFactory) {\n        // static request handler\n        _this3._requestHandler = _this3._requestHandlerFactory(_this3);\n        return;\n      }\n      if (loaderConfig) {\n        // Remove 'baseUrl', handling it is beyond scope for now\n        if (typeof loaderConfig.baseUrl !== 'undefined') {\n          delete loaderConfig['baseUrl'];\n        }\n        if (typeof loaderConfig.paths !== 'undefined') {\n          if (typeof loaderConfig.paths.vs !== 'undefined') {\n            delete loaderConfig.paths['vs'];\n          }\n        }\n        if (typeof loaderConfig.trustedTypesPolicy !== 'undefined') {\n          // don't use, it has been destroyed during serialize\n          delete loaderConfig['trustedTypesPolicy'];\n        }\n        // Since this is in a web worker, enable catching errors\n        loaderConfig.catchError = true;\n        globalThis.require.config(loaderConfig);\n      }\n      if (isESM) {\n        const url = FileAccess.asBrowserUri(`${moduleId}.js`).toString(true);\n        return import(`${url}`).then(module => {\n          _this3._requestHandler = module.create(_this3);\n          if (!_this3._requestHandler) {\n            throw new Error(`No RequestHandler!`);\n          }\n        });\n      }\n      return new Promise((resolve, reject) => {\n        // Use the global require to be sure to get the global config\n        // ESM-comment-begin\n        // \t\t\tconst req = (globalThis.require || require);\n        // ESM-comment-end\n        // ESM-uncomment-begin\n        const req = globalThis.require;\n        // ESM-uncomment-end\n        req([moduleId], module => {\n          _this3._requestHandler = module.create(_this3);\n          if (!_this3._requestHandler) {\n            reject(new Error(`No RequestHandler!`));\n            return;\n          }\n          resolve();\n        }, reject);\n      });\n    })();\n  }\n}\n/**\n * Defines the worker entry point. Must be exported and named `create`.\n * @skipMangle\n */\nexport function create(postMessage) {\n  return new SimpleWorkerServer(postMessage, null);\n}", "map": {"version": 3, "names": ["onUnexpectedError", "transformErrorForSerialization", "Emitter", "Disposable", "FileAccess", "isWeb", "strings", "isESM", "DEFAULT_CHANNEL", "INITIALIZE", "webWorkerWarningLogged", "logOnceWebWorkerWarning", "err", "console", "warn", "message", "RequestMessage", "constructor", "vsWorker", "req", "channel", "method", "args", "type", "ReplyMessage", "seq", "res", "SubscribeEventMessage", "eventName", "arg", "EventMessage", "event", "UnsubscribeEventMessage", "SimpleWorkerProtocol", "handler", "_workerId", "_handler", "_lastSentReq", "_pendingReplies", "Object", "create", "_pendingEmitters", "Map", "_pendingEvents", "setWorkerId", "workerId", "sendMessage", "String", "Promise", "resolve", "reject", "_send", "listen", "emitter", "onWillAddFirstListener", "set", "onDidRemoveLastListener", "delete", "handleMessage", "_handleMessage", "createProxyToRemoteChannel", "sendMessageBarrier", "_this", "get", "target", "name", "propertyIsDynamicEvent", "propertyIsEvent", "undefined", "charCodeAt", "_asyncToGenerator", "myArgs", "Proxy", "msg", "_handleReplyMessage", "_handleRequestMessage", "_handleSubscribeEventMessage", "_handleEventMessage", "_handleUnsubscribeEventMessage", "replyMessage", "reply", "$isError", "Error", "stack", "requestMessage", "result", "then", "r", "e", "detail", "disposable", "handleEvent", "has", "fire", "dispose", "transfer", "i", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "SimpleWorkerClient", "workerFactory", "workerDescriptor", "_this2", "this", "_localChannels", "_worker", "_register", "amdModuleId", "esmModuleLocation", "label", "_protocol", "postMessage", "_handleEvent", "getId", "loaderConfiguration", "globalRequire", "globalThis", "require", "getConfig", "requirejs", "s", "contexts", "_", "config", "_onModuleLoaded", "JSON", "parse", "stringify", "proxy", "catch", "_onError", "channelName", "apply", "call", "setChannel", "error", "info", "isUpperAsciiLetter", "test", "SimpleWorkerServer", "requestHandlerFactory", "_remoteChannels", "_requestHandlerFactory", "_request<PERSON><PERSON><PERSON>", "onmessage", "initialize", "requestHandler", "getChannel", "inst", "loaderConfig", "moduleId", "_this3", "baseUrl", "paths", "vs", "trustedTypesPolicy", "catchError", "url", "as<PERSON><PERSON><PERSON><PERSON><PERSON>", "toString", "module"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/worker/simpleWorker.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { onUnexpectedError, transformErrorForSerialization } from '../errors.js';\nimport { Emitter } from '../event.js';\nimport { Disposable } from '../lifecycle.js';\nimport { FileAccess } from '../network.js';\nimport { isWeb } from '../platform.js';\nimport * as strings from '../strings.js';\n// ESM-comment-begin\n// const isESM = false;\n// ESM-comment-end\n// ESM-uncomment-begin\nconst isESM = true;\n// ESM-uncomment-end\nconst DEFAULT_CHANNEL = 'default';\nconst INITIALIZE = '$initialize';\nlet webWorkerWarningLogged = false;\nexport function logOnceWebWorkerWarning(err) {\n    if (!isWeb) {\n        // running tests\n        return;\n    }\n    if (!webWorkerWarningLogged) {\n        webWorkerWarningLogged = true;\n        console.warn('Could not create web worker(s). Falling back to loading web worker code in main thread, which might cause UI freezes. Please see https://github.com/microsoft/monaco-editor#faq');\n    }\n    console.warn(err.message);\n}\nclass RequestMessage {\n    constructor(vsWorker, req, channel, method, args) {\n        this.vsWorker = vsWorker;\n        this.req = req;\n        this.channel = channel;\n        this.method = method;\n        this.args = args;\n        this.type = 0 /* MessageType.Request */;\n    }\n}\nclass ReplyMessage {\n    constructor(vsWorker, seq, res, err) {\n        this.vsWorker = vsWorker;\n        this.seq = seq;\n        this.res = res;\n        this.err = err;\n        this.type = 1 /* MessageType.Reply */;\n    }\n}\nclass SubscribeEventMessage {\n    constructor(vsWorker, req, channel, eventName, arg) {\n        this.vsWorker = vsWorker;\n        this.req = req;\n        this.channel = channel;\n        this.eventName = eventName;\n        this.arg = arg;\n        this.type = 2 /* MessageType.SubscribeEvent */;\n    }\n}\nclass EventMessage {\n    constructor(vsWorker, req, event) {\n        this.vsWorker = vsWorker;\n        this.req = req;\n        this.event = event;\n        this.type = 3 /* MessageType.Event */;\n    }\n}\nclass UnsubscribeEventMessage {\n    constructor(vsWorker, req) {\n        this.vsWorker = vsWorker;\n        this.req = req;\n        this.type = 4 /* MessageType.UnsubscribeEvent */;\n    }\n}\nclass SimpleWorkerProtocol {\n    constructor(handler) {\n        this._workerId = -1;\n        this._handler = handler;\n        this._lastSentReq = 0;\n        this._pendingReplies = Object.create(null);\n        this._pendingEmitters = new Map();\n        this._pendingEvents = new Map();\n    }\n    setWorkerId(workerId) {\n        this._workerId = workerId;\n    }\n    sendMessage(channel, method, args) {\n        const req = String(++this._lastSentReq);\n        return new Promise((resolve, reject) => {\n            this._pendingReplies[req] = {\n                resolve: resolve,\n                reject: reject\n            };\n            this._send(new RequestMessage(this._workerId, req, channel, method, args));\n        });\n    }\n    listen(channel, eventName, arg) {\n        let req = null;\n        const emitter = new Emitter({\n            onWillAddFirstListener: () => {\n                req = String(++this._lastSentReq);\n                this._pendingEmitters.set(req, emitter);\n                this._send(new SubscribeEventMessage(this._workerId, req, channel, eventName, arg));\n            },\n            onDidRemoveLastListener: () => {\n                this._pendingEmitters.delete(req);\n                this._send(new UnsubscribeEventMessage(this._workerId, req));\n                req = null;\n            }\n        });\n        return emitter.event;\n    }\n    handleMessage(message) {\n        if (!message || !message.vsWorker) {\n            return;\n        }\n        if (this._workerId !== -1 && message.vsWorker !== this._workerId) {\n            return;\n        }\n        this._handleMessage(message);\n    }\n    createProxyToRemoteChannel(channel, sendMessageBarrier) {\n        const handler = {\n            get: (target, name) => {\n                if (typeof name === 'string' && !target[name]) {\n                    if (propertyIsDynamicEvent(name)) { // onDynamic...\n                        target[name] = (arg) => {\n                            return this.listen(channel, name, arg);\n                        };\n                    }\n                    else if (propertyIsEvent(name)) { // on...\n                        target[name] = this.listen(channel, name, undefined);\n                    }\n                    else if (name.charCodeAt(0) === 36 /* CharCode.DollarSign */) { // $...\n                        target[name] = async (...myArgs) => {\n                            await sendMessageBarrier?.();\n                            return this.sendMessage(channel, name, myArgs);\n                        };\n                    }\n                }\n                return target[name];\n            }\n        };\n        return new Proxy(Object.create(null), handler);\n    }\n    _handleMessage(msg) {\n        switch (msg.type) {\n            case 1 /* MessageType.Reply */:\n                return this._handleReplyMessage(msg);\n            case 0 /* MessageType.Request */:\n                return this._handleRequestMessage(msg);\n            case 2 /* MessageType.SubscribeEvent */:\n                return this._handleSubscribeEventMessage(msg);\n            case 3 /* MessageType.Event */:\n                return this._handleEventMessage(msg);\n            case 4 /* MessageType.UnsubscribeEvent */:\n                return this._handleUnsubscribeEventMessage(msg);\n        }\n    }\n    _handleReplyMessage(replyMessage) {\n        if (!this._pendingReplies[replyMessage.seq]) {\n            console.warn('Got reply to unknown seq');\n            return;\n        }\n        const reply = this._pendingReplies[replyMessage.seq];\n        delete this._pendingReplies[replyMessage.seq];\n        if (replyMessage.err) {\n            let err = replyMessage.err;\n            if (replyMessage.err.$isError) {\n                err = new Error();\n                err.name = replyMessage.err.name;\n                err.message = replyMessage.err.message;\n                err.stack = replyMessage.err.stack;\n            }\n            reply.reject(err);\n            return;\n        }\n        reply.resolve(replyMessage.res);\n    }\n    _handleRequestMessage(requestMessage) {\n        const req = requestMessage.req;\n        const result = this._handler.handleMessage(requestMessage.channel, requestMessage.method, requestMessage.args);\n        result.then((r) => {\n            this._send(new ReplyMessage(this._workerId, req, r, undefined));\n        }, (e) => {\n            if (e.detail instanceof Error) {\n                // Loading errors have a detail property that points to the actual error\n                e.detail = transformErrorForSerialization(e.detail);\n            }\n            this._send(new ReplyMessage(this._workerId, req, undefined, transformErrorForSerialization(e)));\n        });\n    }\n    _handleSubscribeEventMessage(msg) {\n        const req = msg.req;\n        const disposable = this._handler.handleEvent(msg.channel, msg.eventName, msg.arg)((event) => {\n            this._send(new EventMessage(this._workerId, req, event));\n        });\n        this._pendingEvents.set(req, disposable);\n    }\n    _handleEventMessage(msg) {\n        if (!this._pendingEmitters.has(msg.req)) {\n            console.warn('Got event for unknown req');\n            return;\n        }\n        this._pendingEmitters.get(msg.req).fire(msg.event);\n    }\n    _handleUnsubscribeEventMessage(msg) {\n        if (!this._pendingEvents.has(msg.req)) {\n            console.warn('Got unsubscribe for unknown req');\n            return;\n        }\n        this._pendingEvents.get(msg.req).dispose();\n        this._pendingEvents.delete(msg.req);\n    }\n    _send(msg) {\n        const transfer = [];\n        if (msg.type === 0 /* MessageType.Request */) {\n            for (let i = 0; i < msg.args.length; i++) {\n                if (msg.args[i] instanceof ArrayBuffer) {\n                    transfer.push(msg.args[i]);\n                }\n            }\n        }\n        else if (msg.type === 1 /* MessageType.Reply */) {\n            if (msg.res instanceof ArrayBuffer) {\n                transfer.push(msg.res);\n            }\n        }\n        this._handler.sendMessage(msg, transfer);\n    }\n}\n/**\n * Main thread side\n */\nexport class SimpleWorkerClient extends Disposable {\n    constructor(workerFactory, workerDescriptor) {\n        super();\n        this._localChannels = new Map();\n        this._worker = this._register(workerFactory.create({\n            amdModuleId: 'vs/base/common/worker/simpleWorker',\n            esmModuleLocation: workerDescriptor.esmModuleLocation,\n            label: workerDescriptor.label\n        }, (msg) => {\n            this._protocol.handleMessage(msg);\n        }, (err) => {\n            // in Firefox, web workers fail lazily :(\n            // we will reject the proxy\n            onUnexpectedError(err);\n        }));\n        this._protocol = new SimpleWorkerProtocol({\n            sendMessage: (msg, transfer) => {\n                this._worker.postMessage(msg, transfer);\n            },\n            handleMessage: (channel, method, args) => {\n                return this._handleMessage(channel, method, args);\n            },\n            handleEvent: (channel, eventName, arg) => {\n                return this._handleEvent(channel, eventName, arg);\n            }\n        });\n        this._protocol.setWorkerId(this._worker.getId());\n        // Gather loader configuration\n        let loaderConfiguration = null;\n        const globalRequire = globalThis.require;\n        if (typeof globalRequire !== 'undefined' && typeof globalRequire.getConfig === 'function') {\n            // Get the configuration from the Monaco AMD Loader\n            loaderConfiguration = globalRequire.getConfig();\n        }\n        else if (typeof globalThis.requirejs !== 'undefined') {\n            // Get the configuration from requirejs\n            loaderConfiguration = globalThis.requirejs.s.contexts._.config;\n        }\n        // Send initialize message\n        this._onModuleLoaded = this._protocol.sendMessage(DEFAULT_CHANNEL, INITIALIZE, [\n            this._worker.getId(),\n            JSON.parse(JSON.stringify(loaderConfiguration)),\n            workerDescriptor.amdModuleId,\n        ]);\n        this.proxy = this._protocol.createProxyToRemoteChannel(DEFAULT_CHANNEL, async () => { await this._onModuleLoaded; });\n        this._onModuleLoaded.catch((e) => {\n            this._onError('Worker failed to load ' + workerDescriptor.amdModuleId, e);\n        });\n    }\n    _handleMessage(channelName, method, args) {\n        const channel = this._localChannels.get(channelName);\n        if (!channel) {\n            return Promise.reject(new Error(`Missing channel ${channelName} on main thread`));\n        }\n        if (typeof channel[method] !== 'function') {\n            return Promise.reject(new Error(`Missing method ${method} on main thread channel ${channelName}`));\n        }\n        try {\n            return Promise.resolve(channel[method].apply(channel, args));\n        }\n        catch (e) {\n            return Promise.reject(e);\n        }\n    }\n    _handleEvent(channelName, eventName, arg) {\n        const channel = this._localChannels.get(channelName);\n        if (!channel) {\n            throw new Error(`Missing channel ${channelName} on main thread`);\n        }\n        if (propertyIsDynamicEvent(eventName)) {\n            const event = channel[eventName].call(channel, arg);\n            if (typeof event !== 'function') {\n                throw new Error(`Missing dynamic event ${eventName} on main thread channel ${channelName}.`);\n            }\n            return event;\n        }\n        if (propertyIsEvent(eventName)) {\n            const event = channel[eventName];\n            if (typeof event !== 'function') {\n                throw new Error(`Missing event ${eventName} on main thread channel ${channelName}.`);\n            }\n            return event;\n        }\n        throw new Error(`Malformed event name ${eventName}`);\n    }\n    setChannel(channel, handler) {\n        this._localChannels.set(channel, handler);\n    }\n    _onError(message, error) {\n        console.error(message);\n        console.info(error);\n    }\n}\nfunction propertyIsEvent(name) {\n    // Assume a property is an event if it has a form of \"onSomething\"\n    return name[0] === 'o' && name[1] === 'n' && strings.isUpperAsciiLetter(name.charCodeAt(2));\n}\nfunction propertyIsDynamicEvent(name) {\n    // Assume a property is a dynamic event (a method that returns an event) if it has a form of \"onDynamicSomething\"\n    return /^onDynamic/.test(name) && strings.isUpperAsciiLetter(name.charCodeAt(9));\n}\n/**\n * Worker side\n */\nexport class SimpleWorkerServer {\n    constructor(postMessage, requestHandlerFactory) {\n        this._localChannels = new Map();\n        this._remoteChannels = new Map();\n        this._requestHandlerFactory = requestHandlerFactory;\n        this._requestHandler = null;\n        this._protocol = new SimpleWorkerProtocol({\n            sendMessage: (msg, transfer) => {\n                postMessage(msg, transfer);\n            },\n            handleMessage: (channel, method, args) => this._handleMessage(channel, method, args),\n            handleEvent: (channel, eventName, arg) => this._handleEvent(channel, eventName, arg)\n        });\n    }\n    onmessage(msg) {\n        this._protocol.handleMessage(msg);\n    }\n    _handleMessage(channel, method, args) {\n        if (channel === DEFAULT_CHANNEL && method === INITIALIZE) {\n            return this.initialize(args[0], args[1], args[2]);\n        }\n        const requestHandler = (channel === DEFAULT_CHANNEL ? this._requestHandler : this._localChannels.get(channel));\n        if (!requestHandler) {\n            return Promise.reject(new Error(`Missing channel ${channel} on worker thread`));\n        }\n        if (typeof requestHandler[method] !== 'function') {\n            return Promise.reject(new Error(`Missing method ${method} on worker thread channel ${channel}`));\n        }\n        try {\n            return Promise.resolve(requestHandler[method].apply(requestHandler, args));\n        }\n        catch (e) {\n            return Promise.reject(e);\n        }\n    }\n    _handleEvent(channel, eventName, arg) {\n        const requestHandler = (channel === DEFAULT_CHANNEL ? this._requestHandler : this._localChannels.get(channel));\n        if (!requestHandler) {\n            throw new Error(`Missing channel ${channel} on worker thread`);\n        }\n        if (propertyIsDynamicEvent(eventName)) {\n            const event = requestHandler[eventName].call(requestHandler, arg);\n            if (typeof event !== 'function') {\n                throw new Error(`Missing dynamic event ${eventName} on request handler.`);\n            }\n            return event;\n        }\n        if (propertyIsEvent(eventName)) {\n            const event = requestHandler[eventName];\n            if (typeof event !== 'function') {\n                throw new Error(`Missing event ${eventName} on request handler.`);\n            }\n            return event;\n        }\n        throw new Error(`Malformed event name ${eventName}`);\n    }\n    getChannel(channel) {\n        if (!this._remoteChannels.has(channel)) {\n            const inst = this._protocol.createProxyToRemoteChannel(channel);\n            this._remoteChannels.set(channel, inst);\n        }\n        return this._remoteChannels.get(channel);\n    }\n    async initialize(workerId, loaderConfig, moduleId) {\n        this._protocol.setWorkerId(workerId);\n        if (this._requestHandlerFactory) {\n            // static request handler\n            this._requestHandler = this._requestHandlerFactory(this);\n            return;\n        }\n        if (loaderConfig) {\n            // Remove 'baseUrl', handling it is beyond scope for now\n            if (typeof loaderConfig.baseUrl !== 'undefined') {\n                delete loaderConfig['baseUrl'];\n            }\n            if (typeof loaderConfig.paths !== 'undefined') {\n                if (typeof loaderConfig.paths.vs !== 'undefined') {\n                    delete loaderConfig.paths['vs'];\n                }\n            }\n            if (typeof loaderConfig.trustedTypesPolicy !== 'undefined') {\n                // don't use, it has been destroyed during serialize\n                delete loaderConfig['trustedTypesPolicy'];\n            }\n            // Since this is in a web worker, enable catching errors\n            loaderConfig.catchError = true;\n            globalThis.require.config(loaderConfig);\n        }\n        if (isESM) {\n            const url = FileAccess.asBrowserUri(`${moduleId}.js`).toString(true);\n            return import(`${url}`).then((module) => {\n                this._requestHandler = module.create(this);\n                if (!this._requestHandler) {\n                    throw new Error(`No RequestHandler!`);\n                }\n            });\n        }\n        return new Promise((resolve, reject) => {\n            // Use the global require to be sure to get the global config\n            // ESM-comment-begin\n            // \t\t\tconst req = (globalThis.require || require);\n            // ESM-comment-end\n            // ESM-uncomment-begin\n            const req = globalThis.require;\n            // ESM-uncomment-end\n            req([moduleId], (module) => {\n                this._requestHandler = module.create(this);\n                if (!this._requestHandler) {\n                    reject(new Error(`No RequestHandler!`));\n                    return;\n                }\n                resolve();\n            }, reject);\n        });\n    }\n}\n/**\n * Defines the worker entry point. Must be exported and named `create`.\n * @skipMangle\n */\nexport function create(postMessage) {\n    return new SimpleWorkerServer(postMessage, null);\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,EAAEC,8BAA8B,QAAQ,cAAc;AAChF,SAASC,OAAO,QAAQ,aAAa;AACrC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,KAAKC,OAAO,MAAM,eAAe;AACxC;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAG,IAAI;AAClB;AACA,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,UAAU,GAAG,aAAa;AAChC,IAAIC,sBAAsB,GAAG,KAAK;AAClC,OAAO,SAASC,uBAAuBA,CAACC,GAAG,EAAE;EACzC,IAAI,CAACP,KAAK,EAAE;IACR;IACA;EACJ;EACA,IAAI,CAACK,sBAAsB,EAAE;IACzBA,sBAAsB,GAAG,IAAI;IAC7BG,OAAO,CAACC,IAAI,CAAC,iLAAiL,CAAC;EACnM;EACAD,OAAO,CAACC,IAAI,CAACF,GAAG,CAACG,OAAO,CAAC;AAC7B;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAE;IAC9C,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;EAClB;AACJ;AACA,MAAMC,YAAY,CAAC;EACfP,WAAWA,CAACC,QAAQ,EAAEO,GAAG,EAAEC,GAAG,EAAEd,GAAG,EAAE;IACjC,IAAI,CAACM,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACO,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACd,GAAG,GAAGA,GAAG;IACd,IAAI,CAACW,IAAI,GAAG,CAAC,CAAC;EAClB;AACJ;AACA,MAAMI,qBAAqB,CAAC;EACxBV,WAAWA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,OAAO,EAAEQ,SAAS,EAAEC,GAAG,EAAE;IAChD,IAAI,CAACX,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACQ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACN,IAAI,GAAG,CAAC,CAAC;EAClB;AACJ;AACA,MAAMO,YAAY,CAAC;EACfb,WAAWA,CAACC,QAAQ,EAAEC,GAAG,EAAEY,KAAK,EAAE;IAC9B,IAAI,CAACb,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACY,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACR,IAAI,GAAG,CAAC,CAAC;EAClB;AACJ;AACA,MAAMS,uBAAuB,CAAC;EAC1Bf,WAAWA,CAACC,QAAQ,EAAEC,GAAG,EAAE;IACvB,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACI,IAAI,GAAG,CAAC,CAAC;EAClB;AACJ;AACA,MAAMU,oBAAoB,CAAC;EACvBhB,WAAWA,CAACiB,OAAO,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAGF,OAAO;IACvB,IAAI,CAACG,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,cAAc,GAAG,IAAID,GAAG,CAAC,CAAC;EACnC;EACAE,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACV,SAAS,GAAGU,QAAQ;EAC7B;EACAC,WAAWA,CAAC1B,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAE;IAC/B,MAAMH,GAAG,GAAG4B,MAAM,CAAC,EAAE,IAAI,CAACV,YAAY,CAAC;IACvC,OAAO,IAAIW,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,IAAI,CAACZ,eAAe,CAACnB,GAAG,CAAC,GAAG;QACxB8B,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAEA;MACZ,CAAC;MACD,IAAI,CAACC,KAAK,CAAC,IAAInC,cAAc,CAAC,IAAI,CAACmB,SAAS,EAAEhB,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,IAAI,CAAC,CAAC;IAC9E,CAAC,CAAC;EACN;EACA8B,MAAMA,CAAChC,OAAO,EAAEQ,SAAS,EAAEC,GAAG,EAAE;IAC5B,IAAIV,GAAG,GAAG,IAAI;IACd,MAAMkC,OAAO,GAAG,IAAInD,OAAO,CAAC;MACxBoD,sBAAsB,EAAEA,CAAA,KAAM;QAC1BnC,GAAG,GAAG4B,MAAM,CAAC,EAAE,IAAI,CAACV,YAAY,CAAC;QACjC,IAAI,CAACI,gBAAgB,CAACc,GAAG,CAACpC,GAAG,EAAEkC,OAAO,CAAC;QACvC,IAAI,CAACF,KAAK,CAAC,IAAIxB,qBAAqB,CAAC,IAAI,CAACQ,SAAS,EAAEhB,GAAG,EAAEC,OAAO,EAAEQ,SAAS,EAAEC,GAAG,CAAC,CAAC;MACvF,CAAC;MACD2B,uBAAuB,EAAEA,CAAA,KAAM;QAC3B,IAAI,CAACf,gBAAgB,CAACgB,MAAM,CAACtC,GAAG,CAAC;QACjC,IAAI,CAACgC,KAAK,CAAC,IAAInB,uBAAuB,CAAC,IAAI,CAACG,SAAS,EAAEhB,GAAG,CAAC,CAAC;QAC5DA,GAAG,GAAG,IAAI;MACd;IACJ,CAAC,CAAC;IACF,OAAOkC,OAAO,CAACtB,KAAK;EACxB;EACA2B,aAAaA,CAAC3C,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACG,QAAQ,EAAE;MAC/B;IACJ;IACA,IAAI,IAAI,CAACiB,SAAS,KAAK,CAAC,CAAC,IAAIpB,OAAO,CAACG,QAAQ,KAAK,IAAI,CAACiB,SAAS,EAAE;MAC9D;IACJ;IACA,IAAI,CAACwB,cAAc,CAAC5C,OAAO,CAAC;EAChC;EACA6C,0BAA0BA,CAACxC,OAAO,EAAEyC,kBAAkB,EAAE;IAAA,IAAAC,KAAA;IACpD,MAAM5B,OAAO,GAAG;MACZ6B,GAAG,EAAEA,CAACC,MAAM,EAAEC,IAAI,KAAK;QACnB,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACD,MAAM,CAACC,IAAI,CAAC,EAAE;UAC3C,IAAIC,sBAAsB,CAACD,IAAI,CAAC,EAAE;YAAE;YAChCD,MAAM,CAACC,IAAI,CAAC,GAAIpC,GAAG,IAAK;cACpB,OAAO,IAAI,CAACuB,MAAM,CAAChC,OAAO,EAAE6C,IAAI,EAAEpC,GAAG,CAAC;YAC1C,CAAC;UACL,CAAC,MACI,IAAIsC,eAAe,CAACF,IAAI,CAAC,EAAE;YAAE;YAC9BD,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACb,MAAM,CAAChC,OAAO,EAAE6C,IAAI,EAAEG,SAAS,CAAC;UACxD,CAAC,MACI,IAAIH,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,2BAA2B;YAAE;YAC5DL,MAAM,CAACC,IAAI,CAAC,gBAAAK,iBAAA,CAAG,WAAO,GAAGC,MAAM,EAAK;cAChC,MAAMV,kBAAkB,GAAG,CAAC;cAC5B,OAAOC,KAAI,CAAChB,WAAW,CAAC1B,OAAO,EAAE6C,IAAI,EAAEM,MAAM,CAAC;YAClD,CAAC;UACL;QACJ;QACA,OAAOP,MAAM,CAACC,IAAI,CAAC;MACvB;IACJ,CAAC;IACD,OAAO,IAAIO,KAAK,CAACjC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,EAAEN,OAAO,CAAC;EAClD;EACAyB,cAAcA,CAACc,GAAG,EAAE;IAChB,QAAQA,GAAG,CAAClD,IAAI;MACZ,KAAK,CAAC,CAAC;QACH,OAAO,IAAI,CAACmD,mBAAmB,CAACD,GAAG,CAAC;MACxC,KAAK,CAAC,CAAC;QACH,OAAO,IAAI,CAACE,qBAAqB,CAACF,GAAG,CAAC;MAC1C,KAAK,CAAC,CAAC;QACH,OAAO,IAAI,CAACG,4BAA4B,CAACH,GAAG,CAAC;MACjD,KAAK,CAAC,CAAC;QACH,OAAO,IAAI,CAACI,mBAAmB,CAACJ,GAAG,CAAC;MACxC,KAAK,CAAC,CAAC;QACH,OAAO,IAAI,CAACK,8BAA8B,CAACL,GAAG,CAAC;IACvD;EACJ;EACAC,mBAAmBA,CAACK,YAAY,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACzC,eAAe,CAACyC,YAAY,CAACtD,GAAG,CAAC,EAAE;MACzCZ,OAAO,CAACC,IAAI,CAAC,0BAA0B,CAAC;MACxC;IACJ;IACA,MAAMkE,KAAK,GAAG,IAAI,CAAC1C,eAAe,CAACyC,YAAY,CAACtD,GAAG,CAAC;IACpD,OAAO,IAAI,CAACa,eAAe,CAACyC,YAAY,CAACtD,GAAG,CAAC;IAC7C,IAAIsD,YAAY,CAACnE,GAAG,EAAE;MAClB,IAAIA,GAAG,GAAGmE,YAAY,CAACnE,GAAG;MAC1B,IAAImE,YAAY,CAACnE,GAAG,CAACqE,QAAQ,EAAE;QAC3BrE,GAAG,GAAG,IAAIsE,KAAK,CAAC,CAAC;QACjBtE,GAAG,CAACqD,IAAI,GAAGc,YAAY,CAACnE,GAAG,CAACqD,IAAI;QAChCrD,GAAG,CAACG,OAAO,GAAGgE,YAAY,CAACnE,GAAG,CAACG,OAAO;QACtCH,GAAG,CAACuE,KAAK,GAAGJ,YAAY,CAACnE,GAAG,CAACuE,KAAK;MACtC;MACAH,KAAK,CAAC9B,MAAM,CAACtC,GAAG,CAAC;MACjB;IACJ;IACAoE,KAAK,CAAC/B,OAAO,CAAC8B,YAAY,CAACrD,GAAG,CAAC;EACnC;EACAiD,qBAAqBA,CAACS,cAAc,EAAE;IAClC,MAAMjE,GAAG,GAAGiE,cAAc,CAACjE,GAAG;IAC9B,MAAMkE,MAAM,GAAG,IAAI,CAACjD,QAAQ,CAACsB,aAAa,CAAC0B,cAAc,CAAChE,OAAO,EAAEgE,cAAc,CAAC/D,MAAM,EAAE+D,cAAc,CAAC9D,IAAI,CAAC;IAC9G+D,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAK;MACf,IAAI,CAACpC,KAAK,CAAC,IAAI3B,YAAY,CAAC,IAAI,CAACW,SAAS,EAAEhB,GAAG,EAAEoE,CAAC,EAAEnB,SAAS,CAAC,CAAC;IACnE,CAAC,EAAGoB,CAAC,IAAK;MACN,IAAIA,CAAC,CAACC,MAAM,YAAYP,KAAK,EAAE;QAC3B;QACAM,CAAC,CAACC,MAAM,GAAGxF,8BAA8B,CAACuF,CAAC,CAACC,MAAM,CAAC;MACvD;MACA,IAAI,CAACtC,KAAK,CAAC,IAAI3B,YAAY,CAAC,IAAI,CAACW,SAAS,EAAEhB,GAAG,EAAEiD,SAAS,EAAEnE,8BAA8B,CAACuF,CAAC,CAAC,CAAC,CAAC;IACnG,CAAC,CAAC;EACN;EACAZ,4BAA4BA,CAACH,GAAG,EAAE;IAC9B,MAAMtD,GAAG,GAAGsD,GAAG,CAACtD,GAAG;IACnB,MAAMuE,UAAU,GAAG,IAAI,CAACtD,QAAQ,CAACuD,WAAW,CAAClB,GAAG,CAACrD,OAAO,EAAEqD,GAAG,CAAC7C,SAAS,EAAE6C,GAAG,CAAC5C,GAAG,CAAC,CAAEE,KAAK,IAAK;MACzF,IAAI,CAACoB,KAAK,CAAC,IAAIrB,YAAY,CAAC,IAAI,CAACK,SAAS,EAAEhB,GAAG,EAAEY,KAAK,CAAC,CAAC;IAC5D,CAAC,CAAC;IACF,IAAI,CAACY,cAAc,CAACY,GAAG,CAACpC,GAAG,EAAEuE,UAAU,CAAC;EAC5C;EACAb,mBAAmBA,CAACJ,GAAG,EAAE;IACrB,IAAI,CAAC,IAAI,CAAChC,gBAAgB,CAACmD,GAAG,CAACnB,GAAG,CAACtD,GAAG,CAAC,EAAE;MACrCN,OAAO,CAACC,IAAI,CAAC,2BAA2B,CAAC;MACzC;IACJ;IACA,IAAI,CAAC2B,gBAAgB,CAACsB,GAAG,CAACU,GAAG,CAACtD,GAAG,CAAC,CAAC0E,IAAI,CAACpB,GAAG,CAAC1C,KAAK,CAAC;EACtD;EACA+C,8BAA8BA,CAACL,GAAG,EAAE;IAChC,IAAI,CAAC,IAAI,CAAC9B,cAAc,CAACiD,GAAG,CAACnB,GAAG,CAACtD,GAAG,CAAC,EAAE;MACnCN,OAAO,CAACC,IAAI,CAAC,iCAAiC,CAAC;MAC/C;IACJ;IACA,IAAI,CAAC6B,cAAc,CAACoB,GAAG,CAACU,GAAG,CAACtD,GAAG,CAAC,CAAC2E,OAAO,CAAC,CAAC;IAC1C,IAAI,CAACnD,cAAc,CAACc,MAAM,CAACgB,GAAG,CAACtD,GAAG,CAAC;EACvC;EACAgC,KAAKA,CAACsB,GAAG,EAAE;IACP,MAAMsB,QAAQ,GAAG,EAAE;IACnB,IAAItB,GAAG,CAAClD,IAAI,KAAK,CAAC,CAAC,2BAA2B;MAC1C,KAAK,IAAIyE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,GAAG,CAACnD,IAAI,CAAC2E,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIvB,GAAG,CAACnD,IAAI,CAAC0E,CAAC,CAAC,YAAYE,WAAW,EAAE;UACpCH,QAAQ,CAACI,IAAI,CAAC1B,GAAG,CAACnD,IAAI,CAAC0E,CAAC,CAAC,CAAC;QAC9B;MACJ;IACJ,CAAC,MACI,IAAIvB,GAAG,CAAClD,IAAI,KAAK,CAAC,CAAC,yBAAyB;MAC7C,IAAIkD,GAAG,CAAC/C,GAAG,YAAYwE,WAAW,EAAE;QAChCH,QAAQ,CAACI,IAAI,CAAC1B,GAAG,CAAC/C,GAAG,CAAC;MAC1B;IACJ;IACA,IAAI,CAACU,QAAQ,CAACU,WAAW,CAAC2B,GAAG,EAAEsB,QAAQ,CAAC;EAC5C;AACJ;AACA;AACA;AACA;AACA,OAAO,MAAMK,kBAAkB,SAASjG,UAAU,CAAC;EAC/Cc,WAAWA,CAACoF,aAAa,EAAEC,gBAAgB,EAAE;IAAA,IAAAC,MAAA;IACzC,KAAK,CAAC,CAAC;IAAAA,MAAA,GAAAC,IAAA;IACP,IAAI,CAACC,cAAc,GAAG,IAAI/D,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACgE,OAAO,GAAG,IAAI,CAACC,SAAS,CAACN,aAAa,CAAC7D,MAAM,CAAC;MAC/CoE,WAAW,EAAE,oCAAoC;MACjDC,iBAAiB,EAAEP,gBAAgB,CAACO,iBAAiB;MACrDC,KAAK,EAAER,gBAAgB,CAACQ;IAC5B,CAAC,EAAGrC,GAAG,IAAK;MACR,IAAI,CAACsC,SAAS,CAACrD,aAAa,CAACe,GAAG,CAAC;IACrC,CAAC,EAAG7D,GAAG,IAAK;MACR;MACA;MACAZ,iBAAiB,CAACY,GAAG,CAAC;IAC1B,CAAC,CAAC,CAAC;IACH,IAAI,CAACmG,SAAS,GAAG,IAAI9E,oBAAoB,CAAC;MACtCa,WAAW,EAAEA,CAAC2B,GAAG,EAAEsB,QAAQ,KAAK;QAC5B,IAAI,CAACW,OAAO,CAACM,WAAW,CAACvC,GAAG,EAAEsB,QAAQ,CAAC;MAC3C,CAAC;MACDrC,aAAa,EAAEA,CAACtC,OAAO,EAAEC,MAAM,EAAEC,IAAI,KAAK;QACtC,OAAO,IAAI,CAACqC,cAAc,CAACvC,OAAO,EAAEC,MAAM,EAAEC,IAAI,CAAC;MACrD,CAAC;MACDqE,WAAW,EAAEA,CAACvE,OAAO,EAAEQ,SAAS,EAAEC,GAAG,KAAK;QACtC,OAAO,IAAI,CAACoF,YAAY,CAAC7F,OAAO,EAAEQ,SAAS,EAAEC,GAAG,CAAC;MACrD;IACJ,CAAC,CAAC;IACF,IAAI,CAACkF,SAAS,CAACnE,WAAW,CAAC,IAAI,CAAC8D,OAAO,CAACQ,KAAK,CAAC,CAAC,CAAC;IAChD;IACA,IAAIC,mBAAmB,GAAG,IAAI;IAC9B,MAAMC,aAAa,GAAGC,UAAU,CAACC,OAAO;IACxC,IAAI,OAAOF,aAAa,KAAK,WAAW,IAAI,OAAOA,aAAa,CAACG,SAAS,KAAK,UAAU,EAAE;MACvF;MACAJ,mBAAmB,GAAGC,aAAa,CAACG,SAAS,CAAC,CAAC;IACnD,CAAC,MACI,IAAI,OAAOF,UAAU,CAACG,SAAS,KAAK,WAAW,EAAE;MAClD;MACAL,mBAAmB,GAAGE,UAAU,CAACG,SAAS,CAACC,CAAC,CAACC,QAAQ,CAACC,CAAC,CAACC,MAAM;IAClE;IACA;IACA,IAAI,CAACC,eAAe,GAAG,IAAI,CAACd,SAAS,CAACjE,WAAW,CAACtC,eAAe,EAAEC,UAAU,EAAE,CAC3E,IAAI,CAACiG,OAAO,CAACQ,KAAK,CAAC,CAAC,EACpBY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACb,mBAAmB,CAAC,CAAC,EAC/Cb,gBAAgB,CAACM,WAAW,CAC/B,CAAC;IACF,IAAI,CAACqB,KAAK,GAAG,IAAI,CAAClB,SAAS,CAACnD,0BAA0B,CAACpD,eAAe,eAAA8D,iBAAA,CAAE,aAAY;MAAE,MAAMiC,MAAI,CAACsB,eAAe;IAAE,CAAC,EAAC;IACpH,IAAI,CAACA,eAAe,CAACK,KAAK,CAAE1C,CAAC,IAAK;MAC9B,IAAI,CAAC2C,QAAQ,CAAC,wBAAwB,GAAG7B,gBAAgB,CAACM,WAAW,EAAEpB,CAAC,CAAC;IAC7E,CAAC,CAAC;EACN;EACA7B,cAAcA,CAACyE,WAAW,EAAE/G,MAAM,EAAEC,IAAI,EAAE;IACtC,MAAMF,OAAO,GAAG,IAAI,CAACqF,cAAc,CAAC1C,GAAG,CAACqE,WAAW,CAAC;IACpD,IAAI,CAAChH,OAAO,EAAE;MACV,OAAO4B,OAAO,CAACE,MAAM,CAAC,IAAIgC,KAAK,CAAC,mBAAmBkD,WAAW,iBAAiB,CAAC,CAAC;IACrF;IACA,IAAI,OAAOhH,OAAO,CAACC,MAAM,CAAC,KAAK,UAAU,EAAE;MACvC,OAAO2B,OAAO,CAACE,MAAM,CAAC,IAAIgC,KAAK,CAAC,kBAAkB7D,MAAM,2BAA2B+G,WAAW,EAAE,CAAC,CAAC;IACtG;IACA,IAAI;MACA,OAAOpF,OAAO,CAACC,OAAO,CAAC7B,OAAO,CAACC,MAAM,CAAC,CAACgH,KAAK,CAACjH,OAAO,EAAEE,IAAI,CAAC,CAAC;IAChE,CAAC,CACD,OAAOkE,CAAC,EAAE;MACN,OAAOxC,OAAO,CAACE,MAAM,CAACsC,CAAC,CAAC;IAC5B;EACJ;EACAyB,YAAYA,CAACmB,WAAW,EAAExG,SAAS,EAAEC,GAAG,EAAE;IACtC,MAAMT,OAAO,GAAG,IAAI,CAACqF,cAAc,CAAC1C,GAAG,CAACqE,WAAW,CAAC;IACpD,IAAI,CAAChH,OAAO,EAAE;MACV,MAAM,IAAI8D,KAAK,CAAC,mBAAmBkD,WAAW,iBAAiB,CAAC;IACpE;IACA,IAAIlE,sBAAsB,CAACtC,SAAS,CAAC,EAAE;MACnC,MAAMG,KAAK,GAAGX,OAAO,CAACQ,SAAS,CAAC,CAAC0G,IAAI,CAAClH,OAAO,EAAES,GAAG,CAAC;MACnD,IAAI,OAAOE,KAAK,KAAK,UAAU,EAAE;QAC7B,MAAM,IAAImD,KAAK,CAAC,yBAAyBtD,SAAS,2BAA2BwG,WAAW,GAAG,CAAC;MAChG;MACA,OAAOrG,KAAK;IAChB;IACA,IAAIoC,eAAe,CAACvC,SAAS,CAAC,EAAE;MAC5B,MAAMG,KAAK,GAAGX,OAAO,CAACQ,SAAS,CAAC;MAChC,IAAI,OAAOG,KAAK,KAAK,UAAU,EAAE;QAC7B,MAAM,IAAImD,KAAK,CAAC,iBAAiBtD,SAAS,2BAA2BwG,WAAW,GAAG,CAAC;MACxF;MACA,OAAOrG,KAAK;IAChB;IACA,MAAM,IAAImD,KAAK,CAAC,wBAAwBtD,SAAS,EAAE,CAAC;EACxD;EACA2G,UAAUA,CAACnH,OAAO,EAAEc,OAAO,EAAE;IACzB,IAAI,CAACuE,cAAc,CAAClD,GAAG,CAACnC,OAAO,EAAEc,OAAO,CAAC;EAC7C;EACAiG,QAAQA,CAACpH,OAAO,EAAEyH,KAAK,EAAE;IACrB3H,OAAO,CAAC2H,KAAK,CAACzH,OAAO,CAAC;IACtBF,OAAO,CAAC4H,IAAI,CAACD,KAAK,CAAC;EACvB;AACJ;AACA,SAASrE,eAAeA,CAACF,IAAI,EAAE;EAC3B;EACA,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI3D,OAAO,CAACoI,kBAAkB,CAACzE,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/F;AACA,SAASH,sBAAsBA,CAACD,IAAI,EAAE;EAClC;EACA,OAAO,YAAY,CAAC0E,IAAI,CAAC1E,IAAI,CAAC,IAAI3D,OAAO,CAACoI,kBAAkB,CAACzE,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC;AACpF;AACA;AACA;AACA;AACA,OAAO,MAAMuE,kBAAkB,CAAC;EAC5B3H,WAAWA,CAAC+F,WAAW,EAAE6B,qBAAqB,EAAE;IAC5C,IAAI,CAACpC,cAAc,GAAG,IAAI/D,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACoG,eAAe,GAAG,IAAIpG,GAAG,CAAC,CAAC;IAChC,IAAI,CAACqG,sBAAsB,GAAGF,qBAAqB;IACnD,IAAI,CAACG,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACjC,SAAS,GAAG,IAAI9E,oBAAoB,CAAC;MACtCa,WAAW,EAAEA,CAAC2B,GAAG,EAAEsB,QAAQ,KAAK;QAC5BiB,WAAW,CAACvC,GAAG,EAAEsB,QAAQ,CAAC;MAC9B,CAAC;MACDrC,aAAa,EAAEA,CAACtC,OAAO,EAAEC,MAAM,EAAEC,IAAI,KAAK,IAAI,CAACqC,cAAc,CAACvC,OAAO,EAAEC,MAAM,EAAEC,IAAI,CAAC;MACpFqE,WAAW,EAAEA,CAACvE,OAAO,EAAEQ,SAAS,EAAEC,GAAG,KAAK,IAAI,CAACoF,YAAY,CAAC7F,OAAO,EAAEQ,SAAS,EAAEC,GAAG;IACvF,CAAC,CAAC;EACN;EACAoH,SAASA,CAACxE,GAAG,EAAE;IACX,IAAI,CAACsC,SAAS,CAACrD,aAAa,CAACe,GAAG,CAAC;EACrC;EACAd,cAAcA,CAACvC,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAE;IAClC,IAAIF,OAAO,KAAKZ,eAAe,IAAIa,MAAM,KAAKZ,UAAU,EAAE;MACtD,OAAO,IAAI,CAACyI,UAAU,CAAC5H,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IACrD;IACA,MAAM6H,cAAc,GAAI/H,OAAO,KAAKZ,eAAe,GAAG,IAAI,CAACwI,eAAe,GAAG,IAAI,CAACvC,cAAc,CAAC1C,GAAG,CAAC3C,OAAO,CAAE;IAC9G,IAAI,CAAC+H,cAAc,EAAE;MACjB,OAAOnG,OAAO,CAACE,MAAM,CAAC,IAAIgC,KAAK,CAAC,mBAAmB9D,OAAO,mBAAmB,CAAC,CAAC;IACnF;IACA,IAAI,OAAO+H,cAAc,CAAC9H,MAAM,CAAC,KAAK,UAAU,EAAE;MAC9C,OAAO2B,OAAO,CAACE,MAAM,CAAC,IAAIgC,KAAK,CAAC,kBAAkB7D,MAAM,6BAA6BD,OAAO,EAAE,CAAC,CAAC;IACpG;IACA,IAAI;MACA,OAAO4B,OAAO,CAACC,OAAO,CAACkG,cAAc,CAAC9H,MAAM,CAAC,CAACgH,KAAK,CAACc,cAAc,EAAE7H,IAAI,CAAC,CAAC;IAC9E,CAAC,CACD,OAAOkE,CAAC,EAAE;MACN,OAAOxC,OAAO,CAACE,MAAM,CAACsC,CAAC,CAAC;IAC5B;EACJ;EACAyB,YAAYA,CAAC7F,OAAO,EAAEQ,SAAS,EAAEC,GAAG,EAAE;IAClC,MAAMsH,cAAc,GAAI/H,OAAO,KAAKZ,eAAe,GAAG,IAAI,CAACwI,eAAe,GAAG,IAAI,CAACvC,cAAc,CAAC1C,GAAG,CAAC3C,OAAO,CAAE;IAC9G,IAAI,CAAC+H,cAAc,EAAE;MACjB,MAAM,IAAIjE,KAAK,CAAC,mBAAmB9D,OAAO,mBAAmB,CAAC;IAClE;IACA,IAAI8C,sBAAsB,CAACtC,SAAS,CAAC,EAAE;MACnC,MAAMG,KAAK,GAAGoH,cAAc,CAACvH,SAAS,CAAC,CAAC0G,IAAI,CAACa,cAAc,EAAEtH,GAAG,CAAC;MACjE,IAAI,OAAOE,KAAK,KAAK,UAAU,EAAE;QAC7B,MAAM,IAAImD,KAAK,CAAC,yBAAyBtD,SAAS,sBAAsB,CAAC;MAC7E;MACA,OAAOG,KAAK;IAChB;IACA,IAAIoC,eAAe,CAACvC,SAAS,CAAC,EAAE;MAC5B,MAAMG,KAAK,GAAGoH,cAAc,CAACvH,SAAS,CAAC;MACvC,IAAI,OAAOG,KAAK,KAAK,UAAU,EAAE;QAC7B,MAAM,IAAImD,KAAK,CAAC,iBAAiBtD,SAAS,sBAAsB,CAAC;MACrE;MACA,OAAOG,KAAK;IAChB;IACA,MAAM,IAAImD,KAAK,CAAC,wBAAwBtD,SAAS,EAAE,CAAC;EACxD;EACAwH,UAAUA,CAAChI,OAAO,EAAE;IAChB,IAAI,CAAC,IAAI,CAAC0H,eAAe,CAAClD,GAAG,CAACxE,OAAO,CAAC,EAAE;MACpC,MAAMiI,IAAI,GAAG,IAAI,CAACtC,SAAS,CAACnD,0BAA0B,CAACxC,OAAO,CAAC;MAC/D,IAAI,CAAC0H,eAAe,CAACvF,GAAG,CAACnC,OAAO,EAAEiI,IAAI,CAAC;IAC3C;IACA,OAAO,IAAI,CAACP,eAAe,CAAC/E,GAAG,CAAC3C,OAAO,CAAC;EAC5C;EACM8H,UAAUA,CAACrG,QAAQ,EAAEyG,YAAY,EAAEC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAlF,iBAAA;MAC/CkF,MAAI,CAACzC,SAAS,CAACnE,WAAW,CAACC,QAAQ,CAAC;MACpC,IAAI2G,MAAI,CAACT,sBAAsB,EAAE;QAC7B;QACAS,MAAI,CAACR,eAAe,GAAGQ,MAAI,CAACT,sBAAsB,CAACS,MAAI,CAAC;QACxD;MACJ;MACA,IAAIF,YAAY,EAAE;QACd;QACA,IAAI,OAAOA,YAAY,CAACG,OAAO,KAAK,WAAW,EAAE;UAC7C,OAAOH,YAAY,CAAC,SAAS,CAAC;QAClC;QACA,IAAI,OAAOA,YAAY,CAACI,KAAK,KAAK,WAAW,EAAE;UAC3C,IAAI,OAAOJ,YAAY,CAACI,KAAK,CAACC,EAAE,KAAK,WAAW,EAAE;YAC9C,OAAOL,YAAY,CAACI,KAAK,CAAC,IAAI,CAAC;UACnC;QACJ;QACA,IAAI,OAAOJ,YAAY,CAACM,kBAAkB,KAAK,WAAW,EAAE;UACxD;UACA,OAAON,YAAY,CAAC,oBAAoB,CAAC;QAC7C;QACA;QACAA,YAAY,CAACO,UAAU,GAAG,IAAI;QAC9BxC,UAAU,CAACC,OAAO,CAACM,MAAM,CAAC0B,YAAY,CAAC;MAC3C;MACA,IAAI/I,KAAK,EAAE;QACP,MAAMuJ,GAAG,GAAG1J,UAAU,CAAC2J,YAAY,CAAC,GAAGR,QAAQ,KAAK,CAAC,CAACS,QAAQ,CAAC,IAAI,CAAC;QACpE,OAAO,MAAM,CAAC,GAAGF,GAAG,EAAE,CAAC,CAACxE,IAAI,CAAE2E,MAAM,IAAK;UACrCT,MAAI,CAACR,eAAe,GAAGiB,MAAM,CAACzH,MAAM,CAACgH,MAAI,CAAC;UAC1C,IAAI,CAACA,MAAI,CAACR,eAAe,EAAE;YACvB,MAAM,IAAI9D,KAAK,CAAC,oBAAoB,CAAC;UACzC;QACJ,CAAC,CAAC;MACN;MACA,OAAO,IAAIlC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpC;QACA;QACA;QACA;QACA;QACA,MAAM/B,GAAG,GAAGkG,UAAU,CAACC,OAAO;QAC9B;QACAnG,GAAG,CAAC,CAACoI,QAAQ,CAAC,EAAGU,MAAM,IAAK;UACxBT,MAAI,CAACR,eAAe,GAAGiB,MAAM,CAACzH,MAAM,CAACgH,MAAI,CAAC;UAC1C,IAAI,CAACA,MAAI,CAACR,eAAe,EAAE;YACvB9F,MAAM,CAAC,IAAIgC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACvC;UACJ;UACAjC,OAAO,CAAC,CAAC;QACb,CAAC,EAAEC,MAAM,CAAC;MACd,CAAC,CAAC;IAAC;EACP;AACJ;AACA;AACA;AACA;AACA;AACA,OAAO,SAASV,MAAMA,CAACwE,WAAW,EAAE;EAChC,OAAO,IAAI4B,kBAAkB,CAAC5B,WAAW,EAAE,IAAI,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}