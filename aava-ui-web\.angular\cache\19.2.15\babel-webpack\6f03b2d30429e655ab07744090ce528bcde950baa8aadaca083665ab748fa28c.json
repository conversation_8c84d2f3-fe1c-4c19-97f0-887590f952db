{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { findLastIdxMonotonous, findLastMonotonous, findFirstMonotonous } from '../../../../base/common/arraysFind.js';\nimport { OffsetRange } from '../../core/offsetRange.js';\nimport { Position } from '../../core/position.js';\nimport { Range } from '../../core/range.js';\nimport { isSpace } from './utils.js';\nexport class LinesSliceCharSequence {\n  constructor(lines, range, considerWhitespaceChanges) {\n    this.lines = lines;\n    this.range = range;\n    this.considerWhitespaceChanges = considerWhitespaceChanges;\n    this.elements = [];\n    this.firstElementOffsetByLineIdx = [];\n    this.lineStartOffsets = [];\n    this.trimmedWsLengthsByLineIdx = [];\n    this.firstElementOffsetByLineIdx.push(0);\n    for (let lineNumber = this.range.startLineNumber; lineNumber <= this.range.endLineNumber; lineNumber++) {\n      let line = lines[lineNumber - 1];\n      let lineStartOffset = 0;\n      if (lineNumber === this.range.startLineNumber && this.range.startColumn > 1) {\n        lineStartOffset = this.range.startColumn - 1;\n        line = line.substring(lineStartOffset);\n      }\n      this.lineStartOffsets.push(lineStartOffset);\n      let trimmedWsLength = 0;\n      if (!considerWhitespaceChanges) {\n        const trimmedStartLine = line.trimStart();\n        trimmedWsLength = line.length - trimmedStartLine.length;\n        line = trimmedStartLine.trimEnd();\n      }\n      this.trimmedWsLengthsByLineIdx.push(trimmedWsLength);\n      const lineLength = lineNumber === this.range.endLineNumber ? Math.min(this.range.endColumn - 1 - lineStartOffset - trimmedWsLength, line.length) : line.length;\n      for (let i = 0; i < lineLength; i++) {\n        this.elements.push(line.charCodeAt(i));\n      }\n      if (lineNumber < this.range.endLineNumber) {\n        this.elements.push('\\n'.charCodeAt(0));\n        this.firstElementOffsetByLineIdx.push(this.elements.length);\n      }\n    }\n  }\n  toString() {\n    return `Slice: \"${this.text}\"`;\n  }\n  get text() {\n    return this.getText(new OffsetRange(0, this.length));\n  }\n  getText(range) {\n    return this.elements.slice(range.start, range.endExclusive).map(e => String.fromCharCode(e)).join('');\n  }\n  getElement(offset) {\n    return this.elements[offset];\n  }\n  get length() {\n    return this.elements.length;\n  }\n  getBoundaryScore(length) {\n    //   a   b   c   ,           d   e   f\n    // 11  0   0   12  15  6   13  0   0   11\n    const prevCategory = getCategory(length > 0 ? this.elements[length - 1] : -1);\n    const nextCategory = getCategory(length < this.elements.length ? this.elements[length] : -1);\n    if (prevCategory === 7 /* CharBoundaryCategory.LineBreakCR */ && nextCategory === 8 /* CharBoundaryCategory.LineBreakLF */) {\n      // don't break between \\r and \\n\n      return 0;\n    }\n    if (prevCategory === 8 /* CharBoundaryCategory.LineBreakLF */) {\n      // prefer the linebreak before the change\n      return 150;\n    }\n    let score = 0;\n    if (prevCategory !== nextCategory) {\n      score += 10;\n      if (prevCategory === 0 /* CharBoundaryCategory.WordLower */ && nextCategory === 1 /* CharBoundaryCategory.WordUpper */) {\n        score += 1;\n      }\n    }\n    score += getCategoryBoundaryScore(prevCategory);\n    score += getCategoryBoundaryScore(nextCategory);\n    return score;\n  }\n  translateOffset(offset, preference = 'right') {\n    // find smallest i, so that lineBreakOffsets[i] <= offset using binary search\n    const i = findLastIdxMonotonous(this.firstElementOffsetByLineIdx, value => value <= offset);\n    const lineOffset = offset - this.firstElementOffsetByLineIdx[i];\n    return new Position(this.range.startLineNumber + i, 1 + this.lineStartOffsets[i] + lineOffset + (lineOffset === 0 && preference === 'left' ? 0 : this.trimmedWsLengthsByLineIdx[i]));\n  }\n  translateRange(range) {\n    const pos1 = this.translateOffset(range.start, 'right');\n    const pos2 = this.translateOffset(range.endExclusive, 'left');\n    if (pos2.isBefore(pos1)) {\n      return Range.fromPositions(pos2, pos2);\n    }\n    return Range.fromPositions(pos1, pos2);\n  }\n  /**\n   * Finds the word that contains the character at the given offset\n   */\n  findWordContaining(offset) {\n    if (offset < 0 || offset >= this.elements.length) {\n      return undefined;\n    }\n    if (!isWordChar(this.elements[offset])) {\n      return undefined;\n    }\n    // find start\n    let start = offset;\n    while (start > 0 && isWordChar(this.elements[start - 1])) {\n      start--;\n    }\n    // find end\n    let end = offset;\n    while (end < this.elements.length && isWordChar(this.elements[end])) {\n      end++;\n    }\n    return new OffsetRange(start, end);\n  }\n  countLinesIn(range) {\n    return this.translateOffset(range.endExclusive).lineNumber - this.translateOffset(range.start).lineNumber;\n  }\n  isStronglyEqual(offset1, offset2) {\n    return this.elements[offset1] === this.elements[offset2];\n  }\n  extendToFullLines(range) {\n    const start = findLastMonotonous(this.firstElementOffsetByLineIdx, x => x <= range.start) ?? 0;\n    const end = findFirstMonotonous(this.firstElementOffsetByLineIdx, x => range.endExclusive <= x) ?? this.elements.length;\n    return new OffsetRange(start, end);\n  }\n}\nfunction isWordChar(charCode) {\n  return charCode >= 97 /* CharCode.a */ && charCode <= 122 /* CharCode.z */ || charCode >= 65 /* CharCode.A */ && charCode <= 90 /* CharCode.Z */ || charCode >= 48 /* CharCode.Digit0 */ && charCode <= 57 /* CharCode.Digit9 */;\n}\nconst score = {\n  [0 /* CharBoundaryCategory.WordLower */]: 0,\n  [1 /* CharBoundaryCategory.WordUpper */]: 0,\n  [2 /* CharBoundaryCategory.WordNumber */]: 0,\n  [3 /* CharBoundaryCategory.End */]: 10,\n  [4 /* CharBoundaryCategory.Other */]: 2,\n  [5 /* CharBoundaryCategory.Separator */]: 30,\n  [6 /* CharBoundaryCategory.Space */]: 3,\n  [7 /* CharBoundaryCategory.LineBreakCR */]: 10,\n  [8 /* CharBoundaryCategory.LineBreakLF */]: 10\n};\nfunction getCategoryBoundaryScore(category) {\n  return score[category];\n}\nfunction getCategory(charCode) {\n  if (charCode === 10 /* CharCode.LineFeed */) {\n    return 8 /* CharBoundaryCategory.LineBreakLF */;\n  } else if (charCode === 13 /* CharCode.CarriageReturn */) {\n    return 7 /* CharBoundaryCategory.LineBreakCR */;\n  } else if (isSpace(charCode)) {\n    return 6 /* CharBoundaryCategory.Space */;\n  } else if (charCode >= 97 /* CharCode.a */ && charCode <= 122 /* CharCode.z */) {\n    return 0 /* CharBoundaryCategory.WordLower */;\n  } else if (charCode >= 65 /* CharCode.A */ && charCode <= 90 /* CharCode.Z */) {\n    return 1 /* CharBoundaryCategory.WordUpper */;\n  } else if (charCode >= 48 /* CharCode.Digit0 */ && charCode <= 57 /* CharCode.Digit9 */) {\n    return 2 /* CharBoundaryCategory.WordNumber */;\n  } else if (charCode === -1) {\n    return 3 /* CharBoundaryCategory.End */;\n  } else if (charCode === 44 /* CharCode.Comma */ || charCode === 59 /* CharCode.Semicolon */) {\n    return 5 /* CharBoundaryCategory.Separator */;\n  } else {\n    return 4 /* CharBoundaryCategory.Other */;\n  }\n}", "map": {"version": 3, "names": ["findLastIdxMonotonous", "findLastMonotonous", "findFirstMonotonous", "OffsetRange", "Position", "Range", "isSpace", "LinesSliceCharSequence", "constructor", "lines", "range", "considerWhitespaceChanges", "elements", "firstElementOffsetByLineIdx", "lineStartOffsets", "trimmedWsLengthsByLineIdx", "push", "lineNumber", "startLineNumber", "endLineNumber", "line", "lineStartOffset", "startColumn", "substring", "trimmed<PERSON>s<PERSON>ength", "trimmedStartLine", "trimStart", "length", "trimEnd", "lineLength", "Math", "min", "endColumn", "i", "charCodeAt", "toString", "text", "getText", "slice", "start", "endExclusive", "map", "e", "String", "fromCharCode", "join", "getElement", "offset", "getBoundaryScore", "prevCategory", "getCategory", "nextCategory", "score", "getCategoryBoundaryScore", "translateOffset", "preference", "value", "lineOffset", "translateRange", "pos1", "pos2", "isBefore", "fromPositions", "findWordContaining", "undefined", "isWordChar", "end", "countLinesIn", "isStronglyEqual", "offset1", "offset2", "extendToFullLines", "x", "charCode", "category"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { findLastIdxMonotonous, findLastMonotonous, findFirstMonotonous } from '../../../../base/common/arraysFind.js';\nimport { OffsetRange } from '../../core/offsetRange.js';\nimport { Position } from '../../core/position.js';\nimport { Range } from '../../core/range.js';\nimport { isSpace } from './utils.js';\nexport class LinesSliceCharSequence {\n    constructor(lines, range, considerWhitespaceChanges) {\n        this.lines = lines;\n        this.range = range;\n        this.considerWhitespaceChanges = considerWhitespaceChanges;\n        this.elements = [];\n        this.firstElementOffsetByLineIdx = [];\n        this.lineStartOffsets = [];\n        this.trimmedWsLengthsByLineIdx = [];\n        this.firstElementOffsetByLineIdx.push(0);\n        for (let lineNumber = this.range.startLineNumber; lineNumber <= this.range.endLineNumber; lineNumber++) {\n            let line = lines[lineNumber - 1];\n            let lineStartOffset = 0;\n            if (lineNumber === this.range.startLineNumber && this.range.startColumn > 1) {\n                lineStartOffset = this.range.startColumn - 1;\n                line = line.substring(lineStartOffset);\n            }\n            this.lineStartOffsets.push(lineStartOffset);\n            let trimmedWsLength = 0;\n            if (!considerWhitespaceChanges) {\n                const trimmedStartLine = line.trimStart();\n                trimmedWsLength = line.length - trimmedStartLine.length;\n                line = trimmedStartLine.trimEnd();\n            }\n            this.trimmedWsLengthsByLineIdx.push(trimmedWsLength);\n            const lineLength = lineNumber === this.range.endLineNumber ? Math.min(this.range.endColumn - 1 - lineStartOffset - trimmedWsLength, line.length) : line.length;\n            for (let i = 0; i < lineLength; i++) {\n                this.elements.push(line.charCodeAt(i));\n            }\n            if (lineNumber < this.range.endLineNumber) {\n                this.elements.push('\\n'.charCodeAt(0));\n                this.firstElementOffsetByLineIdx.push(this.elements.length);\n            }\n        }\n    }\n    toString() {\n        return `Slice: \"${this.text}\"`;\n    }\n    get text() {\n        return this.getText(new OffsetRange(0, this.length));\n    }\n    getText(range) {\n        return this.elements.slice(range.start, range.endExclusive).map(e => String.fromCharCode(e)).join('');\n    }\n    getElement(offset) {\n        return this.elements[offset];\n    }\n    get length() {\n        return this.elements.length;\n    }\n    getBoundaryScore(length) {\n        //   a   b   c   ,           d   e   f\n        // 11  0   0   12  15  6   13  0   0   11\n        const prevCategory = getCategory(length > 0 ? this.elements[length - 1] : -1);\n        const nextCategory = getCategory(length < this.elements.length ? this.elements[length] : -1);\n        if (prevCategory === 7 /* CharBoundaryCategory.LineBreakCR */ && nextCategory === 8 /* CharBoundaryCategory.LineBreakLF */) {\n            // don't break between \\r and \\n\n            return 0;\n        }\n        if (prevCategory === 8 /* CharBoundaryCategory.LineBreakLF */) {\n            // prefer the linebreak before the change\n            return 150;\n        }\n        let score = 0;\n        if (prevCategory !== nextCategory) {\n            score += 10;\n            if (prevCategory === 0 /* CharBoundaryCategory.WordLower */ && nextCategory === 1 /* CharBoundaryCategory.WordUpper */) {\n                score += 1;\n            }\n        }\n        score += getCategoryBoundaryScore(prevCategory);\n        score += getCategoryBoundaryScore(nextCategory);\n        return score;\n    }\n    translateOffset(offset, preference = 'right') {\n        // find smallest i, so that lineBreakOffsets[i] <= offset using binary search\n        const i = findLastIdxMonotonous(this.firstElementOffsetByLineIdx, (value) => value <= offset);\n        const lineOffset = offset - this.firstElementOffsetByLineIdx[i];\n        return new Position(this.range.startLineNumber + i, 1 + this.lineStartOffsets[i] + lineOffset + ((lineOffset === 0 && preference === 'left') ? 0 : this.trimmedWsLengthsByLineIdx[i]));\n    }\n    translateRange(range) {\n        const pos1 = this.translateOffset(range.start, 'right');\n        const pos2 = this.translateOffset(range.endExclusive, 'left');\n        if (pos2.isBefore(pos1)) {\n            return Range.fromPositions(pos2, pos2);\n        }\n        return Range.fromPositions(pos1, pos2);\n    }\n    /**\n     * Finds the word that contains the character at the given offset\n     */\n    findWordContaining(offset) {\n        if (offset < 0 || offset >= this.elements.length) {\n            return undefined;\n        }\n        if (!isWordChar(this.elements[offset])) {\n            return undefined;\n        }\n        // find start\n        let start = offset;\n        while (start > 0 && isWordChar(this.elements[start - 1])) {\n            start--;\n        }\n        // find end\n        let end = offset;\n        while (end < this.elements.length && isWordChar(this.elements[end])) {\n            end++;\n        }\n        return new OffsetRange(start, end);\n    }\n    countLinesIn(range) {\n        return this.translateOffset(range.endExclusive).lineNumber - this.translateOffset(range.start).lineNumber;\n    }\n    isStronglyEqual(offset1, offset2) {\n        return this.elements[offset1] === this.elements[offset2];\n    }\n    extendToFullLines(range) {\n        const start = findLastMonotonous(this.firstElementOffsetByLineIdx, x => x <= range.start) ?? 0;\n        const end = findFirstMonotonous(this.firstElementOffsetByLineIdx, x => range.endExclusive <= x) ?? this.elements.length;\n        return new OffsetRange(start, end);\n    }\n}\nfunction isWordChar(charCode) {\n    return charCode >= 97 /* CharCode.a */ && charCode <= 122 /* CharCode.z */\n        || charCode >= 65 /* CharCode.A */ && charCode <= 90 /* CharCode.Z */\n        || charCode >= 48 /* CharCode.Digit0 */ && charCode <= 57 /* CharCode.Digit9 */;\n}\nconst score = {\n    [0 /* CharBoundaryCategory.WordLower */]: 0,\n    [1 /* CharBoundaryCategory.WordUpper */]: 0,\n    [2 /* CharBoundaryCategory.WordNumber */]: 0,\n    [3 /* CharBoundaryCategory.End */]: 10,\n    [4 /* CharBoundaryCategory.Other */]: 2,\n    [5 /* CharBoundaryCategory.Separator */]: 30,\n    [6 /* CharBoundaryCategory.Space */]: 3,\n    [7 /* CharBoundaryCategory.LineBreakCR */]: 10,\n    [8 /* CharBoundaryCategory.LineBreakLF */]: 10,\n};\nfunction getCategoryBoundaryScore(category) {\n    return score[category];\n}\nfunction getCategory(charCode) {\n    if (charCode === 10 /* CharCode.LineFeed */) {\n        return 8 /* CharBoundaryCategory.LineBreakLF */;\n    }\n    else if (charCode === 13 /* CharCode.CarriageReturn */) {\n        return 7 /* CharBoundaryCategory.LineBreakCR */;\n    }\n    else if (isSpace(charCode)) {\n        return 6 /* CharBoundaryCategory.Space */;\n    }\n    else if (charCode >= 97 /* CharCode.a */ && charCode <= 122 /* CharCode.z */) {\n        return 0 /* CharBoundaryCategory.WordLower */;\n    }\n    else if (charCode >= 65 /* CharCode.A */ && charCode <= 90 /* CharCode.Z */) {\n        return 1 /* CharBoundaryCategory.WordUpper */;\n    }\n    else if (charCode >= 48 /* CharCode.Digit0 */ && charCode <= 57 /* CharCode.Digit9 */) {\n        return 2 /* CharBoundaryCategory.WordNumber */;\n    }\n    else if (charCode === -1) {\n        return 3 /* CharBoundaryCategory.End */;\n    }\n    else if (charCode === 44 /* CharCode.Comma */ || charCode === 59 /* CharCode.Semicolon */) {\n        return 5 /* CharBoundaryCategory.Separator */;\n    }\n    else {\n        return 4 /* CharBoundaryCategory.Other */;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,EAAEC,kBAAkB,EAAEC,mBAAmB,QAAQ,uCAAuC;AACtH,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAO,MAAMC,sBAAsB,CAAC;EAChCC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,yBAAyB,EAAE;IACjD,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,2BAA2B,GAAG,EAAE;IACrC,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACF,2BAA2B,CAACG,IAAI,CAAC,CAAC,CAAC;IACxC,KAAK,IAAIC,UAAU,GAAG,IAAI,CAACP,KAAK,CAACQ,eAAe,EAAED,UAAU,IAAI,IAAI,CAACP,KAAK,CAACS,aAAa,EAAEF,UAAU,EAAE,EAAE;MACpG,IAAIG,IAAI,GAAGX,KAAK,CAACQ,UAAU,GAAG,CAAC,CAAC;MAChC,IAAII,eAAe,GAAG,CAAC;MACvB,IAAIJ,UAAU,KAAK,IAAI,CAACP,KAAK,CAACQ,eAAe,IAAI,IAAI,CAACR,KAAK,CAACY,WAAW,GAAG,CAAC,EAAE;QACzED,eAAe,GAAG,IAAI,CAACX,KAAK,CAACY,WAAW,GAAG,CAAC;QAC5CF,IAAI,GAAGA,IAAI,CAACG,SAAS,CAACF,eAAe,CAAC;MAC1C;MACA,IAAI,CAACP,gBAAgB,CAACE,IAAI,CAACK,eAAe,CAAC;MAC3C,IAAIG,eAAe,GAAG,CAAC;MACvB,IAAI,CAACb,yBAAyB,EAAE;QAC5B,MAAMc,gBAAgB,GAAGL,IAAI,CAACM,SAAS,CAAC,CAAC;QACzCF,eAAe,GAAGJ,IAAI,CAACO,MAAM,GAAGF,gBAAgB,CAACE,MAAM;QACvDP,IAAI,GAAGK,gBAAgB,CAACG,OAAO,CAAC,CAAC;MACrC;MACA,IAAI,CAACb,yBAAyB,CAACC,IAAI,CAACQ,eAAe,CAAC;MACpD,MAAMK,UAAU,GAAGZ,UAAU,KAAK,IAAI,CAACP,KAAK,CAACS,aAAa,GAAGW,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrB,KAAK,CAACsB,SAAS,GAAG,CAAC,GAAGX,eAAe,GAAGG,eAAe,EAAEJ,IAAI,CAACO,MAAM,CAAC,GAAGP,IAAI,CAACO,MAAM;MAC9J,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,EAAEI,CAAC,EAAE,EAAE;QACjC,IAAI,CAACrB,QAAQ,CAACI,IAAI,CAACI,IAAI,CAACc,UAAU,CAACD,CAAC,CAAC,CAAC;MAC1C;MACA,IAAIhB,UAAU,GAAG,IAAI,CAACP,KAAK,CAACS,aAAa,EAAE;QACvC,IAAI,CAACP,QAAQ,CAACI,IAAI,CAAC,IAAI,CAACkB,UAAU,CAAC,CAAC,CAAC,CAAC;QACtC,IAAI,CAACrB,2BAA2B,CAACG,IAAI,CAAC,IAAI,CAACJ,QAAQ,CAACe,MAAM,CAAC;MAC/D;IACJ;EACJ;EACAQ,QAAQA,CAAA,EAAG;IACP,OAAO,WAAW,IAAI,CAACC,IAAI,GAAG;EAClC;EACA,IAAIA,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,OAAO,CAAC,IAAIlC,WAAW,CAAC,CAAC,EAAE,IAAI,CAACwB,MAAM,CAAC,CAAC;EACxD;EACAU,OAAOA,CAAC3B,KAAK,EAAE;IACX,OAAO,IAAI,CAACE,QAAQ,CAAC0B,KAAK,CAAC5B,KAAK,CAAC6B,KAAK,EAAE7B,KAAK,CAAC8B,YAAY,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIC,MAAM,CAACC,YAAY,CAACF,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,EAAE,CAAC;EACzG;EACAC,UAAUA,CAACC,MAAM,EAAE;IACf,OAAO,IAAI,CAACnC,QAAQ,CAACmC,MAAM,CAAC;EAChC;EACA,IAAIpB,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACf,QAAQ,CAACe,MAAM;EAC/B;EACAqB,gBAAgBA,CAACrB,MAAM,EAAE;IACrB;IACA;IACA,MAAMsB,YAAY,GAAGC,WAAW,CAACvB,MAAM,GAAG,CAAC,GAAG,IAAI,CAACf,QAAQ,CAACe,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7E,MAAMwB,YAAY,GAAGD,WAAW,CAACvB,MAAM,GAAG,IAAI,CAACf,QAAQ,CAACe,MAAM,GAAG,IAAI,CAACf,QAAQ,CAACe,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5F,IAAIsB,YAAY,KAAK,CAAC,CAAC,0CAA0CE,YAAY,KAAK,CAAC,CAAC,wCAAwC;MACxH;MACA,OAAO,CAAC;IACZ;IACA,IAAIF,YAAY,KAAK,CAAC,CAAC,wCAAwC;MAC3D;MACA,OAAO,GAAG;IACd;IACA,IAAIG,KAAK,GAAG,CAAC;IACb,IAAIH,YAAY,KAAKE,YAAY,EAAE;MAC/BC,KAAK,IAAI,EAAE;MACX,IAAIH,YAAY,KAAK,CAAC,CAAC,wCAAwCE,YAAY,KAAK,CAAC,CAAC,sCAAsC;QACpHC,KAAK,IAAI,CAAC;MACd;IACJ;IACAA,KAAK,IAAIC,wBAAwB,CAACJ,YAAY,CAAC;IAC/CG,KAAK,IAAIC,wBAAwB,CAACF,YAAY,CAAC;IAC/C,OAAOC,KAAK;EAChB;EACAE,eAAeA,CAACP,MAAM,EAAEQ,UAAU,GAAG,OAAO,EAAE;IAC1C;IACA,MAAMtB,CAAC,GAAGjC,qBAAqB,CAAC,IAAI,CAACa,2BAA2B,EAAG2C,KAAK,IAAKA,KAAK,IAAIT,MAAM,CAAC;IAC7F,MAAMU,UAAU,GAAGV,MAAM,GAAG,IAAI,CAAClC,2BAA2B,CAACoB,CAAC,CAAC;IAC/D,OAAO,IAAI7B,QAAQ,CAAC,IAAI,CAACM,KAAK,CAACQ,eAAe,GAAGe,CAAC,EAAE,CAAC,GAAG,IAAI,CAACnB,gBAAgB,CAACmB,CAAC,CAAC,GAAGwB,UAAU,IAAKA,UAAU,KAAK,CAAC,IAAIF,UAAU,KAAK,MAAM,GAAI,CAAC,GAAG,IAAI,CAACxC,yBAAyB,CAACkB,CAAC,CAAC,CAAC,CAAC;EAC1L;EACAyB,cAAcA,CAAChD,KAAK,EAAE;IAClB,MAAMiD,IAAI,GAAG,IAAI,CAACL,eAAe,CAAC5C,KAAK,CAAC6B,KAAK,EAAE,OAAO,CAAC;IACvD,MAAMqB,IAAI,GAAG,IAAI,CAACN,eAAe,CAAC5C,KAAK,CAAC8B,YAAY,EAAE,MAAM,CAAC;IAC7D,IAAIoB,IAAI,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAE;MACrB,OAAOtD,KAAK,CAACyD,aAAa,CAACF,IAAI,EAAEA,IAAI,CAAC;IAC1C;IACA,OAAOvD,KAAK,CAACyD,aAAa,CAACH,IAAI,EAAEC,IAAI,CAAC;EAC1C;EACA;AACJ;AACA;EACIG,kBAAkBA,CAAChB,MAAM,EAAE;IACvB,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,IAAI,IAAI,CAACnC,QAAQ,CAACe,MAAM,EAAE;MAC9C,OAAOqC,SAAS;IACpB;IACA,IAAI,CAACC,UAAU,CAAC,IAAI,CAACrD,QAAQ,CAACmC,MAAM,CAAC,CAAC,EAAE;MACpC,OAAOiB,SAAS;IACpB;IACA;IACA,IAAIzB,KAAK,GAAGQ,MAAM;IAClB,OAAOR,KAAK,GAAG,CAAC,IAAI0B,UAAU,CAAC,IAAI,CAACrD,QAAQ,CAAC2B,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;MACtDA,KAAK,EAAE;IACX;IACA;IACA,IAAI2B,GAAG,GAAGnB,MAAM;IAChB,OAAOmB,GAAG,GAAG,IAAI,CAACtD,QAAQ,CAACe,MAAM,IAAIsC,UAAU,CAAC,IAAI,CAACrD,QAAQ,CAACsD,GAAG,CAAC,CAAC,EAAE;MACjEA,GAAG,EAAE;IACT;IACA,OAAO,IAAI/D,WAAW,CAACoC,KAAK,EAAE2B,GAAG,CAAC;EACtC;EACAC,YAAYA,CAACzD,KAAK,EAAE;IAChB,OAAO,IAAI,CAAC4C,eAAe,CAAC5C,KAAK,CAAC8B,YAAY,CAAC,CAACvB,UAAU,GAAG,IAAI,CAACqC,eAAe,CAAC5C,KAAK,CAAC6B,KAAK,CAAC,CAACtB,UAAU;EAC7G;EACAmD,eAAeA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC9B,OAAO,IAAI,CAAC1D,QAAQ,CAACyD,OAAO,CAAC,KAAK,IAAI,CAACzD,QAAQ,CAAC0D,OAAO,CAAC;EAC5D;EACAC,iBAAiBA,CAAC7D,KAAK,EAAE;IACrB,MAAM6B,KAAK,GAAGtC,kBAAkB,CAAC,IAAI,CAACY,2BAA2B,EAAE2D,CAAC,IAAIA,CAAC,IAAI9D,KAAK,CAAC6B,KAAK,CAAC,IAAI,CAAC;IAC9F,MAAM2B,GAAG,GAAGhE,mBAAmB,CAAC,IAAI,CAACW,2BAA2B,EAAE2D,CAAC,IAAI9D,KAAK,CAAC8B,YAAY,IAAIgC,CAAC,CAAC,IAAI,IAAI,CAAC5D,QAAQ,CAACe,MAAM;IACvH,OAAO,IAAIxB,WAAW,CAACoC,KAAK,EAAE2B,GAAG,CAAC;EACtC;AACJ;AACA,SAASD,UAAUA,CAACQ,QAAQ,EAAE;EAC1B,OAAOA,QAAQ,IAAI,EAAE,CAAC,oBAAoBA,QAAQ,IAAI,GAAG,CAAC,oBACnDA,QAAQ,IAAI,EAAE,CAAC,oBAAoBA,QAAQ,IAAI,EAAE,CAAC,oBAClDA,QAAQ,IAAI,EAAE,CAAC,yBAAyBA,QAAQ,IAAI,EAAE,CAAC;AAClE;AACA,MAAMrB,KAAK,GAAG;EACV,CAAC,CAAC,CAAC,uCAAuC,CAAC;EAC3C,CAAC,CAAC,CAAC,uCAAuC,CAAC;EAC3C,CAAC,CAAC,CAAC,wCAAwC,CAAC;EAC5C,CAAC,CAAC,CAAC,iCAAiC,EAAE;EACtC,CAAC,CAAC,CAAC,mCAAmC,CAAC;EACvC,CAAC,CAAC,CAAC,uCAAuC,EAAE;EAC5C,CAAC,CAAC,CAAC,mCAAmC,CAAC;EACvC,CAAC,CAAC,CAAC,yCAAyC,EAAE;EAC9C,CAAC,CAAC,CAAC,yCAAyC;AAChD,CAAC;AACD,SAASC,wBAAwBA,CAACqB,QAAQ,EAAE;EACxC,OAAOtB,KAAK,CAACsB,QAAQ,CAAC;AAC1B;AACA,SAASxB,WAAWA,CAACuB,QAAQ,EAAE;EAC3B,IAAIA,QAAQ,KAAK,EAAE,CAAC,yBAAyB;IACzC,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIA,QAAQ,KAAK,EAAE,CAAC,+BAA+B;IACpD,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAInE,OAAO,CAACmE,QAAQ,CAAC,EAAE;IACxB,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIA,QAAQ,IAAI,EAAE,CAAC,oBAAoBA,QAAQ,IAAI,GAAG,CAAC,kBAAkB;IAC1E,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIA,QAAQ,IAAI,EAAE,CAAC,oBAAoBA,QAAQ,IAAI,EAAE,CAAC,kBAAkB;IACzE,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIA,QAAQ,IAAI,EAAE,CAAC,yBAAyBA,QAAQ,IAAI,EAAE,CAAC,uBAAuB;IACnF,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE;IACtB,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIA,QAAQ,KAAK,EAAE,CAAC,wBAAwBA,QAAQ,KAAK,EAAE,CAAC,0BAA0B;IACvF,OAAO,CAAC,CAAC;EACb,CAAC,MACI;IACD,OAAO,CAAC,CAAC;EACb;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}