{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ViewEventHandler } from '../../common/viewEventHandler.js';\nexport class DynamicViewOverlay extends ViewEventHandler {}", "map": {"version": 3, "names": ["ViewEventHandler", "DynamicViewOverlay"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/view/dynamicViewOverlay.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ViewEventHandler } from '../../common/viewEventHandler.js';\nexport class DynamicViewOverlay extends ViewEventHandler {\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,kCAAkC;AACnE,OAAO,MAAMC,kBAAkB,SAASD,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}