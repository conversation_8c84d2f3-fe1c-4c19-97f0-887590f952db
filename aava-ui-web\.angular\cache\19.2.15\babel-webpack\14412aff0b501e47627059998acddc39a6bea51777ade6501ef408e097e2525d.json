{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { derived } from '../../../../../base/common/observable.js';\nimport { allowsTrueInlineDiffRendering } from './diffEditorViewZones/diffEditorViewZones.js';\nimport { MovedBlocksLinesFeature } from '../features/movedBlocksLinesFeature.js';\nimport { diffAddDecoration, diffAddDecorationEmpty, diffDeleteDecoration, diffDeleteDecorationEmpty, diffLineAddDecorationBackground, diffLineAddDecorationBackgroundWithIndicator, diffLineDeleteDecorationBackground, diffLineDeleteDecorationBackgroundWithIndicator, diffWholeLineAddDecoration, diffWholeLineDeleteDecoration } from '../registrations.contribution.js';\nimport { applyObservableDecorations } from '../utils.js';\nexport class DiffEditorDecorations extends Disposable {\n  constructor(_editors, _diffModel, _options, widget) {\n    super();\n    this._editors = _editors;\n    this._diffModel = _diffModel;\n    this._options = _options;\n    this._decorations = derived(this, reader => {\n      const diffModel = this._diffModel.read(reader);\n      const diff = diffModel?.diff.read(reader);\n      if (!diff) {\n        return null;\n      }\n      const movedTextToCompare = this._diffModel.read(reader).movedTextToCompare.read(reader);\n      const renderIndicators = this._options.renderIndicators.read(reader);\n      const showEmptyDecorations = this._options.showEmptyDecorations.read(reader);\n      const originalDecorations = [];\n      const modifiedDecorations = [];\n      if (!movedTextToCompare) {\n        for (const m of diff.mappings) {\n          if (!m.lineRangeMapping.original.isEmpty) {\n            originalDecorations.push({\n              range: m.lineRangeMapping.original.toInclusiveRange(),\n              options: renderIndicators ? diffLineDeleteDecorationBackgroundWithIndicator : diffLineDeleteDecorationBackground\n            });\n          }\n          if (!m.lineRangeMapping.modified.isEmpty) {\n            modifiedDecorations.push({\n              range: m.lineRangeMapping.modified.toInclusiveRange(),\n              options: renderIndicators ? diffLineAddDecorationBackgroundWithIndicator : diffLineAddDecorationBackground\n            });\n          }\n          if (m.lineRangeMapping.modified.isEmpty || m.lineRangeMapping.original.isEmpty) {\n            if (!m.lineRangeMapping.original.isEmpty) {\n              originalDecorations.push({\n                range: m.lineRangeMapping.original.toInclusiveRange(),\n                options: diffWholeLineDeleteDecoration\n              });\n            }\n            if (!m.lineRangeMapping.modified.isEmpty) {\n              modifiedDecorations.push({\n                range: m.lineRangeMapping.modified.toInclusiveRange(),\n                options: diffWholeLineAddDecoration\n              });\n            }\n          } else {\n            const useInlineDiff = this._options.useTrueInlineDiffRendering.read(reader) && allowsTrueInlineDiffRendering(m.lineRangeMapping);\n            for (const i of m.lineRangeMapping.innerChanges || []) {\n              // Don't show empty markers outside the line range\n              if (m.lineRangeMapping.original.contains(i.originalRange.startLineNumber)) {\n                originalDecorations.push({\n                  range: i.originalRange,\n                  options: i.originalRange.isEmpty() && showEmptyDecorations ? diffDeleteDecorationEmpty : diffDeleteDecoration\n                });\n              }\n              if (m.lineRangeMapping.modified.contains(i.modifiedRange.startLineNumber)) {\n                modifiedDecorations.push({\n                  range: i.modifiedRange,\n                  options: i.modifiedRange.isEmpty() && showEmptyDecorations && !useInlineDiff ? diffAddDecorationEmpty : diffAddDecoration\n                });\n              }\n              if (useInlineDiff) {\n                const deletedText = diffModel.model.original.getValueInRange(i.originalRange);\n                modifiedDecorations.push({\n                  range: i.modifiedRange,\n                  options: {\n                    description: 'deleted-text',\n                    before: {\n                      content: deletedText,\n                      inlineClassName: 'inline-deleted-text'\n                    },\n                    zIndex: 100000,\n                    showIfCollapsed: true\n                  }\n                });\n              }\n            }\n          }\n        }\n      }\n      if (movedTextToCompare) {\n        for (const m of movedTextToCompare.changes) {\n          const fullRangeOriginal = m.original.toInclusiveRange();\n          if (fullRangeOriginal) {\n            originalDecorations.push({\n              range: fullRangeOriginal,\n              options: renderIndicators ? diffLineDeleteDecorationBackgroundWithIndicator : diffLineDeleteDecorationBackground\n            });\n          }\n          const fullRangeModified = m.modified.toInclusiveRange();\n          if (fullRangeModified) {\n            modifiedDecorations.push({\n              range: fullRangeModified,\n              options: renderIndicators ? diffLineAddDecorationBackgroundWithIndicator : diffLineAddDecorationBackground\n            });\n          }\n          for (const i of m.innerChanges || []) {\n            originalDecorations.push({\n              range: i.originalRange,\n              options: diffDeleteDecoration\n            });\n            modifiedDecorations.push({\n              range: i.modifiedRange,\n              options: diffAddDecoration\n            });\n          }\n        }\n      }\n      const activeMovedText = this._diffModel.read(reader).activeMovedText.read(reader);\n      for (const m of diff.movedTexts) {\n        originalDecorations.push({\n          range: m.lineRangeMapping.original.toInclusiveRange(),\n          options: {\n            description: 'moved',\n            blockClassName: 'movedOriginal' + (m === activeMovedText ? ' currentMove' : ''),\n            blockPadding: [MovedBlocksLinesFeature.movedCodeBlockPadding, 0, MovedBlocksLinesFeature.movedCodeBlockPadding, MovedBlocksLinesFeature.movedCodeBlockPadding]\n          }\n        });\n        modifiedDecorations.push({\n          range: m.lineRangeMapping.modified.toInclusiveRange(),\n          options: {\n            description: 'moved',\n            blockClassName: 'movedModified' + (m === activeMovedText ? ' currentMove' : ''),\n            blockPadding: [4, 0, 4, 4]\n          }\n        });\n      }\n      return {\n        originalDecorations,\n        modifiedDecorations\n      };\n    });\n    this._register(applyObservableDecorations(this._editors.original, this._decorations.map(d => d?.originalDecorations || [])));\n    this._register(applyObservableDecorations(this._editors.modified, this._decorations.map(d => d?.modifiedDecorations || [])));\n  }\n}", "map": {"version": 3, "names": ["Disposable", "derived", "allowsTrueInlineDiffRendering", "MovedBlocksLinesFeature", "diffAddDecoration", "diffAddDecorationEmpty", "diffDeleteDecoration", "diffDeleteDecorationEmpty", "diffLineAddDecorationBackground", "diffLineAddDecorationBackgroundWithIndicator", "diffLineDeleteDecorationBackground", "diffLineDeleteDecorationBackgroundWithIndicator", "diffWholeLineAddDecoration", "diffWholeLineDeleteDecoration", "applyObservableDecorations", "DiffEditorDecorations", "constructor", "_editors", "_diffModel", "_options", "widget", "_decorations", "reader", "diffModel", "read", "diff", "movedTextToCompare", "renderIndicators", "showEmptyDecorations", "originalDecorations", "modifiedDecorations", "m", "mappings", "lineRangeMapping", "original", "isEmpty", "push", "range", "toInclusiveRange", "options", "modified", "useInlineDiff", "useTrueInlineDiffRendering", "i", "innerChanges", "contains", "originalRange", "startLineNumber", "modifiedRange", "deletedText", "model", "getValueInRange", "description", "before", "content", "inlineClassName", "zIndex", "showIfCollapsed", "changes", "fullRangeOriginal", "fullRangeModified", "activeMovedText", "movedTexts", "blockClassName", "blockPadding", "movedCodeBlockPadding", "_register", "map", "d"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/components/diffEditorDecorations.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { derived } from '../../../../../base/common/observable.js';\nimport { allowsTrueInlineDiffRendering } from './diffEditorViewZones/diffEditorViewZones.js';\nimport { MovedBlocksLinesFeature } from '../features/movedBlocksLinesFeature.js';\nimport { diffAddDecoration, diffAddDecorationEmpty, diffDeleteDecoration, diffDeleteDecorationEmpty, diffLineAddDecorationBackground, diffLineAddDecorationBackgroundWithIndicator, diffLineDeleteDecorationBackground, diffLineDeleteDecorationBackgroundWithIndicator, diffWholeLineAddDecoration, diffWholeLineDeleteDecoration } from '../registrations.contribution.js';\nimport { applyObservableDecorations } from '../utils.js';\nexport class DiffEditorDecorations extends Disposable {\n    constructor(_editors, _diffModel, _options, widget) {\n        super();\n        this._editors = _editors;\n        this._diffModel = _diffModel;\n        this._options = _options;\n        this._decorations = derived(this, (reader) => {\n            const diffModel = this._diffModel.read(reader);\n            const diff = diffModel?.diff.read(reader);\n            if (!diff) {\n                return null;\n            }\n            const movedTextToCompare = this._diffModel.read(reader).movedTextToCompare.read(reader);\n            const renderIndicators = this._options.renderIndicators.read(reader);\n            const showEmptyDecorations = this._options.showEmptyDecorations.read(reader);\n            const originalDecorations = [];\n            const modifiedDecorations = [];\n            if (!movedTextToCompare) {\n                for (const m of diff.mappings) {\n                    if (!m.lineRangeMapping.original.isEmpty) {\n                        originalDecorations.push({ range: m.lineRangeMapping.original.toInclusiveRange(), options: renderIndicators ? diffLineDeleteDecorationBackgroundWithIndicator : diffLineDeleteDecorationBackground });\n                    }\n                    if (!m.lineRangeMapping.modified.isEmpty) {\n                        modifiedDecorations.push({ range: m.lineRangeMapping.modified.toInclusiveRange(), options: renderIndicators ? diffLineAddDecorationBackgroundWithIndicator : diffLineAddDecorationBackground });\n                    }\n                    if (m.lineRangeMapping.modified.isEmpty || m.lineRangeMapping.original.isEmpty) {\n                        if (!m.lineRangeMapping.original.isEmpty) {\n                            originalDecorations.push({ range: m.lineRangeMapping.original.toInclusiveRange(), options: diffWholeLineDeleteDecoration });\n                        }\n                        if (!m.lineRangeMapping.modified.isEmpty) {\n                            modifiedDecorations.push({ range: m.lineRangeMapping.modified.toInclusiveRange(), options: diffWholeLineAddDecoration });\n                        }\n                    }\n                    else {\n                        const useInlineDiff = this._options.useTrueInlineDiffRendering.read(reader) && allowsTrueInlineDiffRendering(m.lineRangeMapping);\n                        for (const i of m.lineRangeMapping.innerChanges || []) {\n                            // Don't show empty markers outside the line range\n                            if (m.lineRangeMapping.original.contains(i.originalRange.startLineNumber)) {\n                                originalDecorations.push({ range: i.originalRange, options: (i.originalRange.isEmpty() && showEmptyDecorations) ? diffDeleteDecorationEmpty : diffDeleteDecoration });\n                            }\n                            if (m.lineRangeMapping.modified.contains(i.modifiedRange.startLineNumber)) {\n                                modifiedDecorations.push({ range: i.modifiedRange, options: (i.modifiedRange.isEmpty() && showEmptyDecorations && !useInlineDiff) ? diffAddDecorationEmpty : diffAddDecoration });\n                            }\n                            if (useInlineDiff) {\n                                const deletedText = diffModel.model.original.getValueInRange(i.originalRange);\n                                modifiedDecorations.push({\n                                    range: i.modifiedRange,\n                                    options: {\n                                        description: 'deleted-text',\n                                        before: {\n                                            content: deletedText,\n                                            inlineClassName: 'inline-deleted-text',\n                                        },\n                                        zIndex: 100000,\n                                        showIfCollapsed: true,\n                                    }\n                                });\n                            }\n                        }\n                    }\n                }\n            }\n            if (movedTextToCompare) {\n                for (const m of movedTextToCompare.changes) {\n                    const fullRangeOriginal = m.original.toInclusiveRange();\n                    if (fullRangeOriginal) {\n                        originalDecorations.push({ range: fullRangeOriginal, options: renderIndicators ? diffLineDeleteDecorationBackgroundWithIndicator : diffLineDeleteDecorationBackground });\n                    }\n                    const fullRangeModified = m.modified.toInclusiveRange();\n                    if (fullRangeModified) {\n                        modifiedDecorations.push({ range: fullRangeModified, options: renderIndicators ? diffLineAddDecorationBackgroundWithIndicator : diffLineAddDecorationBackground });\n                    }\n                    for (const i of m.innerChanges || []) {\n                        originalDecorations.push({ range: i.originalRange, options: diffDeleteDecoration });\n                        modifiedDecorations.push({ range: i.modifiedRange, options: diffAddDecoration });\n                    }\n                }\n            }\n            const activeMovedText = this._diffModel.read(reader).activeMovedText.read(reader);\n            for (const m of diff.movedTexts) {\n                originalDecorations.push({\n                    range: m.lineRangeMapping.original.toInclusiveRange(), options: {\n                        description: 'moved',\n                        blockClassName: 'movedOriginal' + (m === activeMovedText ? ' currentMove' : ''),\n                        blockPadding: [MovedBlocksLinesFeature.movedCodeBlockPadding, 0, MovedBlocksLinesFeature.movedCodeBlockPadding, MovedBlocksLinesFeature.movedCodeBlockPadding],\n                    }\n                });\n                modifiedDecorations.push({\n                    range: m.lineRangeMapping.modified.toInclusiveRange(), options: {\n                        description: 'moved',\n                        blockClassName: 'movedModified' + (m === activeMovedText ? ' currentMove' : ''),\n                        blockPadding: [4, 0, 4, 4],\n                    }\n                });\n            }\n            return { originalDecorations, modifiedDecorations };\n        });\n        this._register(applyObservableDecorations(this._editors.original, this._decorations.map(d => d?.originalDecorations || [])));\n        this._register(applyObservableDecorations(this._editors.modified, this._decorations.map(d => d?.modifiedDecorations || [])));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,yCAAyC;AACpE,SAASC,OAAO,QAAQ,0CAA0C;AAClE,SAASC,6BAA6B,QAAQ,8CAA8C;AAC5F,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,iBAAiB,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,+BAA+B,EAAEC,4CAA4C,EAAEC,kCAAkC,EAAEC,+CAA+C,EAAEC,0BAA0B,EAAEC,6BAA6B,QAAQ,kCAAkC;AAC5W,SAASC,0BAA0B,QAAQ,aAAa;AACxD,OAAO,MAAMC,qBAAqB,SAASf,UAAU,CAAC;EAClDgB,WAAWA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAE;IAChD,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,YAAY,GAAGpB,OAAO,CAAC,IAAI,EAAGqB,MAAM,IAAK;MAC1C,MAAMC,SAAS,GAAG,IAAI,CAACL,UAAU,CAACM,IAAI,CAACF,MAAM,CAAC;MAC9C,MAAMG,IAAI,GAAGF,SAAS,EAAEE,IAAI,CAACD,IAAI,CAACF,MAAM,CAAC;MACzC,IAAI,CAACG,IAAI,EAAE;QACP,OAAO,IAAI;MACf;MACA,MAAMC,kBAAkB,GAAG,IAAI,CAACR,UAAU,CAACM,IAAI,CAACF,MAAM,CAAC,CAACI,kBAAkB,CAACF,IAAI,CAACF,MAAM,CAAC;MACvF,MAAMK,gBAAgB,GAAG,IAAI,CAACR,QAAQ,CAACQ,gBAAgB,CAACH,IAAI,CAACF,MAAM,CAAC;MACpE,MAAMM,oBAAoB,GAAG,IAAI,CAACT,QAAQ,CAACS,oBAAoB,CAACJ,IAAI,CAACF,MAAM,CAAC;MAC5E,MAAMO,mBAAmB,GAAG,EAAE;MAC9B,MAAMC,mBAAmB,GAAG,EAAE;MAC9B,IAAI,CAACJ,kBAAkB,EAAE;QACrB,KAAK,MAAMK,CAAC,IAAIN,IAAI,CAACO,QAAQ,EAAE;UAC3B,IAAI,CAACD,CAAC,CAACE,gBAAgB,CAACC,QAAQ,CAACC,OAAO,EAAE;YACtCN,mBAAmB,CAACO,IAAI,CAAC;cAAEC,KAAK,EAAEN,CAAC,CAACE,gBAAgB,CAACC,QAAQ,CAACI,gBAAgB,CAAC,CAAC;cAAEC,OAAO,EAAEZ,gBAAgB,GAAGhB,+CAA+C,GAAGD;YAAmC,CAAC,CAAC;UACzM;UACA,IAAI,CAACqB,CAAC,CAACE,gBAAgB,CAACO,QAAQ,CAACL,OAAO,EAAE;YACtCL,mBAAmB,CAACM,IAAI,CAAC;cAAEC,KAAK,EAAEN,CAAC,CAACE,gBAAgB,CAACO,QAAQ,CAACF,gBAAgB,CAAC,CAAC;cAAEC,OAAO,EAAEZ,gBAAgB,GAAGlB,4CAA4C,GAAGD;YAAgC,CAAC,CAAC;UACnM;UACA,IAAIuB,CAAC,CAACE,gBAAgB,CAACO,QAAQ,CAACL,OAAO,IAAIJ,CAAC,CAACE,gBAAgB,CAACC,QAAQ,CAACC,OAAO,EAAE;YAC5E,IAAI,CAACJ,CAAC,CAACE,gBAAgB,CAACC,QAAQ,CAACC,OAAO,EAAE;cACtCN,mBAAmB,CAACO,IAAI,CAAC;gBAAEC,KAAK,EAAEN,CAAC,CAACE,gBAAgB,CAACC,QAAQ,CAACI,gBAAgB,CAAC,CAAC;gBAAEC,OAAO,EAAE1B;cAA8B,CAAC,CAAC;YAC/H;YACA,IAAI,CAACkB,CAAC,CAACE,gBAAgB,CAACO,QAAQ,CAACL,OAAO,EAAE;cACtCL,mBAAmB,CAACM,IAAI,CAAC;gBAAEC,KAAK,EAAEN,CAAC,CAACE,gBAAgB,CAACO,QAAQ,CAACF,gBAAgB,CAAC,CAAC;gBAAEC,OAAO,EAAE3B;cAA2B,CAAC,CAAC;YAC5H;UACJ,CAAC,MACI;YACD,MAAM6B,aAAa,GAAG,IAAI,CAACtB,QAAQ,CAACuB,0BAA0B,CAAClB,IAAI,CAACF,MAAM,CAAC,IAAIpB,6BAA6B,CAAC6B,CAAC,CAACE,gBAAgB,CAAC;YAChI,KAAK,MAAMU,CAAC,IAAIZ,CAAC,CAACE,gBAAgB,CAACW,YAAY,IAAI,EAAE,EAAE;cACnD;cACA,IAAIb,CAAC,CAACE,gBAAgB,CAACC,QAAQ,CAACW,QAAQ,CAACF,CAAC,CAACG,aAAa,CAACC,eAAe,CAAC,EAAE;gBACvElB,mBAAmB,CAACO,IAAI,CAAC;kBAAEC,KAAK,EAAEM,CAAC,CAACG,aAAa;kBAAEP,OAAO,EAAGI,CAAC,CAACG,aAAa,CAACX,OAAO,CAAC,CAAC,IAAIP,oBAAoB,GAAIrB,yBAAyB,GAAGD;gBAAqB,CAAC,CAAC;cACzK;cACA,IAAIyB,CAAC,CAACE,gBAAgB,CAACO,QAAQ,CAACK,QAAQ,CAACF,CAAC,CAACK,aAAa,CAACD,eAAe,CAAC,EAAE;gBACvEjB,mBAAmB,CAACM,IAAI,CAAC;kBAAEC,KAAK,EAAEM,CAAC,CAACK,aAAa;kBAAET,OAAO,EAAGI,CAAC,CAACK,aAAa,CAACb,OAAO,CAAC,CAAC,IAAIP,oBAAoB,IAAI,CAACa,aAAa,GAAIpC,sBAAsB,GAAGD;gBAAkB,CAAC,CAAC;cACrL;cACA,IAAIqC,aAAa,EAAE;gBACf,MAAMQ,WAAW,GAAG1B,SAAS,CAAC2B,KAAK,CAAChB,QAAQ,CAACiB,eAAe,CAACR,CAAC,CAACG,aAAa,CAAC;gBAC7EhB,mBAAmB,CAACM,IAAI,CAAC;kBACrBC,KAAK,EAAEM,CAAC,CAACK,aAAa;kBACtBT,OAAO,EAAE;oBACLa,WAAW,EAAE,cAAc;oBAC3BC,MAAM,EAAE;sBACJC,OAAO,EAAEL,WAAW;sBACpBM,eAAe,EAAE;oBACrB,CAAC;oBACDC,MAAM,EAAE,MAAM;oBACdC,eAAe,EAAE;kBACrB;gBACJ,CAAC,CAAC;cACN;YACJ;UACJ;QACJ;MACJ;MACA,IAAI/B,kBAAkB,EAAE;QACpB,KAAK,MAAMK,CAAC,IAAIL,kBAAkB,CAACgC,OAAO,EAAE;UACxC,MAAMC,iBAAiB,GAAG5B,CAAC,CAACG,QAAQ,CAACI,gBAAgB,CAAC,CAAC;UACvD,IAAIqB,iBAAiB,EAAE;YACnB9B,mBAAmB,CAACO,IAAI,CAAC;cAAEC,KAAK,EAAEsB,iBAAiB;cAAEpB,OAAO,EAAEZ,gBAAgB,GAAGhB,+CAA+C,GAAGD;YAAmC,CAAC,CAAC;UAC5K;UACA,MAAMkD,iBAAiB,GAAG7B,CAAC,CAACS,QAAQ,CAACF,gBAAgB,CAAC,CAAC;UACvD,IAAIsB,iBAAiB,EAAE;YACnB9B,mBAAmB,CAACM,IAAI,CAAC;cAAEC,KAAK,EAAEuB,iBAAiB;cAAErB,OAAO,EAAEZ,gBAAgB,GAAGlB,4CAA4C,GAAGD;YAAgC,CAAC,CAAC;UACtK;UACA,KAAK,MAAMmC,CAAC,IAAIZ,CAAC,CAACa,YAAY,IAAI,EAAE,EAAE;YAClCf,mBAAmB,CAACO,IAAI,CAAC;cAAEC,KAAK,EAAEM,CAAC,CAACG,aAAa;cAAEP,OAAO,EAAEjC;YAAqB,CAAC,CAAC;YACnFwB,mBAAmB,CAACM,IAAI,CAAC;cAAEC,KAAK,EAAEM,CAAC,CAACK,aAAa;cAAET,OAAO,EAAEnC;YAAkB,CAAC,CAAC;UACpF;QACJ;MACJ;MACA,MAAMyD,eAAe,GAAG,IAAI,CAAC3C,UAAU,CAACM,IAAI,CAACF,MAAM,CAAC,CAACuC,eAAe,CAACrC,IAAI,CAACF,MAAM,CAAC;MACjF,KAAK,MAAMS,CAAC,IAAIN,IAAI,CAACqC,UAAU,EAAE;QAC7BjC,mBAAmB,CAACO,IAAI,CAAC;UACrBC,KAAK,EAAEN,CAAC,CAACE,gBAAgB,CAACC,QAAQ,CAACI,gBAAgB,CAAC,CAAC;UAAEC,OAAO,EAAE;YAC5Da,WAAW,EAAE,OAAO;YACpBW,cAAc,EAAE,eAAe,IAAIhC,CAAC,KAAK8B,eAAe,GAAG,cAAc,GAAG,EAAE,CAAC;YAC/EG,YAAY,EAAE,CAAC7D,uBAAuB,CAAC8D,qBAAqB,EAAE,CAAC,EAAE9D,uBAAuB,CAAC8D,qBAAqB,EAAE9D,uBAAuB,CAAC8D,qBAAqB;UACjK;QACJ,CAAC,CAAC;QACFnC,mBAAmB,CAACM,IAAI,CAAC;UACrBC,KAAK,EAAEN,CAAC,CAACE,gBAAgB,CAACO,QAAQ,CAACF,gBAAgB,CAAC,CAAC;UAAEC,OAAO,EAAE;YAC5Da,WAAW,EAAE,OAAO;YACpBW,cAAc,EAAE,eAAe,IAAIhC,CAAC,KAAK8B,eAAe,GAAG,cAAc,GAAG,EAAE,CAAC;YAC/EG,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC7B;QACJ,CAAC,CAAC;MACN;MACA,OAAO;QAAEnC,mBAAmB;QAAEC;MAAoB,CAAC;IACvD,CAAC,CAAC;IACF,IAAI,CAACoC,SAAS,CAACpD,0BAA0B,CAAC,IAAI,CAACG,QAAQ,CAACiB,QAAQ,EAAE,IAAI,CAACb,YAAY,CAAC8C,GAAG,CAACC,CAAC,IAAIA,CAAC,EAAEvC,mBAAmB,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5H,IAAI,CAACqC,SAAS,CAACpD,0BAA0B,CAAC,IAAI,CAACG,QAAQ,CAACuB,QAAQ,EAAE,IAAI,CAACnB,YAAY,CAAC8C,GAAG,CAACC,CAAC,IAAIA,CAAC,EAAEtC,mBAAmB,IAAI,EAAE,CAAC,CAAC,CAAC;EAChI;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}