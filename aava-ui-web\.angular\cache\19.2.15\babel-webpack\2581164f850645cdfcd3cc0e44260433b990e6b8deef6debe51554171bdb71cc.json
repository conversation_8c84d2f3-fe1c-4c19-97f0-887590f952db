{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as buffer from '../../../base/common/buffer.js';\nimport { decodeUTF16LE } from './stringBuilder.js';\nfunction escapeNewLine(str) {\n  return str.replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\n}\nexport class TextChange {\n  get oldLength() {\n    return this.oldText.length;\n  }\n  get oldEnd() {\n    return this.oldPosition + this.oldText.length;\n  }\n  get newLength() {\n    return this.newText.length;\n  }\n  get newEnd() {\n    return this.newPosition + this.newText.length;\n  }\n  constructor(oldPosition, oldText, newPosition, newText) {\n    this.oldPosition = oldPosition;\n    this.oldText = oldText;\n    this.newPosition = newPosition;\n    this.newText = newText;\n  }\n  toString() {\n    if (this.oldText.length === 0) {\n      return `(insert@${this.oldPosition} \"${escapeNewLine(this.newText)}\")`;\n    }\n    if (this.newText.length === 0) {\n      return `(delete@${this.oldPosition} \"${escapeNewLine(this.oldText)}\")`;\n    }\n    return `(replace@${this.oldPosition} \"${escapeNewLine(this.oldText)}\" with \"${escapeNewLine(this.newText)}\")`;\n  }\n  static _writeStringSize(str) {\n    return 4 + 2 * str.length;\n  }\n  static _writeString(b, str, offset) {\n    const len = str.length;\n    buffer.writeUInt32BE(b, len, offset);\n    offset += 4;\n    for (let i = 0; i < len; i++) {\n      buffer.writeUInt16LE(b, str.charCodeAt(i), offset);\n      offset += 2;\n    }\n    return offset;\n  }\n  static _readString(b, offset) {\n    const len = buffer.readUInt32BE(b, offset);\n    offset += 4;\n    return decodeUTF16LE(b, offset, len);\n  }\n  writeSize() {\n    return +4 // oldPosition\n    + 4 // newPosition\n    + TextChange._writeStringSize(this.oldText) + TextChange._writeStringSize(this.newText);\n  }\n  write(b, offset) {\n    buffer.writeUInt32BE(b, this.oldPosition, offset);\n    offset += 4;\n    buffer.writeUInt32BE(b, this.newPosition, offset);\n    offset += 4;\n    offset = TextChange._writeString(b, this.oldText, offset);\n    offset = TextChange._writeString(b, this.newText, offset);\n    return offset;\n  }\n  static read(b, offset, dest) {\n    const oldPosition = buffer.readUInt32BE(b, offset);\n    offset += 4;\n    const newPosition = buffer.readUInt32BE(b, offset);\n    offset += 4;\n    const oldText = TextChange._readString(b, offset);\n    offset += TextChange._writeStringSize(oldText);\n    const newText = TextChange._readString(b, offset);\n    offset += TextChange._writeStringSize(newText);\n    dest.push(new TextChange(oldPosition, oldText, newPosition, newText));\n    return offset;\n  }\n}\nexport function compressConsecutiveTextChanges(prevEdits, currEdits) {\n  if (prevEdits === null || prevEdits.length === 0) {\n    return currEdits;\n  }\n  const compressor = new TextChangeCompressor(prevEdits, currEdits);\n  return compressor.compress();\n}\nclass TextChangeCompressor {\n  constructor(prevEdits, currEdits) {\n    this._prevEdits = prevEdits;\n    this._currEdits = currEdits;\n    this._result = [];\n    this._resultLen = 0;\n    this._prevLen = this._prevEdits.length;\n    this._prevDeltaOffset = 0;\n    this._currLen = this._currEdits.length;\n    this._currDeltaOffset = 0;\n  }\n  compress() {\n    let prevIndex = 0;\n    let currIndex = 0;\n    let prevEdit = this._getPrev(prevIndex);\n    let currEdit = this._getCurr(currIndex);\n    while (prevIndex < this._prevLen || currIndex < this._currLen) {\n      if (prevEdit === null) {\n        this._acceptCurr(currEdit);\n        currEdit = this._getCurr(++currIndex);\n        continue;\n      }\n      if (currEdit === null) {\n        this._acceptPrev(prevEdit);\n        prevEdit = this._getPrev(++prevIndex);\n        continue;\n      }\n      if (currEdit.oldEnd <= prevEdit.newPosition) {\n        this._acceptCurr(currEdit);\n        currEdit = this._getCurr(++currIndex);\n        continue;\n      }\n      if (prevEdit.newEnd <= currEdit.oldPosition) {\n        this._acceptPrev(prevEdit);\n        prevEdit = this._getPrev(++prevIndex);\n        continue;\n      }\n      if (currEdit.oldPosition < prevEdit.newPosition) {\n        const [e1, e2] = TextChangeCompressor._splitCurr(currEdit, prevEdit.newPosition - currEdit.oldPosition);\n        this._acceptCurr(e1);\n        currEdit = e2;\n        continue;\n      }\n      if (prevEdit.newPosition < currEdit.oldPosition) {\n        const [e1, e2] = TextChangeCompressor._splitPrev(prevEdit, currEdit.oldPosition - prevEdit.newPosition);\n        this._acceptPrev(e1);\n        prevEdit = e2;\n        continue;\n      }\n      // At this point, currEdit.oldPosition === prevEdit.newPosition\n      let mergePrev;\n      let mergeCurr;\n      if (currEdit.oldEnd === prevEdit.newEnd) {\n        mergePrev = prevEdit;\n        mergeCurr = currEdit;\n        prevEdit = this._getPrev(++prevIndex);\n        currEdit = this._getCurr(++currIndex);\n      } else if (currEdit.oldEnd < prevEdit.newEnd) {\n        const [e1, e2] = TextChangeCompressor._splitPrev(prevEdit, currEdit.oldLength);\n        mergePrev = e1;\n        mergeCurr = currEdit;\n        prevEdit = e2;\n        currEdit = this._getCurr(++currIndex);\n      } else {\n        const [e1, e2] = TextChangeCompressor._splitCurr(currEdit, prevEdit.newLength);\n        mergePrev = prevEdit;\n        mergeCurr = e1;\n        prevEdit = this._getPrev(++prevIndex);\n        currEdit = e2;\n      }\n      this._result[this._resultLen++] = new TextChange(mergePrev.oldPosition, mergePrev.oldText, mergeCurr.newPosition, mergeCurr.newText);\n      this._prevDeltaOffset += mergePrev.newLength - mergePrev.oldLength;\n      this._currDeltaOffset += mergeCurr.newLength - mergeCurr.oldLength;\n    }\n    const merged = TextChangeCompressor._merge(this._result);\n    const cleaned = TextChangeCompressor._removeNoOps(merged);\n    return cleaned;\n  }\n  _acceptCurr(currEdit) {\n    this._result[this._resultLen++] = TextChangeCompressor._rebaseCurr(this._prevDeltaOffset, currEdit);\n    this._currDeltaOffset += currEdit.newLength - currEdit.oldLength;\n  }\n  _getCurr(currIndex) {\n    return currIndex < this._currLen ? this._currEdits[currIndex] : null;\n  }\n  _acceptPrev(prevEdit) {\n    this._result[this._resultLen++] = TextChangeCompressor._rebasePrev(this._currDeltaOffset, prevEdit);\n    this._prevDeltaOffset += prevEdit.newLength - prevEdit.oldLength;\n  }\n  _getPrev(prevIndex) {\n    return prevIndex < this._prevLen ? this._prevEdits[prevIndex] : null;\n  }\n  static _rebaseCurr(prevDeltaOffset, currEdit) {\n    return new TextChange(currEdit.oldPosition - prevDeltaOffset, currEdit.oldText, currEdit.newPosition, currEdit.newText);\n  }\n  static _rebasePrev(currDeltaOffset, prevEdit) {\n    return new TextChange(prevEdit.oldPosition, prevEdit.oldText, prevEdit.newPosition + currDeltaOffset, prevEdit.newText);\n  }\n  static _splitPrev(edit, offset) {\n    const preText = edit.newText.substr(0, offset);\n    const postText = edit.newText.substr(offset);\n    return [new TextChange(edit.oldPosition, edit.oldText, edit.newPosition, preText), new TextChange(edit.oldEnd, '', edit.newPosition + offset, postText)];\n  }\n  static _splitCurr(edit, offset) {\n    const preText = edit.oldText.substr(0, offset);\n    const postText = edit.oldText.substr(offset);\n    return [new TextChange(edit.oldPosition, preText, edit.newPosition, edit.newText), new TextChange(edit.oldPosition + offset, postText, edit.newEnd, '')];\n  }\n  static _merge(edits) {\n    if (edits.length === 0) {\n      return edits;\n    }\n    const result = [];\n    let resultLen = 0;\n    let prev = edits[0];\n    for (let i = 1; i < edits.length; i++) {\n      const curr = edits[i];\n      if (prev.oldEnd === curr.oldPosition) {\n        // Merge into `prev`\n        prev = new TextChange(prev.oldPosition, prev.oldText + curr.oldText, prev.newPosition, prev.newText + curr.newText);\n      } else {\n        result[resultLen++] = prev;\n        prev = curr;\n      }\n    }\n    result[resultLen++] = prev;\n    return result;\n  }\n  static _removeNoOps(edits) {\n    if (edits.length === 0) {\n      return edits;\n    }\n    const result = [];\n    let resultLen = 0;\n    for (let i = 0; i < edits.length; i++) {\n      const edit = edits[i];\n      if (edit.oldText === edit.newText) {\n        continue;\n      }\n      result[resultLen++] = edit;\n    }\n    return result;\n  }\n}", "map": {"version": 3, "names": ["buffer", "decodeUTF16LE", "escapeNewLine", "str", "replace", "TextChange", "<PERSON><PERSON><PERSON><PERSON>", "oldText", "length", "oldEnd", "oldPosition", "<PERSON><PERSON><PERSON><PERSON>", "newText", "newEnd", "newPosition", "constructor", "toString", "_writeStringSize", "_writeString", "b", "offset", "len", "writeUInt32BE", "i", "writeUInt16LE", "charCodeAt", "_readString", "readUInt32BE", "writeSize", "write", "read", "dest", "push", "compressConsecutiveTextChanges", "prevEdits", "currEdits", "compressor", "TextChangeCompressor", "compress", "_prevEdits", "_currEdits", "_result", "_resultLen", "_prevLen", "_prevDeltaOffset", "_currLen", "_currDeltaOffset", "prevIndex", "currIndex", "prevEdit", "_getPrev", "currEdit", "_get<PERSON>urr", "_acceptCurr", "_acceptPrev", "e1", "e2", "_splitCurr", "_splitPrev", "mergePrev", "mergeCurr", "merged", "_merge", "cleaned", "_removeNoOps", "_rebaseCurr", "_rebasePrev", "prevDeltaOffset", "currDeltaOffset", "edit", "preText", "substr", "postText", "edits", "result", "resultLen", "prev", "curr"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/core/textChange.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as buffer from '../../../base/common/buffer.js';\nimport { decodeUTF16LE } from './stringBuilder.js';\nfunction escapeNewLine(str) {\n    return (str\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r'));\n}\nexport class TextChange {\n    get oldLength() {\n        return this.oldText.length;\n    }\n    get oldEnd() {\n        return this.oldPosition + this.oldText.length;\n    }\n    get newLength() {\n        return this.newText.length;\n    }\n    get newEnd() {\n        return this.newPosition + this.newText.length;\n    }\n    constructor(oldPosition, oldText, newPosition, newText) {\n        this.oldPosition = oldPosition;\n        this.oldText = oldText;\n        this.newPosition = newPosition;\n        this.newText = newText;\n    }\n    toString() {\n        if (this.oldText.length === 0) {\n            return `(insert@${this.oldPosition} \"${escapeNewLine(this.newText)}\")`;\n        }\n        if (this.newText.length === 0) {\n            return `(delete@${this.oldPosition} \"${escapeNewLine(this.oldText)}\")`;\n        }\n        return `(replace@${this.oldPosition} \"${escapeNewLine(this.oldText)}\" with \"${escapeNewLine(this.newText)}\")`;\n    }\n    static _writeStringSize(str) {\n        return (4 + 2 * str.length);\n    }\n    static _writeString(b, str, offset) {\n        const len = str.length;\n        buffer.writeUInt32BE(b, len, offset);\n        offset += 4;\n        for (let i = 0; i < len; i++) {\n            buffer.writeUInt16LE(b, str.charCodeAt(i), offset);\n            offset += 2;\n        }\n        return offset;\n    }\n    static _readString(b, offset) {\n        const len = buffer.readUInt32BE(b, offset);\n        offset += 4;\n        return decodeUTF16LE(b, offset, len);\n    }\n    writeSize() {\n        return (+4 // oldPosition\n            + 4 // newPosition\n            + TextChange._writeStringSize(this.oldText)\n            + TextChange._writeStringSize(this.newText));\n    }\n    write(b, offset) {\n        buffer.writeUInt32BE(b, this.oldPosition, offset);\n        offset += 4;\n        buffer.writeUInt32BE(b, this.newPosition, offset);\n        offset += 4;\n        offset = TextChange._writeString(b, this.oldText, offset);\n        offset = TextChange._writeString(b, this.newText, offset);\n        return offset;\n    }\n    static read(b, offset, dest) {\n        const oldPosition = buffer.readUInt32BE(b, offset);\n        offset += 4;\n        const newPosition = buffer.readUInt32BE(b, offset);\n        offset += 4;\n        const oldText = TextChange._readString(b, offset);\n        offset += TextChange._writeStringSize(oldText);\n        const newText = TextChange._readString(b, offset);\n        offset += TextChange._writeStringSize(newText);\n        dest.push(new TextChange(oldPosition, oldText, newPosition, newText));\n        return offset;\n    }\n}\nexport function compressConsecutiveTextChanges(prevEdits, currEdits) {\n    if (prevEdits === null || prevEdits.length === 0) {\n        return currEdits;\n    }\n    const compressor = new TextChangeCompressor(prevEdits, currEdits);\n    return compressor.compress();\n}\nclass TextChangeCompressor {\n    constructor(prevEdits, currEdits) {\n        this._prevEdits = prevEdits;\n        this._currEdits = currEdits;\n        this._result = [];\n        this._resultLen = 0;\n        this._prevLen = this._prevEdits.length;\n        this._prevDeltaOffset = 0;\n        this._currLen = this._currEdits.length;\n        this._currDeltaOffset = 0;\n    }\n    compress() {\n        let prevIndex = 0;\n        let currIndex = 0;\n        let prevEdit = this._getPrev(prevIndex);\n        let currEdit = this._getCurr(currIndex);\n        while (prevIndex < this._prevLen || currIndex < this._currLen) {\n            if (prevEdit === null) {\n                this._acceptCurr(currEdit);\n                currEdit = this._getCurr(++currIndex);\n                continue;\n            }\n            if (currEdit === null) {\n                this._acceptPrev(prevEdit);\n                prevEdit = this._getPrev(++prevIndex);\n                continue;\n            }\n            if (currEdit.oldEnd <= prevEdit.newPosition) {\n                this._acceptCurr(currEdit);\n                currEdit = this._getCurr(++currIndex);\n                continue;\n            }\n            if (prevEdit.newEnd <= currEdit.oldPosition) {\n                this._acceptPrev(prevEdit);\n                prevEdit = this._getPrev(++prevIndex);\n                continue;\n            }\n            if (currEdit.oldPosition < prevEdit.newPosition) {\n                const [e1, e2] = TextChangeCompressor._splitCurr(currEdit, prevEdit.newPosition - currEdit.oldPosition);\n                this._acceptCurr(e1);\n                currEdit = e2;\n                continue;\n            }\n            if (prevEdit.newPosition < currEdit.oldPosition) {\n                const [e1, e2] = TextChangeCompressor._splitPrev(prevEdit, currEdit.oldPosition - prevEdit.newPosition);\n                this._acceptPrev(e1);\n                prevEdit = e2;\n                continue;\n            }\n            // At this point, currEdit.oldPosition === prevEdit.newPosition\n            let mergePrev;\n            let mergeCurr;\n            if (currEdit.oldEnd === prevEdit.newEnd) {\n                mergePrev = prevEdit;\n                mergeCurr = currEdit;\n                prevEdit = this._getPrev(++prevIndex);\n                currEdit = this._getCurr(++currIndex);\n            }\n            else if (currEdit.oldEnd < prevEdit.newEnd) {\n                const [e1, e2] = TextChangeCompressor._splitPrev(prevEdit, currEdit.oldLength);\n                mergePrev = e1;\n                mergeCurr = currEdit;\n                prevEdit = e2;\n                currEdit = this._getCurr(++currIndex);\n            }\n            else {\n                const [e1, e2] = TextChangeCompressor._splitCurr(currEdit, prevEdit.newLength);\n                mergePrev = prevEdit;\n                mergeCurr = e1;\n                prevEdit = this._getPrev(++prevIndex);\n                currEdit = e2;\n            }\n            this._result[this._resultLen++] = new TextChange(mergePrev.oldPosition, mergePrev.oldText, mergeCurr.newPosition, mergeCurr.newText);\n            this._prevDeltaOffset += mergePrev.newLength - mergePrev.oldLength;\n            this._currDeltaOffset += mergeCurr.newLength - mergeCurr.oldLength;\n        }\n        const merged = TextChangeCompressor._merge(this._result);\n        const cleaned = TextChangeCompressor._removeNoOps(merged);\n        return cleaned;\n    }\n    _acceptCurr(currEdit) {\n        this._result[this._resultLen++] = TextChangeCompressor._rebaseCurr(this._prevDeltaOffset, currEdit);\n        this._currDeltaOffset += currEdit.newLength - currEdit.oldLength;\n    }\n    _getCurr(currIndex) {\n        return (currIndex < this._currLen ? this._currEdits[currIndex] : null);\n    }\n    _acceptPrev(prevEdit) {\n        this._result[this._resultLen++] = TextChangeCompressor._rebasePrev(this._currDeltaOffset, prevEdit);\n        this._prevDeltaOffset += prevEdit.newLength - prevEdit.oldLength;\n    }\n    _getPrev(prevIndex) {\n        return (prevIndex < this._prevLen ? this._prevEdits[prevIndex] : null);\n    }\n    static _rebaseCurr(prevDeltaOffset, currEdit) {\n        return new TextChange(currEdit.oldPosition - prevDeltaOffset, currEdit.oldText, currEdit.newPosition, currEdit.newText);\n    }\n    static _rebasePrev(currDeltaOffset, prevEdit) {\n        return new TextChange(prevEdit.oldPosition, prevEdit.oldText, prevEdit.newPosition + currDeltaOffset, prevEdit.newText);\n    }\n    static _splitPrev(edit, offset) {\n        const preText = edit.newText.substr(0, offset);\n        const postText = edit.newText.substr(offset);\n        return [\n            new TextChange(edit.oldPosition, edit.oldText, edit.newPosition, preText),\n            new TextChange(edit.oldEnd, '', edit.newPosition + offset, postText)\n        ];\n    }\n    static _splitCurr(edit, offset) {\n        const preText = edit.oldText.substr(0, offset);\n        const postText = edit.oldText.substr(offset);\n        return [\n            new TextChange(edit.oldPosition, preText, edit.newPosition, edit.newText),\n            new TextChange(edit.oldPosition + offset, postText, edit.newEnd, '')\n        ];\n    }\n    static _merge(edits) {\n        if (edits.length === 0) {\n            return edits;\n        }\n        const result = [];\n        let resultLen = 0;\n        let prev = edits[0];\n        for (let i = 1; i < edits.length; i++) {\n            const curr = edits[i];\n            if (prev.oldEnd === curr.oldPosition) {\n                // Merge into `prev`\n                prev = new TextChange(prev.oldPosition, prev.oldText + curr.oldText, prev.newPosition, prev.newText + curr.newText);\n            }\n            else {\n                result[resultLen++] = prev;\n                prev = curr;\n            }\n        }\n        result[resultLen++] = prev;\n        return result;\n    }\n    static _removeNoOps(edits) {\n        if (edits.length === 0) {\n            return edits;\n        }\n        const result = [];\n        let resultLen = 0;\n        for (let i = 0; i < edits.length; i++) {\n            const edit = edits[i];\n            if (edit.oldText === edit.newText) {\n                continue;\n            }\n            result[resultLen++] = edit;\n        }\n        return result;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,gCAAgC;AACxD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,aAAaA,CAACC,GAAG,EAAE;EACxB,OAAQA,GAAG,CACNC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;AAC9B;AACA,OAAO,MAAMC,UAAU,CAAC;EACpB,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,OAAO,CAACC,MAAM;EAC9B;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,WAAW,GAAG,IAAI,CAACH,OAAO,CAACC,MAAM;EACjD;EACA,IAAIG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,OAAO,CAACJ,MAAM;EAC9B;EACA,IAAIK,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,WAAW,GAAG,IAAI,CAACF,OAAO,CAACJ,MAAM;EACjD;EACAO,WAAWA,CAACL,WAAW,EAAEH,OAAO,EAAEO,WAAW,EAAEF,OAAO,EAAE;IACpD,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACO,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACF,OAAO,GAAGA,OAAO;EAC1B;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACT,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MAC3B,OAAO,WAAW,IAAI,CAACE,WAAW,KAAKR,aAAa,CAAC,IAAI,CAACU,OAAO,CAAC,IAAI;IAC1E;IACA,IAAI,IAAI,CAACA,OAAO,CAACJ,MAAM,KAAK,CAAC,EAAE;MAC3B,OAAO,WAAW,IAAI,CAACE,WAAW,KAAKR,aAAa,CAAC,IAAI,CAACK,OAAO,CAAC,IAAI;IAC1E;IACA,OAAO,YAAY,IAAI,CAACG,WAAW,KAAKR,aAAa,CAAC,IAAI,CAACK,OAAO,CAAC,WAAWL,aAAa,CAAC,IAAI,CAACU,OAAO,CAAC,IAAI;EACjH;EACA,OAAOK,gBAAgBA,CAACd,GAAG,EAAE;IACzB,OAAQ,CAAC,GAAG,CAAC,GAAGA,GAAG,CAACK,MAAM;EAC9B;EACA,OAAOU,YAAYA,CAACC,CAAC,EAAEhB,GAAG,EAAEiB,MAAM,EAAE;IAChC,MAAMC,GAAG,GAAGlB,GAAG,CAACK,MAAM;IACtBR,MAAM,CAACsB,aAAa,CAACH,CAAC,EAAEE,GAAG,EAAED,MAAM,CAAC;IACpCA,MAAM,IAAI,CAAC;IACX,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC1BvB,MAAM,CAACwB,aAAa,CAACL,CAAC,EAAEhB,GAAG,CAACsB,UAAU,CAACF,CAAC,CAAC,EAAEH,MAAM,CAAC;MAClDA,MAAM,IAAI,CAAC;IACf;IACA,OAAOA,MAAM;EACjB;EACA,OAAOM,WAAWA,CAACP,CAAC,EAAEC,MAAM,EAAE;IAC1B,MAAMC,GAAG,GAAGrB,MAAM,CAAC2B,YAAY,CAACR,CAAC,EAAEC,MAAM,CAAC;IAC1CA,MAAM,IAAI,CAAC;IACX,OAAOnB,aAAa,CAACkB,CAAC,EAAEC,MAAM,EAAEC,GAAG,CAAC;EACxC;EACAO,SAASA,CAAA,EAAG;IACR,OAAQ,CAAC,CAAC,CAAC;IAAA,EACL,CAAC,CAAC;IAAA,EACFvB,UAAU,CAACY,gBAAgB,CAAC,IAAI,CAACV,OAAO,CAAC,GACzCF,UAAU,CAACY,gBAAgB,CAAC,IAAI,CAACL,OAAO,CAAC;EACnD;EACAiB,KAAKA,CAACV,CAAC,EAAEC,MAAM,EAAE;IACbpB,MAAM,CAACsB,aAAa,CAACH,CAAC,EAAE,IAAI,CAACT,WAAW,EAAEU,MAAM,CAAC;IACjDA,MAAM,IAAI,CAAC;IACXpB,MAAM,CAACsB,aAAa,CAACH,CAAC,EAAE,IAAI,CAACL,WAAW,EAAEM,MAAM,CAAC;IACjDA,MAAM,IAAI,CAAC;IACXA,MAAM,GAAGf,UAAU,CAACa,YAAY,CAACC,CAAC,EAAE,IAAI,CAACZ,OAAO,EAAEa,MAAM,CAAC;IACzDA,MAAM,GAAGf,UAAU,CAACa,YAAY,CAACC,CAAC,EAAE,IAAI,CAACP,OAAO,EAAEQ,MAAM,CAAC;IACzD,OAAOA,MAAM;EACjB;EACA,OAAOU,IAAIA,CAACX,CAAC,EAAEC,MAAM,EAAEW,IAAI,EAAE;IACzB,MAAMrB,WAAW,GAAGV,MAAM,CAAC2B,YAAY,CAACR,CAAC,EAAEC,MAAM,CAAC;IAClDA,MAAM,IAAI,CAAC;IACX,MAAMN,WAAW,GAAGd,MAAM,CAAC2B,YAAY,CAACR,CAAC,EAAEC,MAAM,CAAC;IAClDA,MAAM,IAAI,CAAC;IACX,MAAMb,OAAO,GAAGF,UAAU,CAACqB,WAAW,CAACP,CAAC,EAAEC,MAAM,CAAC;IACjDA,MAAM,IAAIf,UAAU,CAACY,gBAAgB,CAACV,OAAO,CAAC;IAC9C,MAAMK,OAAO,GAAGP,UAAU,CAACqB,WAAW,CAACP,CAAC,EAAEC,MAAM,CAAC;IACjDA,MAAM,IAAIf,UAAU,CAACY,gBAAgB,CAACL,OAAO,CAAC;IAC9CmB,IAAI,CAACC,IAAI,CAAC,IAAI3B,UAAU,CAACK,WAAW,EAAEH,OAAO,EAAEO,WAAW,EAAEF,OAAO,CAAC,CAAC;IACrE,OAAOQ,MAAM;EACjB;AACJ;AACA,OAAO,SAASa,8BAA8BA,CAACC,SAAS,EAAEC,SAAS,EAAE;EACjE,IAAID,SAAS,KAAK,IAAI,IAAIA,SAAS,CAAC1B,MAAM,KAAK,CAAC,EAAE;IAC9C,OAAO2B,SAAS;EACpB;EACA,MAAMC,UAAU,GAAG,IAAIC,oBAAoB,CAACH,SAAS,EAAEC,SAAS,CAAC;EACjE,OAAOC,UAAU,CAACE,QAAQ,CAAC,CAAC;AAChC;AACA,MAAMD,oBAAoB,CAAC;EACvBtB,WAAWA,CAACmB,SAAS,EAAEC,SAAS,EAAE;IAC9B,IAAI,CAACI,UAAU,GAAGL,SAAS;IAC3B,IAAI,CAACM,UAAU,GAAGL,SAAS;IAC3B,IAAI,CAACM,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACJ,UAAU,CAAC/B,MAAM;IACtC,IAAI,CAACoC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACL,UAAU,CAAChC,MAAM;IACtC,IAAI,CAACsC,gBAAgB,GAAG,CAAC;EAC7B;EACAR,QAAQA,CAAA,EAAG;IACP,IAAIS,SAAS,GAAG,CAAC;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAACH,SAAS,CAAC;IACvC,IAAII,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAACJ,SAAS,CAAC;IACvC,OAAOD,SAAS,GAAG,IAAI,CAACJ,QAAQ,IAAIK,SAAS,GAAG,IAAI,CAACH,QAAQ,EAAE;MAC3D,IAAII,QAAQ,KAAK,IAAI,EAAE;QACnB,IAAI,CAACI,WAAW,CAACF,QAAQ,CAAC;QAC1BA,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAEJ,SAAS,CAAC;QACrC;MACJ;MACA,IAAIG,QAAQ,KAAK,IAAI,EAAE;QACnB,IAAI,CAACG,WAAW,CAACL,QAAQ,CAAC;QAC1BA,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAEH,SAAS,CAAC;QACrC;MACJ;MACA,IAAII,QAAQ,CAAC1C,MAAM,IAAIwC,QAAQ,CAACnC,WAAW,EAAE;QACzC,IAAI,CAACuC,WAAW,CAACF,QAAQ,CAAC;QAC1BA,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAEJ,SAAS,CAAC;QACrC;MACJ;MACA,IAAIC,QAAQ,CAACpC,MAAM,IAAIsC,QAAQ,CAACzC,WAAW,EAAE;QACzC,IAAI,CAAC4C,WAAW,CAACL,QAAQ,CAAC;QAC1BA,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAEH,SAAS,CAAC;QACrC;MACJ;MACA,IAAII,QAAQ,CAACzC,WAAW,GAAGuC,QAAQ,CAACnC,WAAW,EAAE;QAC7C,MAAM,CAACyC,EAAE,EAAEC,EAAE,CAAC,GAAGnB,oBAAoB,CAACoB,UAAU,CAACN,QAAQ,EAAEF,QAAQ,CAACnC,WAAW,GAAGqC,QAAQ,CAACzC,WAAW,CAAC;QACvG,IAAI,CAAC2C,WAAW,CAACE,EAAE,CAAC;QACpBJ,QAAQ,GAAGK,EAAE;QACb;MACJ;MACA,IAAIP,QAAQ,CAACnC,WAAW,GAAGqC,QAAQ,CAACzC,WAAW,EAAE;QAC7C,MAAM,CAAC6C,EAAE,EAAEC,EAAE,CAAC,GAAGnB,oBAAoB,CAACqB,UAAU,CAACT,QAAQ,EAAEE,QAAQ,CAACzC,WAAW,GAAGuC,QAAQ,CAACnC,WAAW,CAAC;QACvG,IAAI,CAACwC,WAAW,CAACC,EAAE,CAAC;QACpBN,QAAQ,GAAGO,EAAE;QACb;MACJ;MACA;MACA,IAAIG,SAAS;MACb,IAAIC,SAAS;MACb,IAAIT,QAAQ,CAAC1C,MAAM,KAAKwC,QAAQ,CAACpC,MAAM,EAAE;QACrC8C,SAAS,GAAGV,QAAQ;QACpBW,SAAS,GAAGT,QAAQ;QACpBF,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAEH,SAAS,CAAC;QACrCI,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAEJ,SAAS,CAAC;MACzC,CAAC,MACI,IAAIG,QAAQ,CAAC1C,MAAM,GAAGwC,QAAQ,CAACpC,MAAM,EAAE;QACxC,MAAM,CAAC0C,EAAE,EAAEC,EAAE,CAAC,GAAGnB,oBAAoB,CAACqB,UAAU,CAACT,QAAQ,EAAEE,QAAQ,CAAC7C,SAAS,CAAC;QAC9EqD,SAAS,GAAGJ,EAAE;QACdK,SAAS,GAAGT,QAAQ;QACpBF,QAAQ,GAAGO,EAAE;QACbL,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAEJ,SAAS,CAAC;MACzC,CAAC,MACI;QACD,MAAM,CAACO,EAAE,EAAEC,EAAE,CAAC,GAAGnB,oBAAoB,CAACoB,UAAU,CAACN,QAAQ,EAAEF,QAAQ,CAACtC,SAAS,CAAC;QAC9EgD,SAAS,GAAGV,QAAQ;QACpBW,SAAS,GAAGL,EAAE;QACdN,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAEH,SAAS,CAAC;QACrCI,QAAQ,GAAGK,EAAE;MACjB;MACA,IAAI,CAACf,OAAO,CAAC,IAAI,CAACC,UAAU,EAAE,CAAC,GAAG,IAAIrC,UAAU,CAACsD,SAAS,CAACjD,WAAW,EAAEiD,SAAS,CAACpD,OAAO,EAAEqD,SAAS,CAAC9C,WAAW,EAAE8C,SAAS,CAAChD,OAAO,CAAC;MACpI,IAAI,CAACgC,gBAAgB,IAAIe,SAAS,CAAChD,SAAS,GAAGgD,SAAS,CAACrD,SAAS;MAClE,IAAI,CAACwC,gBAAgB,IAAIc,SAAS,CAACjD,SAAS,GAAGiD,SAAS,CAACtD,SAAS;IACtE;IACA,MAAMuD,MAAM,GAAGxB,oBAAoB,CAACyB,MAAM,CAAC,IAAI,CAACrB,OAAO,CAAC;IACxD,MAAMsB,OAAO,GAAG1B,oBAAoB,CAAC2B,YAAY,CAACH,MAAM,CAAC;IACzD,OAAOE,OAAO;EAClB;EACAV,WAAWA,CAACF,QAAQ,EAAE;IAClB,IAAI,CAACV,OAAO,CAAC,IAAI,CAACC,UAAU,EAAE,CAAC,GAAGL,oBAAoB,CAAC4B,WAAW,CAAC,IAAI,CAACrB,gBAAgB,EAAEO,QAAQ,CAAC;IACnG,IAAI,CAACL,gBAAgB,IAAIK,QAAQ,CAACxC,SAAS,GAAGwC,QAAQ,CAAC7C,SAAS;EACpE;EACA8C,QAAQA,CAACJ,SAAS,EAAE;IAChB,OAAQA,SAAS,GAAG,IAAI,CAACH,QAAQ,GAAG,IAAI,CAACL,UAAU,CAACQ,SAAS,CAAC,GAAG,IAAI;EACzE;EACAM,WAAWA,CAACL,QAAQ,EAAE;IAClB,IAAI,CAACR,OAAO,CAAC,IAAI,CAACC,UAAU,EAAE,CAAC,GAAGL,oBAAoB,CAAC6B,WAAW,CAAC,IAAI,CAACpB,gBAAgB,EAAEG,QAAQ,CAAC;IACnG,IAAI,CAACL,gBAAgB,IAAIK,QAAQ,CAACtC,SAAS,GAAGsC,QAAQ,CAAC3C,SAAS;EACpE;EACA4C,QAAQA,CAACH,SAAS,EAAE;IAChB,OAAQA,SAAS,GAAG,IAAI,CAACJ,QAAQ,GAAG,IAAI,CAACJ,UAAU,CAACQ,SAAS,CAAC,GAAG,IAAI;EACzE;EACA,OAAOkB,WAAWA,CAACE,eAAe,EAAEhB,QAAQ,EAAE;IAC1C,OAAO,IAAI9C,UAAU,CAAC8C,QAAQ,CAACzC,WAAW,GAAGyD,eAAe,EAAEhB,QAAQ,CAAC5C,OAAO,EAAE4C,QAAQ,CAACrC,WAAW,EAAEqC,QAAQ,CAACvC,OAAO,CAAC;EAC3H;EACA,OAAOsD,WAAWA,CAACE,eAAe,EAAEnB,QAAQ,EAAE;IAC1C,OAAO,IAAI5C,UAAU,CAAC4C,QAAQ,CAACvC,WAAW,EAAEuC,QAAQ,CAAC1C,OAAO,EAAE0C,QAAQ,CAACnC,WAAW,GAAGsD,eAAe,EAAEnB,QAAQ,CAACrC,OAAO,CAAC;EAC3H;EACA,OAAO8C,UAAUA,CAACW,IAAI,EAAEjD,MAAM,EAAE;IAC5B,MAAMkD,OAAO,GAAGD,IAAI,CAACzD,OAAO,CAAC2D,MAAM,CAAC,CAAC,EAAEnD,MAAM,CAAC;IAC9C,MAAMoD,QAAQ,GAAGH,IAAI,CAACzD,OAAO,CAAC2D,MAAM,CAACnD,MAAM,CAAC;IAC5C,OAAO,CACH,IAAIf,UAAU,CAACgE,IAAI,CAAC3D,WAAW,EAAE2D,IAAI,CAAC9D,OAAO,EAAE8D,IAAI,CAACvD,WAAW,EAAEwD,OAAO,CAAC,EACzE,IAAIjE,UAAU,CAACgE,IAAI,CAAC5D,MAAM,EAAE,EAAE,EAAE4D,IAAI,CAACvD,WAAW,GAAGM,MAAM,EAAEoD,QAAQ,CAAC,CACvE;EACL;EACA,OAAOf,UAAUA,CAACY,IAAI,EAAEjD,MAAM,EAAE;IAC5B,MAAMkD,OAAO,GAAGD,IAAI,CAAC9D,OAAO,CAACgE,MAAM,CAAC,CAAC,EAAEnD,MAAM,CAAC;IAC9C,MAAMoD,QAAQ,GAAGH,IAAI,CAAC9D,OAAO,CAACgE,MAAM,CAACnD,MAAM,CAAC;IAC5C,OAAO,CACH,IAAIf,UAAU,CAACgE,IAAI,CAAC3D,WAAW,EAAE4D,OAAO,EAAED,IAAI,CAACvD,WAAW,EAAEuD,IAAI,CAACzD,OAAO,CAAC,EACzE,IAAIP,UAAU,CAACgE,IAAI,CAAC3D,WAAW,GAAGU,MAAM,EAAEoD,QAAQ,EAAEH,IAAI,CAACxD,MAAM,EAAE,EAAE,CAAC,CACvE;EACL;EACA,OAAOiD,MAAMA,CAACW,KAAK,EAAE;IACjB,IAAIA,KAAK,CAACjE,MAAM,KAAK,CAAC,EAAE;MACpB,OAAOiE,KAAK;IAChB;IACA,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,IAAI,GAAGH,KAAK,CAAC,CAAC,CAAC;IACnB,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,KAAK,CAACjE,MAAM,EAAEe,CAAC,EAAE,EAAE;MACnC,MAAMsD,IAAI,GAAGJ,KAAK,CAAClD,CAAC,CAAC;MACrB,IAAIqD,IAAI,CAACnE,MAAM,KAAKoE,IAAI,CAACnE,WAAW,EAAE;QAClC;QACAkE,IAAI,GAAG,IAAIvE,UAAU,CAACuE,IAAI,CAAClE,WAAW,EAAEkE,IAAI,CAACrE,OAAO,GAAGsE,IAAI,CAACtE,OAAO,EAAEqE,IAAI,CAAC9D,WAAW,EAAE8D,IAAI,CAAChE,OAAO,GAAGiE,IAAI,CAACjE,OAAO,CAAC;MACvH,CAAC,MACI;QACD8D,MAAM,CAACC,SAAS,EAAE,CAAC,GAAGC,IAAI;QAC1BA,IAAI,GAAGC,IAAI;MACf;IACJ;IACAH,MAAM,CAACC,SAAS,EAAE,CAAC,GAAGC,IAAI;IAC1B,OAAOF,MAAM;EACjB;EACA,OAAOV,YAAYA,CAACS,KAAK,EAAE;IACvB,IAAIA,KAAK,CAACjE,MAAM,KAAK,CAAC,EAAE;MACpB,OAAOiE,KAAK;IAChB;IACA,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,KAAK,CAACjE,MAAM,EAAEe,CAAC,EAAE,EAAE;MACnC,MAAM8C,IAAI,GAAGI,KAAK,CAAClD,CAAC,CAAC;MACrB,IAAI8C,IAAI,CAAC9D,OAAO,KAAK8D,IAAI,CAACzD,OAAO,EAAE;QAC/B;MACJ;MACA8D,MAAM,CAACC,SAAS,EAAE,CAAC,GAAGN,IAAI;IAC9B;IACA,OAAOK,MAAM;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}