{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as DOM from './dom.js';\nexport function renderText(text, options = {}) {\n  const element = createElement(options);\n  element.textContent = text;\n  return element;\n}\nexport function renderFormattedText(formattedText, options = {}) {\n  const element = createElement(options);\n  _renderFormattedText(element, parseFormattedText(formattedText, !!options.renderCodeSegments), options.actionHandler, options.renderCodeSegments);\n  return element;\n}\nexport function createElement(options) {\n  const tagName = options.inline ? 'span' : 'div';\n  const element = document.createElement(tagName);\n  if (options.className) {\n    element.className = options.className;\n  }\n  return element;\n}\nclass StringStream {\n  constructor(source) {\n    this.source = source;\n    this.index = 0;\n  }\n  eos() {\n    return this.index >= this.source.length;\n  }\n  next() {\n    const next = this.peek();\n    this.advance();\n    return next;\n  }\n  peek() {\n    return this.source[this.index];\n  }\n  advance() {\n    this.index++;\n  }\n}\nfunction _renderFormattedText(element, treeNode, actionHandler, renderCodeSegments) {\n  let child;\n  if (treeNode.type === 2 /* FormatType.Text */) {\n    child = document.createTextNode(treeNode.content || '');\n  } else if (treeNode.type === 3 /* FormatType.Bold */) {\n    child = document.createElement('b');\n  } else if (treeNode.type === 4 /* FormatType.Italics */) {\n    child = document.createElement('i');\n  } else if (treeNode.type === 7 /* FormatType.Code */ && renderCodeSegments) {\n    child = document.createElement('code');\n  } else if (treeNode.type === 5 /* FormatType.Action */ && actionHandler) {\n    const a = document.createElement('a');\n    actionHandler.disposables.add(DOM.addStandardDisposableListener(a, 'click', event => {\n      actionHandler.callback(String(treeNode.index), event);\n    }));\n    child = a;\n  } else if (treeNode.type === 8 /* FormatType.NewLine */) {\n    child = document.createElement('br');\n  } else if (treeNode.type === 1 /* FormatType.Root */) {\n    child = element;\n  }\n  if (child && element !== child) {\n    element.appendChild(child);\n  }\n  if (child && Array.isArray(treeNode.children)) {\n    treeNode.children.forEach(nodeChild => {\n      _renderFormattedText(child, nodeChild, actionHandler, renderCodeSegments);\n    });\n  }\n}\nfunction parseFormattedText(content, parseCodeSegments) {\n  const root = {\n    type: 1 /* FormatType.Root */,\n    children: []\n  };\n  let actionViewItemIndex = 0;\n  let current = root;\n  const stack = [];\n  const stream = new StringStream(content);\n  while (!stream.eos()) {\n    let next = stream.next();\n    const isEscapedFormatType = next === '\\\\' && formatTagType(stream.peek(), parseCodeSegments) !== 0 /* FormatType.Invalid */;\n    if (isEscapedFormatType) {\n      next = stream.next(); // unread the backslash if it escapes a format tag type\n    }\n    if (!isEscapedFormatType && isFormatTag(next, parseCodeSegments) && next === stream.peek()) {\n      stream.advance();\n      if (current.type === 2 /* FormatType.Text */) {\n        current = stack.pop();\n      }\n      const type = formatTagType(next, parseCodeSegments);\n      if (current.type === type || current.type === 5 /* FormatType.Action */ && type === 6 /* FormatType.ActionClose */) {\n        current = stack.pop();\n      } else {\n        const newCurrent = {\n          type: type,\n          children: []\n        };\n        if (type === 5 /* FormatType.Action */) {\n          newCurrent.index = actionViewItemIndex;\n          actionViewItemIndex++;\n        }\n        current.children.push(newCurrent);\n        stack.push(current);\n        current = newCurrent;\n      }\n    } else if (next === '\\n') {\n      if (current.type === 2 /* FormatType.Text */) {\n        current = stack.pop();\n      }\n      current.children.push({\n        type: 8 /* FormatType.NewLine */\n      });\n    } else {\n      if (current.type !== 2 /* FormatType.Text */) {\n        const textCurrent = {\n          type: 2 /* FormatType.Text */,\n          content: next\n        };\n        current.children.push(textCurrent);\n        stack.push(current);\n        current = textCurrent;\n      } else {\n        current.content += next;\n      }\n    }\n  }\n  if (current.type === 2 /* FormatType.Text */) {\n    current = stack.pop();\n  }\n  if (stack.length) {\n    // incorrectly formatted string literal\n  }\n  return root;\n}\nfunction isFormatTag(char, supportCodeSegments) {\n  return formatTagType(char, supportCodeSegments) !== 0 /* FormatType.Invalid */;\n}\nfunction formatTagType(char, supportCodeSegments) {\n  switch (char) {\n    case '*':\n      return 3 /* FormatType.Bold */;\n    case '_':\n      return 4 /* FormatType.Italics */;\n    case '[':\n      return 5 /* FormatType.Action */;\n    case ']':\n      return 6 /* FormatType.ActionClose */;\n    case '`':\n      return supportCodeSegments ? 7 /* FormatType.Code */ : 0 /* FormatType.Invalid */;\n    default:\n      return 0 /* FormatType.Invalid */;\n  }\n}", "map": {"version": 3, "names": ["DOM", "renderText", "text", "options", "element", "createElement", "textContent", "renderFormattedText", "formattedText", "_renderFormattedText", "parseFormattedText", "renderCodeSegments", "actionHandler", "tagName", "inline", "document", "className", "StringStream", "constructor", "source", "index", "eos", "length", "next", "peek", "advance", "treeNode", "child", "type", "createTextNode", "content", "a", "disposables", "add", "addStandardDisposableListener", "event", "callback", "String", "append<PERSON><PERSON><PERSON>", "Array", "isArray", "children", "for<PERSON>ach", "node<PERSON><PERSON>d", "parseCodeSegments", "root", "actionViewItemIndex", "current", "stack", "stream", "isEscapedFormatType", "formatTagType", "isFormatTag", "pop", "newCurrent", "push", "textCurrent", "char", "supportCodeSegments"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/formattedTextRenderer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as DOM from './dom.js';\nexport function renderText(text, options = {}) {\n    const element = createElement(options);\n    element.textContent = text;\n    return element;\n}\nexport function renderFormattedText(formattedText, options = {}) {\n    const element = createElement(options);\n    _renderFormattedText(element, parseFormattedText(formattedText, !!options.renderCodeSegments), options.actionHandler, options.renderCodeSegments);\n    return element;\n}\nexport function createElement(options) {\n    const tagName = options.inline ? 'span' : 'div';\n    const element = document.createElement(tagName);\n    if (options.className) {\n        element.className = options.className;\n    }\n    return element;\n}\nclass StringStream {\n    constructor(source) {\n        this.source = source;\n        this.index = 0;\n    }\n    eos() {\n        return this.index >= this.source.length;\n    }\n    next() {\n        const next = this.peek();\n        this.advance();\n        return next;\n    }\n    peek() {\n        return this.source[this.index];\n    }\n    advance() {\n        this.index++;\n    }\n}\nfunction _renderFormattedText(element, treeNode, actionHandler, renderCodeSegments) {\n    let child;\n    if (treeNode.type === 2 /* FormatType.Text */) {\n        child = document.createTextNode(treeNode.content || '');\n    }\n    else if (treeNode.type === 3 /* FormatType.Bold */) {\n        child = document.createElement('b');\n    }\n    else if (treeNode.type === 4 /* FormatType.Italics */) {\n        child = document.createElement('i');\n    }\n    else if (treeNode.type === 7 /* FormatType.Code */ && renderCodeSegments) {\n        child = document.createElement('code');\n    }\n    else if (treeNode.type === 5 /* FormatType.Action */ && actionHandler) {\n        const a = document.createElement('a');\n        actionHandler.disposables.add(DOM.addStandardDisposableListener(a, 'click', (event) => {\n            actionHandler.callback(String(treeNode.index), event);\n        }));\n        child = a;\n    }\n    else if (treeNode.type === 8 /* FormatType.NewLine */) {\n        child = document.createElement('br');\n    }\n    else if (treeNode.type === 1 /* FormatType.Root */) {\n        child = element;\n    }\n    if (child && element !== child) {\n        element.appendChild(child);\n    }\n    if (child && Array.isArray(treeNode.children)) {\n        treeNode.children.forEach((nodeChild) => {\n            _renderFormattedText(child, nodeChild, actionHandler, renderCodeSegments);\n        });\n    }\n}\nfunction parseFormattedText(content, parseCodeSegments) {\n    const root = {\n        type: 1 /* FormatType.Root */,\n        children: []\n    };\n    let actionViewItemIndex = 0;\n    let current = root;\n    const stack = [];\n    const stream = new StringStream(content);\n    while (!stream.eos()) {\n        let next = stream.next();\n        const isEscapedFormatType = (next === '\\\\' && formatTagType(stream.peek(), parseCodeSegments) !== 0 /* FormatType.Invalid */);\n        if (isEscapedFormatType) {\n            next = stream.next(); // unread the backslash if it escapes a format tag type\n        }\n        if (!isEscapedFormatType && isFormatTag(next, parseCodeSegments) && next === stream.peek()) {\n            stream.advance();\n            if (current.type === 2 /* FormatType.Text */) {\n                current = stack.pop();\n            }\n            const type = formatTagType(next, parseCodeSegments);\n            if (current.type === type || (current.type === 5 /* FormatType.Action */ && type === 6 /* FormatType.ActionClose */)) {\n                current = stack.pop();\n            }\n            else {\n                const newCurrent = {\n                    type: type,\n                    children: []\n                };\n                if (type === 5 /* FormatType.Action */) {\n                    newCurrent.index = actionViewItemIndex;\n                    actionViewItemIndex++;\n                }\n                current.children.push(newCurrent);\n                stack.push(current);\n                current = newCurrent;\n            }\n        }\n        else if (next === '\\n') {\n            if (current.type === 2 /* FormatType.Text */) {\n                current = stack.pop();\n            }\n            current.children.push({\n                type: 8 /* FormatType.NewLine */\n            });\n        }\n        else {\n            if (current.type !== 2 /* FormatType.Text */) {\n                const textCurrent = {\n                    type: 2 /* FormatType.Text */,\n                    content: next\n                };\n                current.children.push(textCurrent);\n                stack.push(current);\n                current = textCurrent;\n            }\n            else {\n                current.content += next;\n            }\n        }\n    }\n    if (current.type === 2 /* FormatType.Text */) {\n        current = stack.pop();\n    }\n    if (stack.length) {\n        // incorrectly formatted string literal\n    }\n    return root;\n}\nfunction isFormatTag(char, supportCodeSegments) {\n    return formatTagType(char, supportCodeSegments) !== 0 /* FormatType.Invalid */;\n}\nfunction formatTagType(char, supportCodeSegments) {\n    switch (char) {\n        case '*':\n            return 3 /* FormatType.Bold */;\n        case '_':\n            return 4 /* FormatType.Italics */;\n        case '[':\n            return 5 /* FormatType.Action */;\n        case ']':\n            return 6 /* FormatType.ActionClose */;\n        case '`':\n            return supportCodeSegments ? 7 /* FormatType.Code */ : 0 /* FormatType.Invalid */;\n        default:\n            return 0 /* FormatType.Invalid */;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,UAAU;AAC/B,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC3C,MAAMC,OAAO,GAAGC,aAAa,CAACF,OAAO,CAAC;EACtCC,OAAO,CAACE,WAAW,GAAGJ,IAAI;EAC1B,OAAOE,OAAO;AAClB;AACA,OAAO,SAASG,mBAAmBA,CAACC,aAAa,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;EAC7D,MAAMC,OAAO,GAAGC,aAAa,CAACF,OAAO,CAAC;EACtCM,oBAAoB,CAACL,OAAO,EAAEM,kBAAkB,CAACF,aAAa,EAAE,CAAC,CAACL,OAAO,CAACQ,kBAAkB,CAAC,EAAER,OAAO,CAACS,aAAa,EAAET,OAAO,CAACQ,kBAAkB,CAAC;EACjJ,OAAOP,OAAO;AAClB;AACA,OAAO,SAASC,aAAaA,CAACF,OAAO,EAAE;EACnC,MAAMU,OAAO,GAAGV,OAAO,CAACW,MAAM,GAAG,MAAM,GAAG,KAAK;EAC/C,MAAMV,OAAO,GAAGW,QAAQ,CAACV,aAAa,CAACQ,OAAO,CAAC;EAC/C,IAAIV,OAAO,CAACa,SAAS,EAAE;IACnBZ,OAAO,CAACY,SAAS,GAAGb,OAAO,CAACa,SAAS;EACzC;EACA,OAAOZ,OAAO;AAClB;AACA,MAAMa,YAAY,CAAC;EACfC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACD,KAAK,IAAI,IAAI,CAACD,MAAM,CAACG,MAAM;EAC3C;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMA,IAAI,GAAG,IAAI,CAACC,IAAI,CAAC,CAAC;IACxB,IAAI,CAACC,OAAO,CAAC,CAAC;IACd,OAAOF,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACL,MAAM,CAAC,IAAI,CAACC,KAAK,CAAC;EAClC;EACAK,OAAOA,CAAA,EAAG;IACN,IAAI,CAACL,KAAK,EAAE;EAChB;AACJ;AACA,SAASX,oBAAoBA,CAACL,OAAO,EAAEsB,QAAQ,EAAEd,aAAa,EAAED,kBAAkB,EAAE;EAChF,IAAIgB,KAAK;EACT,IAAID,QAAQ,CAACE,IAAI,KAAK,CAAC,CAAC,uBAAuB;IAC3CD,KAAK,GAAGZ,QAAQ,CAACc,cAAc,CAACH,QAAQ,CAACI,OAAO,IAAI,EAAE,CAAC;EAC3D,CAAC,MACI,IAAIJ,QAAQ,CAACE,IAAI,KAAK,CAAC,CAAC,uBAAuB;IAChDD,KAAK,GAAGZ,QAAQ,CAACV,aAAa,CAAC,GAAG,CAAC;EACvC,CAAC,MACI,IAAIqB,QAAQ,CAACE,IAAI,KAAK,CAAC,CAAC,0BAA0B;IACnDD,KAAK,GAAGZ,QAAQ,CAACV,aAAa,CAAC,GAAG,CAAC;EACvC,CAAC,MACI,IAAIqB,QAAQ,CAACE,IAAI,KAAK,CAAC,CAAC,yBAAyBjB,kBAAkB,EAAE;IACtEgB,KAAK,GAAGZ,QAAQ,CAACV,aAAa,CAAC,MAAM,CAAC;EAC1C,CAAC,MACI,IAAIqB,QAAQ,CAACE,IAAI,KAAK,CAAC,CAAC,2BAA2BhB,aAAa,EAAE;IACnE,MAAMmB,CAAC,GAAGhB,QAAQ,CAACV,aAAa,CAAC,GAAG,CAAC;IACrCO,aAAa,CAACoB,WAAW,CAACC,GAAG,CAACjC,GAAG,CAACkC,6BAA6B,CAACH,CAAC,EAAE,OAAO,EAAGI,KAAK,IAAK;MACnFvB,aAAa,CAACwB,QAAQ,CAACC,MAAM,CAACX,QAAQ,CAACN,KAAK,CAAC,EAAEe,KAAK,CAAC;IACzD,CAAC,CAAC,CAAC;IACHR,KAAK,GAAGI,CAAC;EACb,CAAC,MACI,IAAIL,QAAQ,CAACE,IAAI,KAAK,CAAC,CAAC,0BAA0B;IACnDD,KAAK,GAAGZ,QAAQ,CAACV,aAAa,CAAC,IAAI,CAAC;EACxC,CAAC,MACI,IAAIqB,QAAQ,CAACE,IAAI,KAAK,CAAC,CAAC,uBAAuB;IAChDD,KAAK,GAAGvB,OAAO;EACnB;EACA,IAAIuB,KAAK,IAAIvB,OAAO,KAAKuB,KAAK,EAAE;IAC5BvB,OAAO,CAACkC,WAAW,CAACX,KAAK,CAAC;EAC9B;EACA,IAAIA,KAAK,IAAIY,KAAK,CAACC,OAAO,CAACd,QAAQ,CAACe,QAAQ,CAAC,EAAE;IAC3Cf,QAAQ,CAACe,QAAQ,CAACC,OAAO,CAAEC,SAAS,IAAK;MACrClC,oBAAoB,CAACkB,KAAK,EAAEgB,SAAS,EAAE/B,aAAa,EAAED,kBAAkB,CAAC;IAC7E,CAAC,CAAC;EACN;AACJ;AACA,SAASD,kBAAkBA,CAACoB,OAAO,EAAEc,iBAAiB,EAAE;EACpD,MAAMC,IAAI,GAAG;IACTjB,IAAI,EAAE,CAAC,CAAC;IACRa,QAAQ,EAAE;EACd,CAAC;EACD,IAAIK,mBAAmB,GAAG,CAAC;EAC3B,IAAIC,OAAO,GAAGF,IAAI;EAClB,MAAMG,KAAK,GAAG,EAAE;EAChB,MAAMC,MAAM,GAAG,IAAIhC,YAAY,CAACa,OAAO,CAAC;EACxC,OAAO,CAACmB,MAAM,CAAC5B,GAAG,CAAC,CAAC,EAAE;IAClB,IAAIE,IAAI,GAAG0B,MAAM,CAAC1B,IAAI,CAAC,CAAC;IACxB,MAAM2B,mBAAmB,GAAI3B,IAAI,KAAK,IAAI,IAAI4B,aAAa,CAACF,MAAM,CAACzB,IAAI,CAAC,CAAC,EAAEoB,iBAAiB,CAAC,KAAK,CAAC,CAAC,wBAAyB;IAC7H,IAAIM,mBAAmB,EAAE;MACrB3B,IAAI,GAAG0B,MAAM,CAAC1B,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1B;IACA,IAAI,CAAC2B,mBAAmB,IAAIE,WAAW,CAAC7B,IAAI,EAAEqB,iBAAiB,CAAC,IAAIrB,IAAI,KAAK0B,MAAM,CAACzB,IAAI,CAAC,CAAC,EAAE;MACxFyB,MAAM,CAACxB,OAAO,CAAC,CAAC;MAChB,IAAIsB,OAAO,CAACnB,IAAI,KAAK,CAAC,CAAC,uBAAuB;QAC1CmB,OAAO,GAAGC,KAAK,CAACK,GAAG,CAAC,CAAC;MACzB;MACA,MAAMzB,IAAI,GAAGuB,aAAa,CAAC5B,IAAI,EAAEqB,iBAAiB,CAAC;MACnD,IAAIG,OAAO,CAACnB,IAAI,KAAKA,IAAI,IAAKmB,OAAO,CAACnB,IAAI,KAAK,CAAC,CAAC,2BAA2BA,IAAI,KAAK,CAAC,CAAC,4BAA6B,EAAE;QAClHmB,OAAO,GAAGC,KAAK,CAACK,GAAG,CAAC,CAAC;MACzB,CAAC,MACI;QACD,MAAMC,UAAU,GAAG;UACf1B,IAAI,EAAEA,IAAI;UACVa,QAAQ,EAAE;QACd,CAAC;QACD,IAAIb,IAAI,KAAK,CAAC,CAAC,yBAAyB;UACpC0B,UAAU,CAAClC,KAAK,GAAG0B,mBAAmB;UACtCA,mBAAmB,EAAE;QACzB;QACAC,OAAO,CAACN,QAAQ,CAACc,IAAI,CAACD,UAAU,CAAC;QACjCN,KAAK,CAACO,IAAI,CAACR,OAAO,CAAC;QACnBA,OAAO,GAAGO,UAAU;MACxB;IACJ,CAAC,MACI,IAAI/B,IAAI,KAAK,IAAI,EAAE;MACpB,IAAIwB,OAAO,CAACnB,IAAI,KAAK,CAAC,CAAC,uBAAuB;QAC1CmB,OAAO,GAAGC,KAAK,CAACK,GAAG,CAAC,CAAC;MACzB;MACAN,OAAO,CAACN,QAAQ,CAACc,IAAI,CAAC;QAClB3B,IAAI,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAImB,OAAO,CAACnB,IAAI,KAAK,CAAC,CAAC,uBAAuB;QAC1C,MAAM4B,WAAW,GAAG;UAChB5B,IAAI,EAAE,CAAC,CAAC;UACRE,OAAO,EAAEP;QACb,CAAC;QACDwB,OAAO,CAACN,QAAQ,CAACc,IAAI,CAACC,WAAW,CAAC;QAClCR,KAAK,CAACO,IAAI,CAACR,OAAO,CAAC;QACnBA,OAAO,GAAGS,WAAW;MACzB,CAAC,MACI;QACDT,OAAO,CAACjB,OAAO,IAAIP,IAAI;MAC3B;IACJ;EACJ;EACA,IAAIwB,OAAO,CAACnB,IAAI,KAAK,CAAC,CAAC,uBAAuB;IAC1CmB,OAAO,GAAGC,KAAK,CAACK,GAAG,CAAC,CAAC;EACzB;EACA,IAAIL,KAAK,CAAC1B,MAAM,EAAE;IACd;EAAA;EAEJ,OAAOuB,IAAI;AACf;AACA,SAASO,WAAWA,CAACK,IAAI,EAAEC,mBAAmB,EAAE;EAC5C,OAAOP,aAAa,CAACM,IAAI,EAAEC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC1D;AACA,SAASP,aAAaA,CAACM,IAAI,EAAEC,mBAAmB,EAAE;EAC9C,QAAQD,IAAI;IACR,KAAK,GAAG;MACJ,OAAO,CAAC,CAAC;IACb,KAAK,GAAG;MACJ,OAAO,CAAC,CAAC;IACb,KAAK,GAAG;MACJ,OAAO,CAAC,CAAC;IACb,KAAK,GAAG;MACJ,OAAO,CAAC,CAAC;IACb,KAAK,GAAG;MACJ,OAAOC,mBAAmB,GAAG,CAAC,CAAC,wBAAwB,CAAC,CAAC;IAC7D;MACI,OAAO,CAAC,CAAC;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}