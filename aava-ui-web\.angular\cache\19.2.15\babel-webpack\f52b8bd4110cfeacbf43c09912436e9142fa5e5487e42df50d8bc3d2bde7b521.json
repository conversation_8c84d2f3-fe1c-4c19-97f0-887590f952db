{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { $, addDisposableListener, append, asCSSUrl, EventType, ModifierKeyEmitter, prepend } from '../../../base/browser/dom.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { ActionViewItem, BaseActionViewItem, SelectActionViewItem } from '../../../base/browser/ui/actionbar/actionViewItems.js';\nimport { DropdownMenuActionViewItem } from '../../../base/browser/ui/dropdown/dropdownActionViewItem.js';\nimport { ActionRunner, Separator, SubmenuAction } from '../../../base/common/actions.js';\nimport { UILabelProvider } from '../../../base/common/keybindingLabels.js';\nimport { combinedDisposable, MutableDisposable, toDisposable } from '../../../base/common/lifecycle.js';\nimport { isLinux, isWindows, OS } from '../../../base/common/platform.js';\nimport './menuEntryActionViewItem.css';\nimport { localize } from '../../../nls.js';\nimport { IMenuService, MenuItemAction, SubmenuItemAction } from '../common/actions.js';\nimport { isICommandActionToggleInfo } from '../../action/common/action.js';\nimport { IContextKeyService } from '../../contextkey/common/contextkey.js';\nimport { IContextMenuService, IContextViewService } from '../../contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../keybinding/common/keybinding.js';\nimport { INotificationService } from '../../notification/common/notification.js';\nimport { IStorageService } from '../../storage/common/storage.js';\nimport { IThemeService } from '../../theme/common/themeService.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport { isDark } from '../../theme/common/theme.js';\nimport { assertType } from '../../../base/common/types.js';\nimport { asCssVariable, selectBorder } from '../../theme/common/colorRegistry.js';\nimport { defaultSelectBoxStyles } from '../../theme/browser/defaultStyles.js';\nimport { IAccessibilityService } from '../../accessibility/common/accessibility.js';\nexport function createAndFillInContextMenuActions(menu, optionsOrTarget, targetOrPrimaryGroup, primaryGroupOrUndefined) {\n  let target;\n  let primaryGroup;\n  let groups;\n  if (Array.isArray(menu)) {\n    groups = menu;\n    target = optionsOrTarget;\n    primaryGroup = targetOrPrimaryGroup;\n  } else {\n    const options = optionsOrTarget;\n    groups = menu.getActions(options);\n    target = targetOrPrimaryGroup;\n    primaryGroup = primaryGroupOrUndefined;\n  }\n  const modifierKeyEmitter = ModifierKeyEmitter.getInstance();\n  const useAlternativeActions = modifierKeyEmitter.keyStatus.altKey || (isWindows || isLinux) && modifierKeyEmitter.keyStatus.shiftKey;\n  fillInActions(groups, target, useAlternativeActions, primaryGroup ? actionGroup => actionGroup === primaryGroup : actionGroup => actionGroup === 'navigation');\n}\nexport function createAndFillInActionBarActions(menu, optionsOrTarget, targetOrPrimaryGroup, primaryGroupOrShouldInlineSubmenu, shouldInlineSubmenuOrUseSeparatorsInPrimaryActions, useSeparatorsInPrimaryActionsOrUndefined) {\n  let target;\n  let primaryGroup;\n  let shouldInlineSubmenu;\n  let useSeparatorsInPrimaryActions;\n  let groups;\n  if (Array.isArray(menu)) {\n    groups = menu;\n    target = optionsOrTarget;\n    primaryGroup = targetOrPrimaryGroup;\n    shouldInlineSubmenu = primaryGroupOrShouldInlineSubmenu;\n    useSeparatorsInPrimaryActions = shouldInlineSubmenuOrUseSeparatorsInPrimaryActions;\n  } else {\n    const options = optionsOrTarget;\n    groups = menu.getActions(options);\n    target = targetOrPrimaryGroup;\n    primaryGroup = primaryGroupOrShouldInlineSubmenu;\n    shouldInlineSubmenu = shouldInlineSubmenuOrUseSeparatorsInPrimaryActions;\n    useSeparatorsInPrimaryActions = useSeparatorsInPrimaryActionsOrUndefined;\n  }\n  const isPrimaryAction = typeof primaryGroup === 'string' ? actionGroup => actionGroup === primaryGroup : primaryGroup;\n  // Action bars handle alternative actions on their own so the alternative actions should be ignored\n  fillInActions(groups, target, false, isPrimaryAction, shouldInlineSubmenu, useSeparatorsInPrimaryActions);\n}\nfunction fillInActions(groups, target, useAlternativeActions, isPrimaryAction = actionGroup => actionGroup === 'navigation', shouldInlineSubmenu = () => false, useSeparatorsInPrimaryActions = false) {\n  let primaryBucket;\n  let secondaryBucket;\n  if (Array.isArray(target)) {\n    primaryBucket = target;\n    secondaryBucket = target;\n  } else {\n    primaryBucket = target.primary;\n    secondaryBucket = target.secondary;\n  }\n  const submenuInfo = new Set();\n  for (const [group, actions] of groups) {\n    let target;\n    if (isPrimaryAction(group)) {\n      target = primaryBucket;\n      if (target.length > 0 && useSeparatorsInPrimaryActions) {\n        target.push(new Separator());\n      }\n    } else {\n      target = secondaryBucket;\n      if (target.length > 0) {\n        target.push(new Separator());\n      }\n    }\n    for (let action of actions) {\n      if (useAlternativeActions) {\n        action = action instanceof MenuItemAction && action.alt ? action.alt : action;\n      }\n      const newLen = target.push(action);\n      // keep submenu info for later inlining\n      if (action instanceof SubmenuAction) {\n        submenuInfo.add({\n          group,\n          action,\n          index: newLen - 1\n        });\n      }\n    }\n  }\n  // ask the outside if submenu should be inlined or not. only ask when\n  // there would be enough space\n  for (const {\n    group,\n    action,\n    index\n  } of submenuInfo) {\n    const target = isPrimaryAction(group) ? primaryBucket : secondaryBucket;\n    // inlining submenus with length 0 or 1 is easy,\n    // larger submenus need to be checked with the overall limit\n    const submenuActions = action.actions;\n    if (shouldInlineSubmenu(action, group, target.length)) {\n      target.splice(index, 1, ...submenuActions);\n    }\n  }\n}\nlet MenuEntryActionViewItem = class MenuEntryActionViewItem extends ActionViewItem {\n  constructor(action, _options, _keybindingService, _notificationService, _contextKeyService, _themeService, _contextMenuService, _accessibilityService) {\n    super(undefined, action, {\n      icon: !!(action.class || action.item.icon),\n      label: !action.class && !action.item.icon,\n      draggable: _options?.draggable,\n      keybinding: _options?.keybinding,\n      hoverDelegate: _options?.hoverDelegate\n    });\n    this._options = _options;\n    this._keybindingService = _keybindingService;\n    this._notificationService = _notificationService;\n    this._contextKeyService = _contextKeyService;\n    this._themeService = _themeService;\n    this._contextMenuService = _contextMenuService;\n    this._accessibilityService = _accessibilityService;\n    this._wantsAltCommand = false;\n    this._itemClassDispose = this._register(new MutableDisposable());\n    this._altKey = ModifierKeyEmitter.getInstance();\n  }\n  get _menuItemAction() {\n    return this._action;\n  }\n  get _commandAction() {\n    return this._wantsAltCommand && this._menuItemAction.alt || this._menuItemAction;\n  }\n  onClick(event) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      event.preventDefault();\n      event.stopPropagation();\n      try {\n        yield _this.actionRunner.run(_this._commandAction, _this._context);\n      } catch (err) {\n        _this._notificationService.error(err);\n      }\n    })();\n  }\n  render(container) {\n    super.render(container);\n    container.classList.add('menu-entry');\n    if (this.options.icon) {\n      this._updateItemClass(this._menuItemAction.item);\n    }\n    if (this._menuItemAction.alt) {\n      let isMouseOver = false;\n      const updateAltState = () => {\n        const wantsAltCommand = !!this._menuItemAction.alt?.enabled && (!this._accessibilityService.isMotionReduced() || isMouseOver) && (this._altKey.keyStatus.altKey || this._altKey.keyStatus.shiftKey && isMouseOver);\n        if (wantsAltCommand !== this._wantsAltCommand) {\n          this._wantsAltCommand = wantsAltCommand;\n          this.updateLabel();\n          this.updateTooltip();\n          this.updateClass();\n        }\n      };\n      this._register(this._altKey.event(updateAltState));\n      this._register(addDisposableListener(container, 'mouseleave', _ => {\n        isMouseOver = false;\n        updateAltState();\n      }));\n      this._register(addDisposableListener(container, 'mouseenter', _ => {\n        isMouseOver = true;\n        updateAltState();\n      }));\n      updateAltState();\n    }\n  }\n  updateLabel() {\n    if (this.options.label && this.label) {\n      this.label.textContent = this._commandAction.label;\n    }\n  }\n  getTooltip() {\n    const keybinding = this._keybindingService.lookupKeybinding(this._commandAction.id, this._contextKeyService);\n    const keybindingLabel = keybinding && keybinding.getLabel();\n    const tooltip = this._commandAction.tooltip || this._commandAction.label;\n    let title = keybindingLabel ? localize('titleAndKb', \"{0} ({1})\", tooltip, keybindingLabel) : tooltip;\n    if (!this._wantsAltCommand && this._menuItemAction.alt?.enabled) {\n      const altTooltip = this._menuItemAction.alt.tooltip || this._menuItemAction.alt.label;\n      const altKeybinding = this._keybindingService.lookupKeybinding(this._menuItemAction.alt.id, this._contextKeyService);\n      const altKeybindingLabel = altKeybinding && altKeybinding.getLabel();\n      const altTitleSection = altKeybindingLabel ? localize('titleAndKb', \"{0} ({1})\", altTooltip, altKeybindingLabel) : altTooltip;\n      title = localize('titleAndKbAndAlt', \"{0}\\n[{1}] {2}\", title, UILabelProvider.modifierLabels[OS].altKey, altTitleSection);\n    }\n    return title;\n  }\n  updateClass() {\n    if (this.options.icon) {\n      if (this._commandAction !== this._menuItemAction) {\n        if (this._menuItemAction.alt) {\n          this._updateItemClass(this._menuItemAction.alt.item);\n        }\n      } else {\n        this._updateItemClass(this._menuItemAction.item);\n      }\n    }\n  }\n  _updateItemClass(item) {\n    this._itemClassDispose.value = undefined;\n    const {\n      element,\n      label\n    } = this;\n    if (!element || !label) {\n      return;\n    }\n    const icon = this._commandAction.checked && isICommandActionToggleInfo(item.toggled) && item.toggled.icon ? item.toggled.icon : item.icon;\n    if (!icon) {\n      return;\n    }\n    if (ThemeIcon.isThemeIcon(icon)) {\n      // theme icons\n      const iconClasses = ThemeIcon.asClassNameArray(icon);\n      label.classList.add(...iconClasses);\n      this._itemClassDispose.value = toDisposable(() => {\n        label.classList.remove(...iconClasses);\n      });\n    } else {\n      // icon path/url\n      label.style.backgroundImage = isDark(this._themeService.getColorTheme().type) ? asCSSUrl(icon.dark) : asCSSUrl(icon.light);\n      label.classList.add('icon');\n      this._itemClassDispose.value = combinedDisposable(toDisposable(() => {\n        label.style.backgroundImage = '';\n        label.classList.remove('icon');\n      }), this._themeService.onDidColorThemeChange(() => {\n        // refresh when the theme changes in case we go between dark <-> light\n        this.updateClass();\n      }));\n    }\n  }\n};\nMenuEntryActionViewItem = __decorate([__param(2, IKeybindingService), __param(3, INotificationService), __param(4, IContextKeyService), __param(5, IThemeService), __param(6, IContextMenuService), __param(7, IAccessibilityService)], MenuEntryActionViewItem);\nexport { MenuEntryActionViewItem };\nexport class TextOnlyMenuEntryActionViewItem extends MenuEntryActionViewItem {\n  render(container) {\n    this.options.label = true;\n    this.options.icon = false;\n    super.render(container);\n    container.classList.add('text-only');\n    container.classList.toggle('use-comma', this._options?.useComma ?? false);\n  }\n  updateLabel() {\n    const kb = this._keybindingService.lookupKeybinding(this._action.id, this._contextKeyService);\n    if (!kb) {\n      return super.updateLabel();\n    }\n    if (this.label) {\n      const kb2 = TextOnlyMenuEntryActionViewItem._symbolPrintEnter(kb);\n      if (this._options?.conversational) {\n        this.label.textContent = localize({\n          key: 'content2',\n          comment: ['A label with keybindg like \"ESC to dismiss\"']\n        }, '{1} to {0}', this._action.label, kb2);\n      } else {\n        this.label.textContent = localize({\n          key: 'content',\n          comment: ['A label', 'A keybinding']\n        }, '{0} ({1})', this._action.label, kb2);\n      }\n    }\n  }\n  static _symbolPrintEnter(kb) {\n    return kb.getLabel()?.replace(/\\benter\\b/gi, '\\u23CE').replace(/\\bEscape\\b/gi, 'Esc');\n  }\n}\nlet SubmenuEntryActionViewItem = class SubmenuEntryActionViewItem extends DropdownMenuActionViewItem {\n  constructor(action, options, _keybindingService, _contextMenuService, _themeService) {\n    const dropdownOptions = {\n      ...options,\n      menuAsChild: options?.menuAsChild ?? false,\n      classNames: options?.classNames ?? (ThemeIcon.isThemeIcon(action.item.icon) ? ThemeIcon.asClassName(action.item.icon) : undefined),\n      keybindingProvider: options?.keybindingProvider ?? (action => _keybindingService.lookupKeybinding(action.id))\n    };\n    super(action, {\n      getActions: () => action.actions\n    }, _contextMenuService, dropdownOptions);\n    this._keybindingService = _keybindingService;\n    this._contextMenuService = _contextMenuService;\n    this._themeService = _themeService;\n  }\n  render(container) {\n    super.render(container);\n    assertType(this.element);\n    container.classList.add('menu-entry');\n    const action = this._action;\n    const {\n      icon\n    } = action.item;\n    if (icon && !ThemeIcon.isThemeIcon(icon)) {\n      this.element.classList.add('icon');\n      const setBackgroundImage = () => {\n        if (this.element) {\n          this.element.style.backgroundImage = isDark(this._themeService.getColorTheme().type) ? asCSSUrl(icon.dark) : asCSSUrl(icon.light);\n        }\n      };\n      setBackgroundImage();\n      this._register(this._themeService.onDidColorThemeChange(() => {\n        // refresh when the theme changes in case we go between dark <-> light\n        setBackgroundImage();\n      }));\n    }\n  }\n};\nSubmenuEntryActionViewItem = __decorate([__param(2, IKeybindingService), __param(3, IContextMenuService), __param(4, IThemeService)], SubmenuEntryActionViewItem);\nexport { SubmenuEntryActionViewItem };\nlet DropdownWithDefaultActionViewItem = class DropdownWithDefaultActionViewItem extends BaseActionViewItem {\n  constructor(submenuAction, options, _keybindingService, _notificationService, _contextMenuService, _menuService, _instaService, _storageService) {\n    super(null, submenuAction);\n    this._keybindingService = _keybindingService;\n    this._notificationService = _notificationService;\n    this._contextMenuService = _contextMenuService;\n    this._menuService = _menuService;\n    this._instaService = _instaService;\n    this._storageService = _storageService;\n    this._container = null;\n    this._options = options;\n    this._storageKey = `${submenuAction.item.submenu.id}_lastActionId`;\n    // determine default action\n    let defaultAction;\n    const defaultActionId = options?.persistLastActionId ? _storageService.get(this._storageKey, 1 /* StorageScope.WORKSPACE */) : undefined;\n    if (defaultActionId) {\n      defaultAction = submenuAction.actions.find(a => defaultActionId === a.id);\n    }\n    if (!defaultAction) {\n      defaultAction = submenuAction.actions[0];\n    }\n    this._defaultAction = this._instaService.createInstance(MenuEntryActionViewItem, defaultAction, {\n      keybinding: this._getDefaultActionKeybindingLabel(defaultAction)\n    });\n    const dropdownOptions = {\n      keybindingProvider: action => this._keybindingService.lookupKeybinding(action.id),\n      ...options,\n      menuAsChild: options?.menuAsChild ?? true,\n      classNames: options?.classNames ?? ['codicon', 'codicon-chevron-down'],\n      actionRunner: options?.actionRunner ?? new ActionRunner()\n    };\n    this._dropdown = new DropdownMenuActionViewItem(submenuAction, submenuAction.actions, this._contextMenuService, dropdownOptions);\n    this._register(this._dropdown.actionRunner.onDidRun(e => {\n      if (e.action instanceof MenuItemAction) {\n        this.update(e.action);\n      }\n    }));\n  }\n  update(lastAction) {\n    if (this._options?.persistLastActionId) {\n      this._storageService.store(this._storageKey, lastAction.id, 1 /* StorageScope.WORKSPACE */, 1 /* StorageTarget.MACHINE */);\n    }\n    this._defaultAction.dispose();\n    this._defaultAction = this._instaService.createInstance(MenuEntryActionViewItem, lastAction, {\n      keybinding: this._getDefaultActionKeybindingLabel(lastAction)\n    });\n    this._defaultAction.actionRunner = new class extends ActionRunner {\n      runAction(action, context) {\n        return _asyncToGenerator(function* () {\n          yield action.run(undefined);\n        })();\n      }\n    }();\n    if (this._container) {\n      this._defaultAction.render(prepend(this._container, $('.action-container')));\n    }\n  }\n  _getDefaultActionKeybindingLabel(defaultAction) {\n    let defaultActionKeybinding;\n    if (this._options?.renderKeybindingWithDefaultActionLabel) {\n      const kb = this._keybindingService.lookupKeybinding(defaultAction.id);\n      if (kb) {\n        defaultActionKeybinding = `(${kb.getLabel()})`;\n      }\n    }\n    return defaultActionKeybinding;\n  }\n  setActionContext(newContext) {\n    super.setActionContext(newContext);\n    this._defaultAction.setActionContext(newContext);\n    this._dropdown.setActionContext(newContext);\n  }\n  render(container) {\n    this._container = container;\n    super.render(this._container);\n    this._container.classList.add('monaco-dropdown-with-default');\n    const primaryContainer = $('.action-container');\n    this._defaultAction.render(append(this._container, primaryContainer));\n    this._register(addDisposableListener(primaryContainer, EventType.KEY_DOWN, e => {\n      const event = new StandardKeyboardEvent(e);\n      if (event.equals(17 /* KeyCode.RightArrow */)) {\n        this._defaultAction.element.tabIndex = -1;\n        this._dropdown.focus();\n        event.stopPropagation();\n      }\n    }));\n    const dropdownContainer = $('.dropdown-action-container');\n    this._dropdown.render(append(this._container, dropdownContainer));\n    this._register(addDisposableListener(dropdownContainer, EventType.KEY_DOWN, e => {\n      const event = new StandardKeyboardEvent(e);\n      if (event.equals(15 /* KeyCode.LeftArrow */)) {\n        this._defaultAction.element.tabIndex = 0;\n        this._dropdown.setFocusable(false);\n        this._defaultAction.element?.focus();\n        event.stopPropagation();\n      }\n    }));\n  }\n  focus(fromRight) {\n    if (fromRight) {\n      this._dropdown.focus();\n    } else {\n      this._defaultAction.element.tabIndex = 0;\n      this._defaultAction.element.focus();\n    }\n  }\n  blur() {\n    this._defaultAction.element.tabIndex = -1;\n    this._dropdown.blur();\n    this._container.blur();\n  }\n  setFocusable(focusable) {\n    if (focusable) {\n      this._defaultAction.element.tabIndex = 0;\n    } else {\n      this._defaultAction.element.tabIndex = -1;\n      this._dropdown.setFocusable(false);\n    }\n  }\n  dispose() {\n    this._defaultAction.dispose();\n    this._dropdown.dispose();\n    super.dispose();\n  }\n};\nDropdownWithDefaultActionViewItem = __decorate([__param(2, IKeybindingService), __param(3, INotificationService), __param(4, IContextMenuService), __param(5, IMenuService), __param(6, IInstantiationService), __param(7, IStorageService)], DropdownWithDefaultActionViewItem);\nexport { DropdownWithDefaultActionViewItem };\nlet SubmenuEntrySelectActionViewItem = class SubmenuEntrySelectActionViewItem extends SelectActionViewItem {\n  constructor(action, contextViewService) {\n    super(null, action, action.actions.map(a => ({\n      text: a.id === Separator.ID ? '\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500' : a.label,\n      isDisabled: !a.enabled\n    })), 0, contextViewService, defaultSelectBoxStyles, {\n      ariaLabel: action.tooltip,\n      optionsAsChildren: true\n    });\n    this.select(Math.max(0, action.actions.findIndex(a => a.checked)));\n  }\n  render(container) {\n    super.render(container);\n    container.style.borderColor = asCssVariable(selectBorder);\n  }\n  runAction(option, index) {\n    const action = this.action.actions[index];\n    if (action) {\n      this.actionRunner.run(action);\n    }\n  }\n};\nSubmenuEntrySelectActionViewItem = __decorate([__param(1, IContextViewService)], SubmenuEntrySelectActionViewItem);\n/**\n * Creates action view items for menu actions or submenu actions.\n */\nexport function createActionViewItem(instaService, action, options) {\n  if (action instanceof MenuItemAction) {\n    return instaService.createInstance(MenuEntryActionViewItem, action, options);\n  } else if (action instanceof SubmenuItemAction) {\n    if (action.item.isSelection) {\n      return instaService.createInstance(SubmenuEntrySelectActionViewItem, action);\n    } else {\n      if (action.item.rememberDefaultAction) {\n        return instaService.createInstance(DropdownWithDefaultActionViewItem, action, {\n          ...options,\n          persistLastActionId: true\n        });\n      } else {\n        return instaService.createInstance(SubmenuEntryActionViewItem, action, options);\n      }\n    }\n  } else {\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "$", "addDisposableListener", "append", "asCSSUrl", "EventType", "ModifierKeyEmitter", "prepend", "StandardKeyboardEvent", "ActionViewItem", "BaseActionViewItem", "SelectActionViewItem", "DropdownMenuActionViewItem", "ActionRunner", "Separator", "SubmenuAction", "UILabe<PERSON>", "combinedDisposable", "MutableDisposable", "toDisposable", "isLinux", "isWindows", "OS", "localize", "IMenuService", "MenuItemAction", "SubmenuItemAction", "isICommandActionToggleInfo", "IContextKeyService", "IContextMenuService", "IContextViewService", "IInstantiationService", "IKeybindingService", "INotificationService", "IStorageService", "IThemeService", "ThemeIcon", "isDark", "assertType", "asCssVariable", "selectBorder", "defaultSelectBoxStyles", "IAccessibilityService", "createAndFillInContextMenuActions", "menu", "optionsOrTarget", "targetOrPrimaryGroup", "primaryGroupOrUndefined", "primaryGroup", "groups", "Array", "isArray", "options", "getActions", "modifierKeyEmitter", "getInstance", "useAlternativeActions", "keyStatus", "altKey", "shift<PERSON>ey", "fillInActions", "actionGroup", "createAndFillInActionBarActions", "primaryGroupOrShouldInlineSubmenu", "shouldInlineSubmenuOrUseSeparatorsInPrimaryActions", "useSeparatorsInPrimaryActionsOrUndefined", "shouldInlineSubmenu", "useSeparatorsInPrimaryActions", "isPrimaryAction", "primaryBucket", "secondaryBucket", "primary", "secondary", "submenuInfo", "Set", "group", "actions", "push", "action", "alt", "newLen", "add", "index", "submenuActions", "splice", "MenuEntryActionViewItem", "constructor", "_options", "_keybindingService", "_notificationService", "_contextKeyService", "_themeService", "_contextMenuService", "_accessibilityService", "undefined", "icon", "class", "item", "label", "draggable", "keybinding", "hoverDelegate", "_wantsAltCommand", "_itemClassDispose", "_register", "_altKey", "_menuItemAction", "_action", "_commandAction", "onClick", "event", "_this", "_asyncToGenerator", "preventDefault", "stopPropagation", "actionRunner", "run", "_context", "err", "error", "render", "container", "classList", "_updateItemClass", "isMouseOver", "updateAltState", "wantsAltCommand", "enabled", "isMotionReduced", "updateLabel", "updateTooltip", "updateClass", "_", "textContent", "getTooltip", "lookupKeybinding", "id", "keybinding<PERSON>abel", "get<PERSON><PERSON><PERSON>", "tooltip", "title", "altTooltip", "altKeybinding", "altKeybindingLabel", "altTitleSection", "modifierLabels", "value", "element", "checked", "toggled", "isThemeIcon", "iconClasses", "asClassNameArray", "remove", "style", "backgroundImage", "getColorTheme", "type", "dark", "light", "onDidColorThemeChange", "TextOnlyMenuEntryActionViewItem", "toggle", "useComma", "kb", "kb2", "_symbolPrintEnter", "conversational", "comment", "replace", "SubmenuEntryActionViewItem", "dropdownOptions", "menuAsChild", "classNames", "asClassName", "keybindingProvider", "setBackgroundImage", "DropdownWithDefaultActionViewItem", "submenuAction", "_menuService", "_instaService", "_storageService", "_container", "_storageKey", "submenu", "defaultAction", "defaultActionId", "persistLastActionId", "get", "find", "a", "_defaultAction", "createInstance", "_getDefaultActionKeybindingLabel", "_dropdown", "onDidRun", "e", "update", "lastAction", "store", "dispose", "runAction", "context", "defaultActionKeybinding", "renderKeybindingWithDefaultActionLabel", "setActionContext", "newContext", "primaryContainer", "KEY_DOWN", "equals", "tabIndex", "focus", "dropdownContainer", "setFocusable", "fromRight", "blur", "focusable", "SubmenuEntrySelectActionViewItem", "contextViewService", "map", "text", "ID", "isDisabled", "aria<PERSON><PERSON><PERSON>", "optionsAsChildren", "select", "Math", "max", "findIndex", "borderColor", "option", "createActionViewItem", "instaService", "isSelection", "rememberDefaultAction"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/actions/browser/menuEntryActionViewItem.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { $, addDisposableListener, append, asCSSUrl, EventType, ModifierKeyEmitter, prepend } from '../../../base/browser/dom.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { ActionViewItem, BaseActionViewItem, SelectActionViewItem } from '../../../base/browser/ui/actionbar/actionViewItems.js';\nimport { DropdownMenuActionViewItem } from '../../../base/browser/ui/dropdown/dropdownActionViewItem.js';\nimport { ActionRunner, Separator, SubmenuAction } from '../../../base/common/actions.js';\nimport { UILabelProvider } from '../../../base/common/keybindingLabels.js';\nimport { combinedDisposable, MutableDisposable, toDisposable } from '../../../base/common/lifecycle.js';\nimport { isLinux, isWindows, OS } from '../../../base/common/platform.js';\nimport './menuEntryActionViewItem.css';\nimport { localize } from '../../../nls.js';\nimport { IMenuService, MenuItemAction, SubmenuItemAction } from '../common/actions.js';\nimport { isICommandActionToggleInfo } from '../../action/common/action.js';\nimport { IContextKeyService } from '../../contextkey/common/contextkey.js';\nimport { IContextMenuService, IContextViewService } from '../../contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../keybinding/common/keybinding.js';\nimport { INotificationService } from '../../notification/common/notification.js';\nimport { IStorageService } from '../../storage/common/storage.js';\nimport { IThemeService } from '../../theme/common/themeService.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport { isDark } from '../../theme/common/theme.js';\nimport { assertType } from '../../../base/common/types.js';\nimport { asCssVariable, selectBorder } from '../../theme/common/colorRegistry.js';\nimport { defaultSelectBoxStyles } from '../../theme/browser/defaultStyles.js';\nimport { IAccessibilityService } from '../../accessibility/common/accessibility.js';\nexport function createAndFillInContextMenuActions(menu, optionsOrTarget, targetOrPrimaryGroup, primaryGroupOrUndefined) {\n    let target;\n    let primaryGroup;\n    let groups;\n    if (Array.isArray(menu)) {\n        groups = menu;\n        target = optionsOrTarget;\n        primaryGroup = targetOrPrimaryGroup;\n    }\n    else {\n        const options = optionsOrTarget;\n        groups = menu.getActions(options);\n        target = targetOrPrimaryGroup;\n        primaryGroup = primaryGroupOrUndefined;\n    }\n    const modifierKeyEmitter = ModifierKeyEmitter.getInstance();\n    const useAlternativeActions = modifierKeyEmitter.keyStatus.altKey || ((isWindows || isLinux) && modifierKeyEmitter.keyStatus.shiftKey);\n    fillInActions(groups, target, useAlternativeActions, primaryGroup ? actionGroup => actionGroup === primaryGroup : actionGroup => actionGroup === 'navigation');\n}\nexport function createAndFillInActionBarActions(menu, optionsOrTarget, targetOrPrimaryGroup, primaryGroupOrShouldInlineSubmenu, shouldInlineSubmenuOrUseSeparatorsInPrimaryActions, useSeparatorsInPrimaryActionsOrUndefined) {\n    let target;\n    let primaryGroup;\n    let shouldInlineSubmenu;\n    let useSeparatorsInPrimaryActions;\n    let groups;\n    if (Array.isArray(menu)) {\n        groups = menu;\n        target = optionsOrTarget;\n        primaryGroup = targetOrPrimaryGroup;\n        shouldInlineSubmenu = primaryGroupOrShouldInlineSubmenu;\n        useSeparatorsInPrimaryActions = shouldInlineSubmenuOrUseSeparatorsInPrimaryActions;\n    }\n    else {\n        const options = optionsOrTarget;\n        groups = menu.getActions(options);\n        target = targetOrPrimaryGroup;\n        primaryGroup = primaryGroupOrShouldInlineSubmenu;\n        shouldInlineSubmenu = shouldInlineSubmenuOrUseSeparatorsInPrimaryActions;\n        useSeparatorsInPrimaryActions = useSeparatorsInPrimaryActionsOrUndefined;\n    }\n    const isPrimaryAction = typeof primaryGroup === 'string' ? (actionGroup) => actionGroup === primaryGroup : primaryGroup;\n    // Action bars handle alternative actions on their own so the alternative actions should be ignored\n    fillInActions(groups, target, false, isPrimaryAction, shouldInlineSubmenu, useSeparatorsInPrimaryActions);\n}\nfunction fillInActions(groups, target, useAlternativeActions, isPrimaryAction = actionGroup => actionGroup === 'navigation', shouldInlineSubmenu = () => false, useSeparatorsInPrimaryActions = false) {\n    let primaryBucket;\n    let secondaryBucket;\n    if (Array.isArray(target)) {\n        primaryBucket = target;\n        secondaryBucket = target;\n    }\n    else {\n        primaryBucket = target.primary;\n        secondaryBucket = target.secondary;\n    }\n    const submenuInfo = new Set();\n    for (const [group, actions] of groups) {\n        let target;\n        if (isPrimaryAction(group)) {\n            target = primaryBucket;\n            if (target.length > 0 && useSeparatorsInPrimaryActions) {\n                target.push(new Separator());\n            }\n        }\n        else {\n            target = secondaryBucket;\n            if (target.length > 0) {\n                target.push(new Separator());\n            }\n        }\n        for (let action of actions) {\n            if (useAlternativeActions) {\n                action = action instanceof MenuItemAction && action.alt ? action.alt : action;\n            }\n            const newLen = target.push(action);\n            // keep submenu info for later inlining\n            if (action instanceof SubmenuAction) {\n                submenuInfo.add({ group, action, index: newLen - 1 });\n            }\n        }\n    }\n    // ask the outside if submenu should be inlined or not. only ask when\n    // there would be enough space\n    for (const { group, action, index } of submenuInfo) {\n        const target = isPrimaryAction(group) ? primaryBucket : secondaryBucket;\n        // inlining submenus with length 0 or 1 is easy,\n        // larger submenus need to be checked with the overall limit\n        const submenuActions = action.actions;\n        if (shouldInlineSubmenu(action, group, target.length)) {\n            target.splice(index, 1, ...submenuActions);\n        }\n    }\n}\nlet MenuEntryActionViewItem = class MenuEntryActionViewItem extends ActionViewItem {\n    constructor(action, _options, _keybindingService, _notificationService, _contextKeyService, _themeService, _contextMenuService, _accessibilityService) {\n        super(undefined, action, { icon: !!(action.class || action.item.icon), label: !action.class && !action.item.icon, draggable: _options?.draggable, keybinding: _options?.keybinding, hoverDelegate: _options?.hoverDelegate });\n        this._options = _options;\n        this._keybindingService = _keybindingService;\n        this._notificationService = _notificationService;\n        this._contextKeyService = _contextKeyService;\n        this._themeService = _themeService;\n        this._contextMenuService = _contextMenuService;\n        this._accessibilityService = _accessibilityService;\n        this._wantsAltCommand = false;\n        this._itemClassDispose = this._register(new MutableDisposable());\n        this._altKey = ModifierKeyEmitter.getInstance();\n    }\n    get _menuItemAction() {\n        return this._action;\n    }\n    get _commandAction() {\n        return this._wantsAltCommand && this._menuItemAction.alt || this._menuItemAction;\n    }\n    async onClick(event) {\n        event.preventDefault();\n        event.stopPropagation();\n        try {\n            await this.actionRunner.run(this._commandAction, this._context);\n        }\n        catch (err) {\n            this._notificationService.error(err);\n        }\n    }\n    render(container) {\n        super.render(container);\n        container.classList.add('menu-entry');\n        if (this.options.icon) {\n            this._updateItemClass(this._menuItemAction.item);\n        }\n        if (this._menuItemAction.alt) {\n            let isMouseOver = false;\n            const updateAltState = () => {\n                const wantsAltCommand = !!this._menuItemAction.alt?.enabled &&\n                    (!this._accessibilityService.isMotionReduced() || isMouseOver) && (this._altKey.keyStatus.altKey ||\n                    (this._altKey.keyStatus.shiftKey && isMouseOver));\n                if (wantsAltCommand !== this._wantsAltCommand) {\n                    this._wantsAltCommand = wantsAltCommand;\n                    this.updateLabel();\n                    this.updateTooltip();\n                    this.updateClass();\n                }\n            };\n            this._register(this._altKey.event(updateAltState));\n            this._register(addDisposableListener(container, 'mouseleave', _ => {\n                isMouseOver = false;\n                updateAltState();\n            }));\n            this._register(addDisposableListener(container, 'mouseenter', _ => {\n                isMouseOver = true;\n                updateAltState();\n            }));\n            updateAltState();\n        }\n    }\n    updateLabel() {\n        if (this.options.label && this.label) {\n            this.label.textContent = this._commandAction.label;\n        }\n    }\n    getTooltip() {\n        const keybinding = this._keybindingService.lookupKeybinding(this._commandAction.id, this._contextKeyService);\n        const keybindingLabel = keybinding && keybinding.getLabel();\n        const tooltip = this._commandAction.tooltip || this._commandAction.label;\n        let title = keybindingLabel\n            ? localize('titleAndKb', \"{0} ({1})\", tooltip, keybindingLabel)\n            : tooltip;\n        if (!this._wantsAltCommand && this._menuItemAction.alt?.enabled) {\n            const altTooltip = this._menuItemAction.alt.tooltip || this._menuItemAction.alt.label;\n            const altKeybinding = this._keybindingService.lookupKeybinding(this._menuItemAction.alt.id, this._contextKeyService);\n            const altKeybindingLabel = altKeybinding && altKeybinding.getLabel();\n            const altTitleSection = altKeybindingLabel\n                ? localize('titleAndKb', \"{0} ({1})\", altTooltip, altKeybindingLabel)\n                : altTooltip;\n            title = localize('titleAndKbAndAlt', \"{0}\\n[{1}] {2}\", title, UILabelProvider.modifierLabels[OS].altKey, altTitleSection);\n        }\n        return title;\n    }\n    updateClass() {\n        if (this.options.icon) {\n            if (this._commandAction !== this._menuItemAction) {\n                if (this._menuItemAction.alt) {\n                    this._updateItemClass(this._menuItemAction.alt.item);\n                }\n            }\n            else {\n                this._updateItemClass(this._menuItemAction.item);\n            }\n        }\n    }\n    _updateItemClass(item) {\n        this._itemClassDispose.value = undefined;\n        const { element, label } = this;\n        if (!element || !label) {\n            return;\n        }\n        const icon = this._commandAction.checked && isICommandActionToggleInfo(item.toggled) && item.toggled.icon ? item.toggled.icon : item.icon;\n        if (!icon) {\n            return;\n        }\n        if (ThemeIcon.isThemeIcon(icon)) {\n            // theme icons\n            const iconClasses = ThemeIcon.asClassNameArray(icon);\n            label.classList.add(...iconClasses);\n            this._itemClassDispose.value = toDisposable(() => {\n                label.classList.remove(...iconClasses);\n            });\n        }\n        else {\n            // icon path/url\n            label.style.backgroundImage = (isDark(this._themeService.getColorTheme().type)\n                ? asCSSUrl(icon.dark)\n                : asCSSUrl(icon.light));\n            label.classList.add('icon');\n            this._itemClassDispose.value = combinedDisposable(toDisposable(() => {\n                label.style.backgroundImage = '';\n                label.classList.remove('icon');\n            }), this._themeService.onDidColorThemeChange(() => {\n                // refresh when the theme changes in case we go between dark <-> light\n                this.updateClass();\n            }));\n        }\n    }\n};\nMenuEntryActionViewItem = __decorate([\n    __param(2, IKeybindingService),\n    __param(3, INotificationService),\n    __param(4, IContextKeyService),\n    __param(5, IThemeService),\n    __param(6, IContextMenuService),\n    __param(7, IAccessibilityService)\n], MenuEntryActionViewItem);\nexport { MenuEntryActionViewItem };\nexport class TextOnlyMenuEntryActionViewItem extends MenuEntryActionViewItem {\n    render(container) {\n        this.options.label = true;\n        this.options.icon = false;\n        super.render(container);\n        container.classList.add('text-only');\n        container.classList.toggle('use-comma', this._options?.useComma ?? false);\n    }\n    updateLabel() {\n        const kb = this._keybindingService.lookupKeybinding(this._action.id, this._contextKeyService);\n        if (!kb) {\n            return super.updateLabel();\n        }\n        if (this.label) {\n            const kb2 = TextOnlyMenuEntryActionViewItem._symbolPrintEnter(kb);\n            if (this._options?.conversational) {\n                this.label.textContent = localize({ key: 'content2', comment: ['A label with keybindg like \"ESC to dismiss\"'] }, '{1} to {0}', this._action.label, kb2);\n            }\n            else {\n                this.label.textContent = localize({ key: 'content', comment: ['A label', 'A keybinding'] }, '{0} ({1})', this._action.label, kb2);\n            }\n        }\n    }\n    static _symbolPrintEnter(kb) {\n        return kb.getLabel()\n            ?.replace(/\\benter\\b/gi, '\\u23CE')\n            .replace(/\\bEscape\\b/gi, 'Esc');\n    }\n}\nlet SubmenuEntryActionViewItem = class SubmenuEntryActionViewItem extends DropdownMenuActionViewItem {\n    constructor(action, options, _keybindingService, _contextMenuService, _themeService) {\n        const dropdownOptions = {\n            ...options,\n            menuAsChild: options?.menuAsChild ?? false,\n            classNames: options?.classNames ?? (ThemeIcon.isThemeIcon(action.item.icon) ? ThemeIcon.asClassName(action.item.icon) : undefined),\n            keybindingProvider: options?.keybindingProvider ?? (action => _keybindingService.lookupKeybinding(action.id))\n        };\n        super(action, { getActions: () => action.actions }, _contextMenuService, dropdownOptions);\n        this._keybindingService = _keybindingService;\n        this._contextMenuService = _contextMenuService;\n        this._themeService = _themeService;\n    }\n    render(container) {\n        super.render(container);\n        assertType(this.element);\n        container.classList.add('menu-entry');\n        const action = this._action;\n        const { icon } = action.item;\n        if (icon && !ThemeIcon.isThemeIcon(icon)) {\n            this.element.classList.add('icon');\n            const setBackgroundImage = () => {\n                if (this.element) {\n                    this.element.style.backgroundImage = (isDark(this._themeService.getColorTheme().type)\n                        ? asCSSUrl(icon.dark)\n                        : asCSSUrl(icon.light));\n                }\n            };\n            setBackgroundImage();\n            this._register(this._themeService.onDidColorThemeChange(() => {\n                // refresh when the theme changes in case we go between dark <-> light\n                setBackgroundImage();\n            }));\n        }\n    }\n};\nSubmenuEntryActionViewItem = __decorate([\n    __param(2, IKeybindingService),\n    __param(3, IContextMenuService),\n    __param(4, IThemeService)\n], SubmenuEntryActionViewItem);\nexport { SubmenuEntryActionViewItem };\nlet DropdownWithDefaultActionViewItem = class DropdownWithDefaultActionViewItem extends BaseActionViewItem {\n    constructor(submenuAction, options, _keybindingService, _notificationService, _contextMenuService, _menuService, _instaService, _storageService) {\n        super(null, submenuAction);\n        this._keybindingService = _keybindingService;\n        this._notificationService = _notificationService;\n        this._contextMenuService = _contextMenuService;\n        this._menuService = _menuService;\n        this._instaService = _instaService;\n        this._storageService = _storageService;\n        this._container = null;\n        this._options = options;\n        this._storageKey = `${submenuAction.item.submenu.id}_lastActionId`;\n        // determine default action\n        let defaultAction;\n        const defaultActionId = options?.persistLastActionId ? _storageService.get(this._storageKey, 1 /* StorageScope.WORKSPACE */) : undefined;\n        if (defaultActionId) {\n            defaultAction = submenuAction.actions.find(a => defaultActionId === a.id);\n        }\n        if (!defaultAction) {\n            defaultAction = submenuAction.actions[0];\n        }\n        this._defaultAction = this._instaService.createInstance(MenuEntryActionViewItem, defaultAction, { keybinding: this._getDefaultActionKeybindingLabel(defaultAction) });\n        const dropdownOptions = {\n            keybindingProvider: action => this._keybindingService.lookupKeybinding(action.id),\n            ...options,\n            menuAsChild: options?.menuAsChild ?? true,\n            classNames: options?.classNames ?? ['codicon', 'codicon-chevron-down'],\n            actionRunner: options?.actionRunner ?? new ActionRunner(),\n        };\n        this._dropdown = new DropdownMenuActionViewItem(submenuAction, submenuAction.actions, this._contextMenuService, dropdownOptions);\n        this._register(this._dropdown.actionRunner.onDidRun((e) => {\n            if (e.action instanceof MenuItemAction) {\n                this.update(e.action);\n            }\n        }));\n    }\n    update(lastAction) {\n        if (this._options?.persistLastActionId) {\n            this._storageService.store(this._storageKey, lastAction.id, 1 /* StorageScope.WORKSPACE */, 1 /* StorageTarget.MACHINE */);\n        }\n        this._defaultAction.dispose();\n        this._defaultAction = this._instaService.createInstance(MenuEntryActionViewItem, lastAction, { keybinding: this._getDefaultActionKeybindingLabel(lastAction) });\n        this._defaultAction.actionRunner = new class extends ActionRunner {\n            async runAction(action, context) {\n                await action.run(undefined);\n            }\n        }();\n        if (this._container) {\n            this._defaultAction.render(prepend(this._container, $('.action-container')));\n        }\n    }\n    _getDefaultActionKeybindingLabel(defaultAction) {\n        let defaultActionKeybinding;\n        if (this._options?.renderKeybindingWithDefaultActionLabel) {\n            const kb = this._keybindingService.lookupKeybinding(defaultAction.id);\n            if (kb) {\n                defaultActionKeybinding = `(${kb.getLabel()})`;\n            }\n        }\n        return defaultActionKeybinding;\n    }\n    setActionContext(newContext) {\n        super.setActionContext(newContext);\n        this._defaultAction.setActionContext(newContext);\n        this._dropdown.setActionContext(newContext);\n    }\n    render(container) {\n        this._container = container;\n        super.render(this._container);\n        this._container.classList.add('monaco-dropdown-with-default');\n        const primaryContainer = $('.action-container');\n        this._defaultAction.render(append(this._container, primaryContainer));\n        this._register(addDisposableListener(primaryContainer, EventType.KEY_DOWN, (e) => {\n            const event = new StandardKeyboardEvent(e);\n            if (event.equals(17 /* KeyCode.RightArrow */)) {\n                this._defaultAction.element.tabIndex = -1;\n                this._dropdown.focus();\n                event.stopPropagation();\n            }\n        }));\n        const dropdownContainer = $('.dropdown-action-container');\n        this._dropdown.render(append(this._container, dropdownContainer));\n        this._register(addDisposableListener(dropdownContainer, EventType.KEY_DOWN, (e) => {\n            const event = new StandardKeyboardEvent(e);\n            if (event.equals(15 /* KeyCode.LeftArrow */)) {\n                this._defaultAction.element.tabIndex = 0;\n                this._dropdown.setFocusable(false);\n                this._defaultAction.element?.focus();\n                event.stopPropagation();\n            }\n        }));\n    }\n    focus(fromRight) {\n        if (fromRight) {\n            this._dropdown.focus();\n        }\n        else {\n            this._defaultAction.element.tabIndex = 0;\n            this._defaultAction.element.focus();\n        }\n    }\n    blur() {\n        this._defaultAction.element.tabIndex = -1;\n        this._dropdown.blur();\n        this._container.blur();\n    }\n    setFocusable(focusable) {\n        if (focusable) {\n            this._defaultAction.element.tabIndex = 0;\n        }\n        else {\n            this._defaultAction.element.tabIndex = -1;\n            this._dropdown.setFocusable(false);\n        }\n    }\n    dispose() {\n        this._defaultAction.dispose();\n        this._dropdown.dispose();\n        super.dispose();\n    }\n};\nDropdownWithDefaultActionViewItem = __decorate([\n    __param(2, IKeybindingService),\n    __param(3, INotificationService),\n    __param(4, IContextMenuService),\n    __param(5, IMenuService),\n    __param(6, IInstantiationService),\n    __param(7, IStorageService)\n], DropdownWithDefaultActionViewItem);\nexport { DropdownWithDefaultActionViewItem };\nlet SubmenuEntrySelectActionViewItem = class SubmenuEntrySelectActionViewItem extends SelectActionViewItem {\n    constructor(action, contextViewService) {\n        super(null, action, action.actions.map(a => ({\n            text: a.id === Separator.ID ? '\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500' : a.label,\n            isDisabled: !a.enabled,\n        })), 0, contextViewService, defaultSelectBoxStyles, { ariaLabel: action.tooltip, optionsAsChildren: true });\n        this.select(Math.max(0, action.actions.findIndex(a => a.checked)));\n    }\n    render(container) {\n        super.render(container);\n        container.style.borderColor = asCssVariable(selectBorder);\n    }\n    runAction(option, index) {\n        const action = this.action.actions[index];\n        if (action) {\n            this.actionRunner.run(action);\n        }\n    }\n};\nSubmenuEntrySelectActionViewItem = __decorate([\n    __param(1, IContextViewService)\n], SubmenuEntrySelectActionViewItem);\n/**\n * Creates action view items for menu actions or submenu actions.\n */\nexport function createActionViewItem(instaService, action, options) {\n    if (action instanceof MenuItemAction) {\n        return instaService.createInstance(MenuEntryActionViewItem, action, options);\n    }\n    else if (action instanceof SubmenuItemAction) {\n        if (action.item.isSelection) {\n            return instaService.createInstance(SubmenuEntrySelectActionViewItem, action);\n        }\n        else {\n            if (action.item.rememberDefaultAction) {\n                return instaService.createInstance(DropdownWithDefaultActionViewItem, action, { ...options, persistLastActionId: true });\n            }\n            else {\n                return instaService.createInstance(SubmenuEntryActionViewItem, action, options);\n            }\n        }\n    }\n    else {\n        return undefined;\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,CAAC,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,kBAAkB,EAAEC,OAAO,QAAQ,8BAA8B;AACjI,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,cAAc,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,uDAAuD;AAChI,SAASC,0BAA0B,QAAQ,6DAA6D;AACxG,SAASC,YAAY,EAAEC,SAAS,EAAEC,aAAa,QAAQ,iCAAiC;AACxF,SAASC,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,mCAAmC;AACvG,SAASC,OAAO,EAAEC,SAAS,EAAEC,EAAE,QAAQ,kCAAkC;AACzE,OAAO,+BAA+B;AACtC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,sBAAsB;AACtF,SAASC,0BAA0B,QAAQ,+BAA+B;AAC1E,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,mBAAmB,EAAEC,mBAAmB,QAAQ,0CAA0C;AACnG,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,MAAM,QAAQ,6BAA6B;AACpD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,aAAa,EAAEC,YAAY,QAAQ,qCAAqC;AACjF,SAASC,sBAAsB,QAAQ,sCAAsC;AAC7E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,OAAO,SAASC,iCAAiCA,CAACC,IAAI,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,uBAAuB,EAAE;EACpH,IAAI/D,MAAM;EACV,IAAIgE,YAAY;EAChB,IAAIC,MAAM;EACV,IAAIC,KAAK,CAACC,OAAO,CAACP,IAAI,CAAC,EAAE;IACrBK,MAAM,GAAGL,IAAI;IACb5D,MAAM,GAAG6D,eAAe;IACxBG,YAAY,GAAGF,oBAAoB;EACvC,CAAC,MACI;IACD,MAAMM,OAAO,GAAGP,eAAe;IAC/BI,MAAM,GAAGL,IAAI,CAACS,UAAU,CAACD,OAAO,CAAC;IACjCpE,MAAM,GAAG8D,oBAAoB;IAC7BE,YAAY,GAAGD,uBAAuB;EAC1C;EACA,MAAMO,kBAAkB,GAAGhD,kBAAkB,CAACiD,WAAW,CAAC,CAAC;EAC3D,MAAMC,qBAAqB,GAAGF,kBAAkB,CAACG,SAAS,CAACC,MAAM,IAAK,CAACrC,SAAS,IAAID,OAAO,KAAKkC,kBAAkB,CAACG,SAAS,CAACE,QAAS;EACtIC,aAAa,CAACX,MAAM,EAAEjE,MAAM,EAAEwE,qBAAqB,EAAER,YAAY,GAAGa,WAAW,IAAIA,WAAW,KAAKb,YAAY,GAAGa,WAAW,IAAIA,WAAW,KAAK,YAAY,CAAC;AAClK;AACA,OAAO,SAASC,+BAA+BA,CAAClB,IAAI,EAAEC,eAAe,EAAEC,oBAAoB,EAAEiB,iCAAiC,EAAEC,kDAAkD,EAAEC,wCAAwC,EAAE;EAC1N,IAAIjF,MAAM;EACV,IAAIgE,YAAY;EAChB,IAAIkB,mBAAmB;EACvB,IAAIC,6BAA6B;EACjC,IAAIlB,MAAM;EACV,IAAIC,KAAK,CAACC,OAAO,CAACP,IAAI,CAAC,EAAE;IACrBK,MAAM,GAAGL,IAAI;IACb5D,MAAM,GAAG6D,eAAe;IACxBG,YAAY,GAAGF,oBAAoB;IACnCoB,mBAAmB,GAAGH,iCAAiC;IACvDI,6BAA6B,GAAGH,kDAAkD;EACtF,CAAC,MACI;IACD,MAAMZ,OAAO,GAAGP,eAAe;IAC/BI,MAAM,GAAGL,IAAI,CAACS,UAAU,CAACD,OAAO,CAAC;IACjCpE,MAAM,GAAG8D,oBAAoB;IAC7BE,YAAY,GAAGe,iCAAiC;IAChDG,mBAAmB,GAAGF,kDAAkD;IACxEG,6BAA6B,GAAGF,wCAAwC;EAC5E;EACA,MAAMG,eAAe,GAAG,OAAOpB,YAAY,KAAK,QAAQ,GAAIa,WAAW,IAAKA,WAAW,KAAKb,YAAY,GAAGA,YAAY;EACvH;EACAY,aAAa,CAACX,MAAM,EAAEjE,MAAM,EAAE,KAAK,EAAEoF,eAAe,EAAEF,mBAAmB,EAAEC,6BAA6B,CAAC;AAC7G;AACA,SAASP,aAAaA,CAACX,MAAM,EAAEjE,MAAM,EAAEwE,qBAAqB,EAAEY,eAAe,GAAGP,WAAW,IAAIA,WAAW,KAAK,YAAY,EAAEK,mBAAmB,GAAGA,CAAA,KAAM,KAAK,EAAEC,6BAA6B,GAAG,KAAK,EAAE;EACnM,IAAIE,aAAa;EACjB,IAAIC,eAAe;EACnB,IAAIpB,KAAK,CAACC,OAAO,CAACnE,MAAM,CAAC,EAAE;IACvBqF,aAAa,GAAGrF,MAAM;IACtBsF,eAAe,GAAGtF,MAAM;EAC5B,CAAC,MACI;IACDqF,aAAa,GAAGrF,MAAM,CAACuF,OAAO;IAC9BD,eAAe,GAAGtF,MAAM,CAACwF,SAAS;EACtC;EACA,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7B,KAAK,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,IAAI3B,MAAM,EAAE;IACnC,IAAIjE,MAAM;IACV,IAAIoF,eAAe,CAACO,KAAK,CAAC,EAAE;MACxB3F,MAAM,GAAGqF,aAAa;MACtB,IAAIrF,MAAM,CAACK,MAAM,GAAG,CAAC,IAAI8E,6BAA6B,EAAE;QACpDnF,MAAM,CAAC6F,IAAI,CAAC,IAAI/D,SAAS,CAAC,CAAC,CAAC;MAChC;IACJ,CAAC,MACI;MACD9B,MAAM,GAAGsF,eAAe;MACxB,IAAItF,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;QACnBL,MAAM,CAAC6F,IAAI,CAAC,IAAI/D,SAAS,CAAC,CAAC,CAAC;MAChC;IACJ;IACA,KAAK,IAAIgE,MAAM,IAAIF,OAAO,EAAE;MACxB,IAAIpB,qBAAqB,EAAE;QACvBsB,MAAM,GAAGA,MAAM,YAAYrD,cAAc,IAAIqD,MAAM,CAACC,GAAG,GAAGD,MAAM,CAACC,GAAG,GAAGD,MAAM;MACjF;MACA,MAAME,MAAM,GAAGhG,MAAM,CAAC6F,IAAI,CAACC,MAAM,CAAC;MAClC;MACA,IAAIA,MAAM,YAAY/D,aAAa,EAAE;QACjC0D,WAAW,CAACQ,GAAG,CAAC;UAAEN,KAAK;UAAEG,MAAM;UAAEI,KAAK,EAAEF,MAAM,GAAG;QAAE,CAAC,CAAC;MACzD;IACJ;EACJ;EACA;EACA;EACA,KAAK,MAAM;IAAEL,KAAK;IAAEG,MAAM;IAAEI;EAAM,CAAC,IAAIT,WAAW,EAAE;IAChD,MAAMzF,MAAM,GAAGoF,eAAe,CAACO,KAAK,CAAC,GAAGN,aAAa,GAAGC,eAAe;IACvE;IACA;IACA,MAAMa,cAAc,GAAGL,MAAM,CAACF,OAAO;IACrC,IAAIV,mBAAmB,CAACY,MAAM,EAAEH,KAAK,EAAE3F,MAAM,CAACK,MAAM,CAAC,EAAE;MACnDL,MAAM,CAACoG,MAAM,CAACF,KAAK,EAAE,CAAC,EAAE,GAAGC,cAAc,CAAC;IAC9C;EACJ;AACJ;AACA,IAAIE,uBAAuB,GAAG,MAAMA,uBAAuB,SAAS5E,cAAc,CAAC;EAC/E6E,WAAWA,CAACR,MAAM,EAAES,QAAQ,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAE;IACnJ,KAAK,CAACC,SAAS,EAAEhB,MAAM,EAAE;MAAEiB,IAAI,EAAE,CAAC,EAAEjB,MAAM,CAACkB,KAAK,IAAIlB,MAAM,CAACmB,IAAI,CAACF,IAAI,CAAC;MAAEG,KAAK,EAAE,CAACpB,MAAM,CAACkB,KAAK,IAAI,CAAClB,MAAM,CAACmB,IAAI,CAACF,IAAI;MAAEI,SAAS,EAAEZ,QAAQ,EAAEY,SAAS;MAAEC,UAAU,EAAEb,QAAQ,EAAEa,UAAU;MAAEC,aAAa,EAAEd,QAAQ,EAAEc;IAAc,CAAC,CAAC;IAC7N,IAAI,CAACd,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACS,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAItF,iBAAiB,CAAC,CAAC,CAAC;IAChE,IAAI,CAACuF,OAAO,GAAGnG,kBAAkB,CAACiD,WAAW,CAAC,CAAC;EACnD;EACA,IAAImD,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACN,gBAAgB,IAAI,IAAI,CAACI,eAAe,CAAC3B,GAAG,IAAI,IAAI,CAAC2B,eAAe;EACpF;EACMG,OAAOA,CAACC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjBF,KAAK,CAACG,cAAc,CAAC,CAAC;MACtBH,KAAK,CAACI,eAAe,CAAC,CAAC;MACvB,IAAI;QACA,MAAMH,KAAI,CAACI,YAAY,CAACC,GAAG,CAACL,KAAI,CAACH,cAAc,EAAEG,KAAI,CAACM,QAAQ,CAAC;MACnE,CAAC,CACD,OAAOC,GAAG,EAAE;QACRP,KAAI,CAACtB,oBAAoB,CAAC8B,KAAK,CAACD,GAAG,CAAC;MACxC;IAAC;EACL;EACAE,MAAMA,CAACC,SAAS,EAAE;IACd,KAAK,CAACD,MAAM,CAACC,SAAS,CAAC;IACvBA,SAAS,CAACC,SAAS,CAACzC,GAAG,CAAC,YAAY,CAAC;IACrC,IAAI,IAAI,CAAC7B,OAAO,CAAC2C,IAAI,EAAE;MACnB,IAAI,CAAC4B,gBAAgB,CAAC,IAAI,CAACjB,eAAe,CAACT,IAAI,CAAC;IACpD;IACA,IAAI,IAAI,CAACS,eAAe,CAAC3B,GAAG,EAAE;MAC1B,IAAI6C,WAAW,GAAG,KAAK;MACvB,MAAMC,cAAc,GAAGA,CAAA,KAAM;QACzB,MAAMC,eAAe,GAAG,CAAC,CAAC,IAAI,CAACpB,eAAe,CAAC3B,GAAG,EAAEgD,OAAO,KACtD,CAAC,IAAI,CAAClC,qBAAqB,CAACmC,eAAe,CAAC,CAAC,IAAIJ,WAAW,CAAC,KAAK,IAAI,CAACnB,OAAO,CAAChD,SAAS,CAACC,MAAM,IAC/F,IAAI,CAAC+C,OAAO,CAAChD,SAAS,CAACE,QAAQ,IAAIiE,WAAY,CAAC;QACrD,IAAIE,eAAe,KAAK,IAAI,CAACxB,gBAAgB,EAAE;UAC3C,IAAI,CAACA,gBAAgB,GAAGwB,eAAe;UACvC,IAAI,CAACG,WAAW,CAAC,CAAC;UAClB,IAAI,CAACC,aAAa,CAAC,CAAC;UACpB,IAAI,CAACC,WAAW,CAAC,CAAC;QACtB;MACJ,CAAC;MACD,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAACC,OAAO,CAACK,KAAK,CAACe,cAAc,CAAC,CAAC;MAClD,IAAI,CAACrB,SAAS,CAACtG,qBAAqB,CAACuH,SAAS,EAAE,YAAY,EAAEW,CAAC,IAAI;QAC/DR,WAAW,GAAG,KAAK;QACnBC,cAAc,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC;MACH,IAAI,CAACrB,SAAS,CAACtG,qBAAqB,CAACuH,SAAS,EAAE,YAAY,EAAEW,CAAC,IAAI;QAC/DR,WAAW,GAAG,IAAI;QAClBC,cAAc,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC;MACHA,cAAc,CAAC,CAAC;IACpB;EACJ;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC7E,OAAO,CAAC8C,KAAK,IAAI,IAAI,CAACA,KAAK,EAAE;MAClC,IAAI,CAACA,KAAK,CAACmC,WAAW,GAAG,IAAI,CAACzB,cAAc,CAACV,KAAK;IACtD;EACJ;EACAoC,UAAUA,CAAA,EAAG;IACT,MAAMlC,UAAU,GAAG,IAAI,CAACZ,kBAAkB,CAAC+C,gBAAgB,CAAC,IAAI,CAAC3B,cAAc,CAAC4B,EAAE,EAAE,IAAI,CAAC9C,kBAAkB,CAAC;IAC5G,MAAM+C,eAAe,GAAGrC,UAAU,IAAIA,UAAU,CAACsC,QAAQ,CAAC,CAAC;IAC3D,MAAMC,OAAO,GAAG,IAAI,CAAC/B,cAAc,CAAC+B,OAAO,IAAI,IAAI,CAAC/B,cAAc,CAACV,KAAK;IACxE,IAAI0C,KAAK,GAAGH,eAAe,GACrBlH,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAEoH,OAAO,EAAEF,eAAe,CAAC,GAC7DE,OAAO;IACb,IAAI,CAAC,IAAI,CAACrC,gBAAgB,IAAI,IAAI,CAACI,eAAe,CAAC3B,GAAG,EAAEgD,OAAO,EAAE;MAC7D,MAAMc,UAAU,GAAG,IAAI,CAACnC,eAAe,CAAC3B,GAAG,CAAC4D,OAAO,IAAI,IAAI,CAACjC,eAAe,CAAC3B,GAAG,CAACmB,KAAK;MACrF,MAAM4C,aAAa,GAAG,IAAI,CAACtD,kBAAkB,CAAC+C,gBAAgB,CAAC,IAAI,CAAC7B,eAAe,CAAC3B,GAAG,CAACyD,EAAE,EAAE,IAAI,CAAC9C,kBAAkB,CAAC;MACpH,MAAMqD,kBAAkB,GAAGD,aAAa,IAAIA,aAAa,CAACJ,QAAQ,CAAC,CAAC;MACpE,MAAMM,eAAe,GAAGD,kBAAkB,GACpCxH,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAEsH,UAAU,EAAEE,kBAAkB,CAAC,GACnEF,UAAU;MAChBD,KAAK,GAAGrH,QAAQ,CAAC,kBAAkB,EAAE,gBAAgB,EAAEqH,KAAK,EAAE5H,eAAe,CAACiI,cAAc,CAAC3H,EAAE,CAAC,CAACoC,MAAM,EAAEsF,eAAe,CAAC;IAC7H;IACA,OAAOJ,KAAK;EAChB;EACAT,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC/E,OAAO,CAAC2C,IAAI,EAAE;MACnB,IAAI,IAAI,CAACa,cAAc,KAAK,IAAI,CAACF,eAAe,EAAE;QAC9C,IAAI,IAAI,CAACA,eAAe,CAAC3B,GAAG,EAAE;UAC1B,IAAI,CAAC4C,gBAAgB,CAAC,IAAI,CAACjB,eAAe,CAAC3B,GAAG,CAACkB,IAAI,CAAC;QACxD;MACJ,CAAC,MACI;QACD,IAAI,CAAC0B,gBAAgB,CAAC,IAAI,CAACjB,eAAe,CAACT,IAAI,CAAC;MACpD;IACJ;EACJ;EACA0B,gBAAgBA,CAAC1B,IAAI,EAAE;IACnB,IAAI,CAACM,iBAAiB,CAAC2C,KAAK,GAAGpD,SAAS;IACxC,MAAM;MAAEqD,OAAO;MAAEjD;IAAM,CAAC,GAAG,IAAI;IAC/B,IAAI,CAACiD,OAAO,IAAI,CAACjD,KAAK,EAAE;MACpB;IACJ;IACA,MAAMH,IAAI,GAAG,IAAI,CAACa,cAAc,CAACwC,OAAO,IAAIzH,0BAA0B,CAACsE,IAAI,CAACoD,OAAO,CAAC,IAAIpD,IAAI,CAACoD,OAAO,CAACtD,IAAI,GAAGE,IAAI,CAACoD,OAAO,CAACtD,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzI,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI3D,SAAS,CAACkH,WAAW,CAACvD,IAAI,CAAC,EAAE;MAC7B;MACA,MAAMwD,WAAW,GAAGnH,SAAS,CAACoH,gBAAgB,CAACzD,IAAI,CAAC;MACpDG,KAAK,CAACwB,SAAS,CAACzC,GAAG,CAAC,GAAGsE,WAAW,CAAC;MACnC,IAAI,CAAChD,iBAAiB,CAAC2C,KAAK,GAAG/H,YAAY,CAAC,MAAM;QAC9C+E,KAAK,CAACwB,SAAS,CAAC+B,MAAM,CAAC,GAAGF,WAAW,CAAC;MAC1C,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACArD,KAAK,CAACwD,KAAK,CAACC,eAAe,GAAItH,MAAM,CAAC,IAAI,CAACsD,aAAa,CAACiE,aAAa,CAAC,CAAC,CAACC,IAAI,CAAC,GACxEzJ,QAAQ,CAAC2F,IAAI,CAAC+D,IAAI,CAAC,GACnB1J,QAAQ,CAAC2F,IAAI,CAACgE,KAAK,CAAE;MAC3B7D,KAAK,CAACwB,SAAS,CAACzC,GAAG,CAAC,MAAM,CAAC;MAC3B,IAAI,CAACsB,iBAAiB,CAAC2C,KAAK,GAAGjI,kBAAkB,CAACE,YAAY,CAAC,MAAM;QACjE+E,KAAK,CAACwD,KAAK,CAACC,eAAe,GAAG,EAAE;QAChCzD,KAAK,CAACwB,SAAS,CAAC+B,MAAM,CAAC,MAAM,CAAC;MAClC,CAAC,CAAC,EAAE,IAAI,CAAC9D,aAAa,CAACqE,qBAAqB,CAAC,MAAM;QAC/C;QACA,IAAI,CAAC7B,WAAW,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC;IACP;EACJ;AACJ,CAAC;AACD9C,uBAAuB,GAAGvG,UAAU,CAAC,CACjCgB,OAAO,CAAC,CAAC,EAAEkC,kBAAkB,CAAC,EAC9BlC,OAAO,CAAC,CAAC,EAAEmC,oBAAoB,CAAC,EAChCnC,OAAO,CAAC,CAAC,EAAE8B,kBAAkB,CAAC,EAC9B9B,OAAO,CAAC,CAAC,EAAEqC,aAAa,CAAC,EACzBrC,OAAO,CAAC,CAAC,EAAE+B,mBAAmB,CAAC,EAC/B/B,OAAO,CAAC,CAAC,EAAE4C,qBAAqB,CAAC,CACpC,EAAE2C,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB;AAChC,OAAO,MAAM4E,+BAA+B,SAAS5E,uBAAuB,CAAC;EACzEmC,MAAMA,CAACC,SAAS,EAAE;IACd,IAAI,CAACrE,OAAO,CAAC8C,KAAK,GAAG,IAAI;IACzB,IAAI,CAAC9C,OAAO,CAAC2C,IAAI,GAAG,KAAK;IACzB,KAAK,CAACyB,MAAM,CAACC,SAAS,CAAC;IACvBA,SAAS,CAACC,SAAS,CAACzC,GAAG,CAAC,WAAW,CAAC;IACpCwC,SAAS,CAACC,SAAS,CAACwC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC3E,QAAQ,EAAE4E,QAAQ,IAAI,KAAK,CAAC;EAC7E;EACAlC,WAAWA,CAAA,EAAG;IACV,MAAMmC,EAAE,GAAG,IAAI,CAAC5E,kBAAkB,CAAC+C,gBAAgB,CAAC,IAAI,CAAC5B,OAAO,CAAC6B,EAAE,EAAE,IAAI,CAAC9C,kBAAkB,CAAC;IAC7F,IAAI,CAAC0E,EAAE,EAAE;MACL,OAAO,KAAK,CAACnC,WAAW,CAAC,CAAC;IAC9B;IACA,IAAI,IAAI,CAAC/B,KAAK,EAAE;MACZ,MAAMmE,GAAG,GAAGJ,+BAA+B,CAACK,iBAAiB,CAACF,EAAE,CAAC;MACjE,IAAI,IAAI,CAAC7E,QAAQ,EAAEgF,cAAc,EAAE;QAC/B,IAAI,CAACrE,KAAK,CAACmC,WAAW,GAAG9G,QAAQ,CAAC;UAAEtC,GAAG,EAAE,UAAU;UAAEuL,OAAO,EAAE,CAAC,6CAA6C;QAAE,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC7D,OAAO,CAACT,KAAK,EAAEmE,GAAG,CAAC;MAC3J,CAAC,MACI;QACD,IAAI,CAACnE,KAAK,CAACmC,WAAW,GAAG9G,QAAQ,CAAC;UAAEtC,GAAG,EAAE,SAAS;UAAEuL,OAAO,EAAE,CAAC,SAAS,EAAE,cAAc;QAAE,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC7D,OAAO,CAACT,KAAK,EAAEmE,GAAG,CAAC;MACrI;IACJ;EACJ;EACA,OAAOC,iBAAiBA,CAACF,EAAE,EAAE;IACzB,OAAOA,EAAE,CAAC1B,QAAQ,CAAC,CAAC,EACd+B,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CACjCA,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;EACvC;AACJ;AACA,IAAIC,0BAA0B,GAAG,MAAMA,0BAA0B,SAAS9J,0BAA0B,CAAC;EACjG0E,WAAWA,CAACR,MAAM,EAAE1B,OAAO,EAAEoC,kBAAkB,EAAEI,mBAAmB,EAAED,aAAa,EAAE;IACjF,MAAMgF,eAAe,GAAG;MACpB,GAAGvH,OAAO;MACVwH,WAAW,EAAExH,OAAO,EAAEwH,WAAW,IAAI,KAAK;MAC1CC,UAAU,EAAEzH,OAAO,EAAEyH,UAAU,KAAKzI,SAAS,CAACkH,WAAW,CAACxE,MAAM,CAACmB,IAAI,CAACF,IAAI,CAAC,GAAG3D,SAAS,CAAC0I,WAAW,CAAChG,MAAM,CAACmB,IAAI,CAACF,IAAI,CAAC,GAAGD,SAAS,CAAC;MAClIiF,kBAAkB,EAAE3H,OAAO,EAAE2H,kBAAkB,KAAKjG,MAAM,IAAIU,kBAAkB,CAAC+C,gBAAgB,CAACzD,MAAM,CAAC0D,EAAE,CAAC;IAChH,CAAC;IACD,KAAK,CAAC1D,MAAM,EAAE;MAAEzB,UAAU,EAAEA,CAAA,KAAMyB,MAAM,CAACF;IAAQ,CAAC,EAAEgB,mBAAmB,EAAE+E,eAAe,CAAC;IACzF,IAAI,CAACnF,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACI,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACD,aAAa,GAAGA,aAAa;EACtC;EACA6B,MAAMA,CAACC,SAAS,EAAE;IACd,KAAK,CAACD,MAAM,CAACC,SAAS,CAAC;IACvBnF,UAAU,CAAC,IAAI,CAAC6G,OAAO,CAAC;IACxB1B,SAAS,CAACC,SAAS,CAACzC,GAAG,CAAC,YAAY,CAAC;IACrC,MAAMH,MAAM,GAAG,IAAI,CAAC6B,OAAO;IAC3B,MAAM;MAAEZ;IAAK,CAAC,GAAGjB,MAAM,CAACmB,IAAI;IAC5B,IAAIF,IAAI,IAAI,CAAC3D,SAAS,CAACkH,WAAW,CAACvD,IAAI,CAAC,EAAE;MACtC,IAAI,CAACoD,OAAO,CAACzB,SAAS,CAACzC,GAAG,CAAC,MAAM,CAAC;MAClC,MAAM+F,kBAAkB,GAAGA,CAAA,KAAM;QAC7B,IAAI,IAAI,CAAC7B,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACO,KAAK,CAACC,eAAe,GAAItH,MAAM,CAAC,IAAI,CAACsD,aAAa,CAACiE,aAAa,CAAC,CAAC,CAACC,IAAI,CAAC,GAC/EzJ,QAAQ,CAAC2F,IAAI,CAAC+D,IAAI,CAAC,GACnB1J,QAAQ,CAAC2F,IAAI,CAACgE,KAAK,CAAE;QAC/B;MACJ,CAAC;MACDiB,kBAAkB,CAAC,CAAC;MACpB,IAAI,CAACxE,SAAS,CAAC,IAAI,CAACb,aAAa,CAACqE,qBAAqB,CAAC,MAAM;QAC1D;QACAgB,kBAAkB,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC;IACP;EACJ;AACJ,CAAC;AACDN,0BAA0B,GAAG5L,UAAU,CAAC,CACpCgB,OAAO,CAAC,CAAC,EAAEkC,kBAAkB,CAAC,EAC9BlC,OAAO,CAAC,CAAC,EAAE+B,mBAAmB,CAAC,EAC/B/B,OAAO,CAAC,CAAC,EAAEqC,aAAa,CAAC,CAC5B,EAAEuI,0BAA0B,CAAC;AAC9B,SAASA,0BAA0B;AACnC,IAAIO,iCAAiC,GAAG,MAAMA,iCAAiC,SAASvK,kBAAkB,CAAC;EACvG4E,WAAWA,CAAC4F,aAAa,EAAE9H,OAAO,EAAEoC,kBAAkB,EAAEC,oBAAoB,EAAEG,mBAAmB,EAAEuF,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAE;IAC7I,KAAK,CAAC,IAAI,EAAEH,aAAa,CAAC;IAC1B,IAAI,CAAC1F,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACG,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACuF,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC/F,QAAQ,GAAGnC,OAAO;IACvB,IAAI,CAACmI,WAAW,GAAG,GAAGL,aAAa,CAACjF,IAAI,CAACuF,OAAO,CAAChD,EAAE,eAAe;IAClE;IACA,IAAIiD,aAAa;IACjB,MAAMC,eAAe,GAAGtI,OAAO,EAAEuI,mBAAmB,GAAGN,eAAe,CAACO,GAAG,CAAC,IAAI,CAACL,WAAW,EAAE,CAAC,CAAC,4BAA4B,CAAC,GAAGzF,SAAS;IACxI,IAAI4F,eAAe,EAAE;MACjBD,aAAa,GAAGP,aAAa,CAACtG,OAAO,CAACiH,IAAI,CAACC,CAAC,IAAIJ,eAAe,KAAKI,CAAC,CAACtD,EAAE,CAAC;IAC7E;IACA,IAAI,CAACiD,aAAa,EAAE;MAChBA,aAAa,GAAGP,aAAa,CAACtG,OAAO,CAAC,CAAC,CAAC;IAC5C;IACA,IAAI,CAACmH,cAAc,GAAG,IAAI,CAACX,aAAa,CAACY,cAAc,CAAC3G,uBAAuB,EAAEoG,aAAa,EAAE;MAAErF,UAAU,EAAE,IAAI,CAAC6F,gCAAgC,CAACR,aAAa;IAAE,CAAC,CAAC;IACrK,MAAMd,eAAe,GAAG;MACpBI,kBAAkB,EAAEjG,MAAM,IAAI,IAAI,CAACU,kBAAkB,CAAC+C,gBAAgB,CAACzD,MAAM,CAAC0D,EAAE,CAAC;MACjF,GAAGpF,OAAO;MACVwH,WAAW,EAAExH,OAAO,EAAEwH,WAAW,IAAI,IAAI;MACzCC,UAAU,EAAEzH,OAAO,EAAEyH,UAAU,IAAI,CAAC,SAAS,EAAE,sBAAsB,CAAC;MACtE1D,YAAY,EAAE/D,OAAO,EAAE+D,YAAY,IAAI,IAAItG,YAAY,CAAC;IAC5D,CAAC;IACD,IAAI,CAACqL,SAAS,GAAG,IAAItL,0BAA0B,CAACsK,aAAa,EAAEA,aAAa,CAACtG,OAAO,EAAE,IAAI,CAACgB,mBAAmB,EAAE+E,eAAe,CAAC;IAChI,IAAI,CAACnE,SAAS,CAAC,IAAI,CAAC0F,SAAS,CAAC/E,YAAY,CAACgF,QAAQ,CAAEC,CAAC,IAAK;MACvD,IAAIA,CAAC,CAACtH,MAAM,YAAYrD,cAAc,EAAE;QACpC,IAAI,CAAC4K,MAAM,CAACD,CAAC,CAACtH,MAAM,CAAC;MACzB;IACJ,CAAC,CAAC,CAAC;EACP;EACAuH,MAAMA,CAACC,UAAU,EAAE;IACf,IAAI,IAAI,CAAC/G,QAAQ,EAAEoG,mBAAmB,EAAE;MACpC,IAAI,CAACN,eAAe,CAACkB,KAAK,CAAC,IAAI,CAAChB,WAAW,EAAEe,UAAU,CAAC9D,EAAE,EAAE,CAAC,CAAC,8BAA8B,CAAC,CAAC,2BAA2B,CAAC;IAC9H;IACA,IAAI,CAACuD,cAAc,CAACS,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACT,cAAc,GAAG,IAAI,CAACX,aAAa,CAACY,cAAc,CAAC3G,uBAAuB,EAAEiH,UAAU,EAAE;MAAElG,UAAU,EAAE,IAAI,CAAC6F,gCAAgC,CAACK,UAAU;IAAE,CAAC,CAAC;IAC/J,IAAI,CAACP,cAAc,CAAC5E,YAAY,GAAG,IAAI,cAActG,YAAY,CAAC;MACxD4L,SAASA,CAAC3H,MAAM,EAAE4H,OAAO,EAAE;QAAA,OAAA1F,iBAAA;UAC7B,MAAMlC,MAAM,CAACsC,GAAG,CAACtB,SAAS,CAAC;QAAC;MAChC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACwF,UAAU,EAAE;MACjB,IAAI,CAACS,cAAc,CAACvE,MAAM,CAACjH,OAAO,CAAC,IAAI,CAAC+K,UAAU,EAAErL,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAChF;EACJ;EACAgM,gCAAgCA,CAACR,aAAa,EAAE;IAC5C,IAAIkB,uBAAuB;IAC3B,IAAI,IAAI,CAACpH,QAAQ,EAAEqH,sCAAsC,EAAE;MACvD,MAAMxC,EAAE,GAAG,IAAI,CAAC5E,kBAAkB,CAAC+C,gBAAgB,CAACkD,aAAa,CAACjD,EAAE,CAAC;MACrE,IAAI4B,EAAE,EAAE;QACJuC,uBAAuB,GAAG,IAAIvC,EAAE,CAAC1B,QAAQ,CAAC,CAAC,GAAG;MAClD;IACJ;IACA,OAAOiE,uBAAuB;EAClC;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,KAAK,CAACD,gBAAgB,CAACC,UAAU,CAAC;IAClC,IAAI,CAACf,cAAc,CAACc,gBAAgB,CAACC,UAAU,CAAC;IAChD,IAAI,CAACZ,SAAS,CAACW,gBAAgB,CAACC,UAAU,CAAC;EAC/C;EACAtF,MAAMA,CAACC,SAAS,EAAE;IACd,IAAI,CAAC6D,UAAU,GAAG7D,SAAS;IAC3B,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC8D,UAAU,CAAC;IAC7B,IAAI,CAACA,UAAU,CAAC5D,SAAS,CAACzC,GAAG,CAAC,8BAA8B,CAAC;IAC7D,MAAM8H,gBAAgB,GAAG9M,CAAC,CAAC,mBAAmB,CAAC;IAC/C,IAAI,CAAC8L,cAAc,CAACvE,MAAM,CAACrH,MAAM,CAAC,IAAI,CAACmL,UAAU,EAAEyB,gBAAgB,CAAC,CAAC;IACrE,IAAI,CAACvG,SAAS,CAACtG,qBAAqB,CAAC6M,gBAAgB,EAAE1M,SAAS,CAAC2M,QAAQ,EAAGZ,CAAC,IAAK;MAC9E,MAAMtF,KAAK,GAAG,IAAItG,qBAAqB,CAAC4L,CAAC,CAAC;MAC1C,IAAItF,KAAK,CAACmG,MAAM,CAAC,EAAE,CAAC,wBAAwB,CAAC,EAAE;QAC3C,IAAI,CAAClB,cAAc,CAAC5C,OAAO,CAAC+D,QAAQ,GAAG,CAAC,CAAC;QACzC,IAAI,CAAChB,SAAS,CAACiB,KAAK,CAAC,CAAC;QACtBrG,KAAK,CAACI,eAAe,CAAC,CAAC;MAC3B;IACJ,CAAC,CAAC,CAAC;IACH,MAAMkG,iBAAiB,GAAGnN,CAAC,CAAC,4BAA4B,CAAC;IACzD,IAAI,CAACiM,SAAS,CAAC1E,MAAM,CAACrH,MAAM,CAAC,IAAI,CAACmL,UAAU,EAAE8B,iBAAiB,CAAC,CAAC;IACjE,IAAI,CAAC5G,SAAS,CAACtG,qBAAqB,CAACkN,iBAAiB,EAAE/M,SAAS,CAAC2M,QAAQ,EAAGZ,CAAC,IAAK;MAC/E,MAAMtF,KAAK,GAAG,IAAItG,qBAAqB,CAAC4L,CAAC,CAAC;MAC1C,IAAItF,KAAK,CAACmG,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,EAAE;QAC1C,IAAI,CAAClB,cAAc,CAAC5C,OAAO,CAAC+D,QAAQ,GAAG,CAAC;QACxC,IAAI,CAAChB,SAAS,CAACmB,YAAY,CAAC,KAAK,CAAC;QAClC,IAAI,CAACtB,cAAc,CAAC5C,OAAO,EAAEgE,KAAK,CAAC,CAAC;QACpCrG,KAAK,CAACI,eAAe,CAAC,CAAC;MAC3B;IACJ,CAAC,CAAC,CAAC;EACP;EACAiG,KAAKA,CAACG,SAAS,EAAE;IACb,IAAIA,SAAS,EAAE;MACX,IAAI,CAACpB,SAAS,CAACiB,KAAK,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,IAAI,CAACpB,cAAc,CAAC5C,OAAO,CAAC+D,QAAQ,GAAG,CAAC;MACxC,IAAI,CAACnB,cAAc,CAAC5C,OAAO,CAACgE,KAAK,CAAC,CAAC;IACvC;EACJ;EACAI,IAAIA,CAAA,EAAG;IACH,IAAI,CAACxB,cAAc,CAAC5C,OAAO,CAAC+D,QAAQ,GAAG,CAAC,CAAC;IACzC,IAAI,CAAChB,SAAS,CAACqB,IAAI,CAAC,CAAC;IACrB,IAAI,CAACjC,UAAU,CAACiC,IAAI,CAAC,CAAC;EAC1B;EACAF,YAAYA,CAACG,SAAS,EAAE;IACpB,IAAIA,SAAS,EAAE;MACX,IAAI,CAACzB,cAAc,CAAC5C,OAAO,CAAC+D,QAAQ,GAAG,CAAC;IAC5C,CAAC,MACI;MACD,IAAI,CAACnB,cAAc,CAAC5C,OAAO,CAAC+D,QAAQ,GAAG,CAAC,CAAC;MACzC,IAAI,CAAChB,SAAS,CAACmB,YAAY,CAAC,KAAK,CAAC;IACtC;EACJ;EACAb,OAAOA,CAAA,EAAG;IACN,IAAI,CAACT,cAAc,CAACS,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACN,SAAS,CAACM,OAAO,CAAC,CAAC;IACxB,KAAK,CAACA,OAAO,CAAC,CAAC;EACnB;AACJ,CAAC;AACDvB,iCAAiC,GAAGnM,UAAU,CAAC,CAC3CgB,OAAO,CAAC,CAAC,EAAEkC,kBAAkB,CAAC,EAC9BlC,OAAO,CAAC,CAAC,EAAEmC,oBAAoB,CAAC,EAChCnC,OAAO,CAAC,CAAC,EAAE+B,mBAAmB,CAAC,EAC/B/B,OAAO,CAAC,CAAC,EAAE0B,YAAY,CAAC,EACxB1B,OAAO,CAAC,CAAC,EAAEiC,qBAAqB,CAAC,EACjCjC,OAAO,CAAC,CAAC,EAAEoC,eAAe,CAAC,CAC9B,EAAE+I,iCAAiC,CAAC;AACrC,SAASA,iCAAiC;AAC1C,IAAIwC,gCAAgC,GAAG,MAAMA,gCAAgC,SAAS9M,oBAAoB,CAAC;EACvG2E,WAAWA,CAACR,MAAM,EAAE4I,kBAAkB,EAAE;IACpC,KAAK,CAAC,IAAI,EAAE5I,MAAM,EAAEA,MAAM,CAACF,OAAO,CAAC+I,GAAG,CAAC7B,CAAC,KAAK;MACzC8B,IAAI,EAAE9B,CAAC,CAACtD,EAAE,KAAK1H,SAAS,CAAC+M,EAAE,GAAG,wDAAwD,GAAG/B,CAAC,CAAC5F,KAAK;MAChG4H,UAAU,EAAE,CAAChC,CAAC,CAAC/D;IACnB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE2F,kBAAkB,EAAEjL,sBAAsB,EAAE;MAAEsL,SAAS,EAAEjJ,MAAM,CAAC6D,OAAO;MAAEqF,iBAAiB,EAAE;IAAK,CAAC,CAAC;IAC3G,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErJ,MAAM,CAACF,OAAO,CAACwJ,SAAS,CAACtC,CAAC,IAAIA,CAAC,CAAC1C,OAAO,CAAC,CAAC,CAAC;EACtE;EACA5B,MAAMA,CAACC,SAAS,EAAE;IACd,KAAK,CAACD,MAAM,CAACC,SAAS,CAAC;IACvBA,SAAS,CAACiC,KAAK,CAAC2E,WAAW,GAAG9L,aAAa,CAACC,YAAY,CAAC;EAC7D;EACAiK,SAASA,CAAC6B,MAAM,EAAEpJ,KAAK,EAAE;IACrB,MAAMJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAACF,OAAO,CAACM,KAAK,CAAC;IACzC,IAAIJ,MAAM,EAAE;MACR,IAAI,CAACqC,YAAY,CAACC,GAAG,CAACtC,MAAM,CAAC;IACjC;EACJ;AACJ,CAAC;AACD2I,gCAAgC,GAAG3O,UAAU,CAAC,CAC1CgB,OAAO,CAAC,CAAC,EAAEgC,mBAAmB,CAAC,CAClC,EAAE2L,gCAAgC,CAAC;AACpC;AACA;AACA;AACA,OAAO,SAASc,oBAAoBA,CAACC,YAAY,EAAE1J,MAAM,EAAE1B,OAAO,EAAE;EAChE,IAAI0B,MAAM,YAAYrD,cAAc,EAAE;IAClC,OAAO+M,YAAY,CAACxC,cAAc,CAAC3G,uBAAuB,EAAEP,MAAM,EAAE1B,OAAO,CAAC;EAChF,CAAC,MACI,IAAI0B,MAAM,YAAYpD,iBAAiB,EAAE;IAC1C,IAAIoD,MAAM,CAACmB,IAAI,CAACwI,WAAW,EAAE;MACzB,OAAOD,YAAY,CAACxC,cAAc,CAACyB,gCAAgC,EAAE3I,MAAM,CAAC;IAChF,CAAC,MACI;MACD,IAAIA,MAAM,CAACmB,IAAI,CAACyI,qBAAqB,EAAE;QACnC,OAAOF,YAAY,CAACxC,cAAc,CAACf,iCAAiC,EAAEnG,MAAM,EAAE;UAAE,GAAG1B,OAAO;UAAEuI,mBAAmB,EAAE;QAAK,CAAC,CAAC;MAC5H,CAAC,MACI;QACD,OAAO6C,YAAY,CAACxC,cAAc,CAACtB,0BAA0B,EAAE5F,MAAM,EAAE1B,OAAO,CAAC;MACnF;IACJ;EACJ,CAAC,MACI;IACD,OAAO0C,SAAS;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}