{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { addDisposableListener, addStandardDisposableListener, reset } from '../../../../../base/browser/dom.js';\nimport { createTrustedTypesPolicy } from '../../../../../base/browser/trustedTypes.js';\nimport { ActionBar } from '../../../../../base/browser/ui/actionbar/actionbar.js';\nimport { DomScrollableElement } from '../../../../../base/browser/ui/scrollbar/scrollableElement.js';\nimport { Action } from '../../../../../base/common/actions.js';\nimport { forEachAdjacent, groupAdjacentBy } from '../../../../../base/common/arrays.js';\nimport { Codicon } from '../../../../../base/common/codicons.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, derivedWithStore, observableValue, subtransaction, transaction } from '../../../../../base/common/observable.js';\nimport { ThemeIcon } from '../../../../../base/common/themables.js';\nimport { applyFontInfo } from '../../../config/domFontInfo.js';\nimport { applyStyle } from '../utils.js';\nimport { EditorFontLigatures } from '../../../../common/config/editorOptions.js';\nimport { LineRange } from '../../../../common/core/lineRange.js';\nimport { OffsetRange } from '../../../../common/core/offsetRange.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { LineRangeMapping } from '../../../../common/diff/rangeMapping.js';\nimport { ILanguageService } from '../../../../common/languages/language.js';\nimport { LineTokens } from '../../../../common/tokens/lineTokens.js';\nimport { RenderLineInput, renderViewLine2 } from '../../../../common/viewLayout/viewLineRenderer.js';\nimport { ViewLineRenderingData } from '../../../../common/viewModel.js';\nimport { localize } from '../../../../../nls.js';\nimport { AccessibilitySignal, IAccessibilitySignalService } from '../../../../../platform/accessibilitySignal/browser/accessibilitySignalService.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { registerIcon } from '../../../../../platform/theme/common/iconRegistry.js';\nimport './accessibleDiffViewer.css';\nconst accessibleDiffViewerInsertIcon = registerIcon('diff-review-insert', Codicon.add, localize('accessibleDiffViewerInsertIcon', 'Icon for \\'Insert\\' in accessible diff viewer.'));\nconst accessibleDiffViewerRemoveIcon = registerIcon('diff-review-remove', Codicon.remove, localize('accessibleDiffViewerRemoveIcon', 'Icon for \\'Remove\\' in accessible diff viewer.'));\nconst accessibleDiffViewerCloseIcon = registerIcon('diff-review-close', Codicon.close, localize('accessibleDiffViewerCloseIcon', 'Icon for \\'Close\\' in accessible diff viewer.'));\nlet AccessibleDiffViewer = class AccessibleDiffViewer extends Disposable {\n  static {\n    this._ttPolicy = createTrustedTypesPolicy('diffReview', {\n      createHTML: value => value\n    });\n  }\n  constructor(_parentNode, _visible, _setVisible, _canClose, _width, _height, _diffs, _models, _instantiationService) {\n    super();\n    this._parentNode = _parentNode;\n    this._visible = _visible;\n    this._setVisible = _setVisible;\n    this._canClose = _canClose;\n    this._width = _width;\n    this._height = _height;\n    this._diffs = _diffs;\n    this._models = _models;\n    this._instantiationService = _instantiationService;\n    this._state = derivedWithStore(this, (reader, store) => {\n      const visible = this._visible.read(reader);\n      this._parentNode.style.visibility = visible ? 'visible' : 'hidden';\n      if (!visible) {\n        return null;\n      }\n      const model = store.add(this._instantiationService.createInstance(ViewModel, this._diffs, this._models, this._setVisible, this._canClose));\n      const view = store.add(this._instantiationService.createInstance(View, this._parentNode, model, this._width, this._height, this._models));\n      return {\n        model,\n        view\n      };\n    }).recomputeInitiallyAndOnChange(this._store);\n  }\n  next() {\n    transaction(tx => {\n      const isVisible = this._visible.get();\n      this._setVisible(true, tx);\n      if (isVisible) {\n        this._state.get().model.nextGroup(tx);\n      }\n    });\n  }\n  prev() {\n    transaction(tx => {\n      this._setVisible(true, tx);\n      this._state.get().model.previousGroup(tx);\n    });\n  }\n  close() {\n    transaction(tx => {\n      this._setVisible(false, tx);\n    });\n  }\n};\nAccessibleDiffViewer = __decorate([__param(8, IInstantiationService)], AccessibleDiffViewer);\nexport { AccessibleDiffViewer };\nlet ViewModel = class ViewModel extends Disposable {\n  constructor(_diffs, _models, _setVisible, canClose, _accessibilitySignalService) {\n    super();\n    this._diffs = _diffs;\n    this._models = _models;\n    this._setVisible = _setVisible;\n    this.canClose = canClose;\n    this._accessibilitySignalService = _accessibilitySignalService;\n    this._groups = observableValue(this, []);\n    this._currentGroupIdx = observableValue(this, 0);\n    this._currentElementIdx = observableValue(this, 0);\n    this.groups = this._groups;\n    this.currentGroup = this._currentGroupIdx.map((idx, r) => this._groups.read(r)[idx]);\n    this.currentGroupIndex = this._currentGroupIdx;\n    this.currentElement = this._currentElementIdx.map((idx, r) => this.currentGroup.read(r)?.lines[idx]);\n    this._register(autorun(reader => {\n      /** @description update groups */\n      const diffs = this._diffs.read(reader);\n      if (!diffs) {\n        this._groups.set([], undefined);\n        return;\n      }\n      const groups = computeViewElementGroups(diffs, this._models.getOriginalModel().getLineCount(), this._models.getModifiedModel().getLineCount());\n      transaction(tx => {\n        const p = this._models.getModifiedPosition();\n        if (p) {\n          const nextGroup = groups.findIndex(g => p?.lineNumber < g.range.modified.endLineNumberExclusive);\n          if (nextGroup !== -1) {\n            this._currentGroupIdx.set(nextGroup, tx);\n          }\n        }\n        this._groups.set(groups, tx);\n      });\n    }));\n    this._register(autorun(reader => {\n      /** @description play audio-cue for diff */\n      const currentViewItem = this.currentElement.read(reader);\n      if (currentViewItem?.type === LineType.Deleted) {\n        this._accessibilitySignalService.playSignal(AccessibilitySignal.diffLineDeleted, {\n          source: 'accessibleDiffViewer.currentElementChanged'\n        });\n      } else if (currentViewItem?.type === LineType.Added) {\n        this._accessibilitySignalService.playSignal(AccessibilitySignal.diffLineInserted, {\n          source: 'accessibleDiffViewer.currentElementChanged'\n        });\n      }\n    }));\n    this._register(autorun(reader => {\n      /** @description select lines in editor */\n      // This ensures editor commands (like revert/stage) work\n      const currentViewItem = this.currentElement.read(reader);\n      if (currentViewItem && currentViewItem.type !== LineType.Header) {\n        const lineNumber = currentViewItem.modifiedLineNumber ?? currentViewItem.diff.modified.startLineNumber;\n        this._models.modifiedSetSelection(Range.fromPositions(new Position(lineNumber, 1)));\n      }\n    }));\n  }\n  _goToGroupDelta(delta, tx) {\n    const groups = this.groups.get();\n    if (!groups || groups.length <= 1) {\n      return;\n    }\n    subtransaction(tx, tx => {\n      this._currentGroupIdx.set(OffsetRange.ofLength(groups.length).clipCyclic(this._currentGroupIdx.get() + delta), tx);\n      this._currentElementIdx.set(0, tx);\n    });\n  }\n  nextGroup(tx) {\n    this._goToGroupDelta(1, tx);\n  }\n  previousGroup(tx) {\n    this._goToGroupDelta(-1, tx);\n  }\n  _goToLineDelta(delta) {\n    const group = this.currentGroup.get();\n    if (!group || group.lines.length <= 1) {\n      return;\n    }\n    transaction(tx => {\n      this._currentElementIdx.set(OffsetRange.ofLength(group.lines.length).clip(this._currentElementIdx.get() + delta), tx);\n    });\n  }\n  goToNextLine() {\n    this._goToLineDelta(1);\n  }\n  goToPreviousLine() {\n    this._goToLineDelta(-1);\n  }\n  goToLine(line) {\n    const group = this.currentGroup.get();\n    if (!group) {\n      return;\n    }\n    const idx = group.lines.indexOf(line);\n    if (idx === -1) {\n      return;\n    }\n    transaction(tx => {\n      this._currentElementIdx.set(idx, tx);\n    });\n  }\n  revealCurrentElementInEditor() {\n    if (!this.canClose.get()) {\n      return;\n    }\n    this._setVisible(false, undefined);\n    const curElem = this.currentElement.get();\n    if (curElem) {\n      if (curElem.type === LineType.Deleted) {\n        this._models.originalReveal(Range.fromPositions(new Position(curElem.originalLineNumber, 1)));\n      } else {\n        this._models.modifiedReveal(curElem.type !== LineType.Header ? Range.fromPositions(new Position(curElem.modifiedLineNumber, 1)) : undefined);\n      }\n    }\n  }\n  close() {\n    if (!this.canClose.get()) {\n      return;\n    }\n    this._setVisible(false, undefined);\n    this._models.modifiedFocus();\n  }\n};\nViewModel = __decorate([__param(4, IAccessibilitySignalService)], ViewModel);\nconst viewElementGroupLineMargin = 3;\nfunction computeViewElementGroups(diffs, originalLineCount, modifiedLineCount) {\n  const result = [];\n  for (const g of groupAdjacentBy(diffs, (a, b) => b.modified.startLineNumber - a.modified.endLineNumberExclusive < 2 * viewElementGroupLineMargin)) {\n    const viewElements = [];\n    viewElements.push(new HeaderViewElement());\n    const origFullRange = new LineRange(Math.max(1, g[0].original.startLineNumber - viewElementGroupLineMargin), Math.min(g[g.length - 1].original.endLineNumberExclusive + viewElementGroupLineMargin, originalLineCount + 1));\n    const modifiedFullRange = new LineRange(Math.max(1, g[0].modified.startLineNumber - viewElementGroupLineMargin), Math.min(g[g.length - 1].modified.endLineNumberExclusive + viewElementGroupLineMargin, modifiedLineCount + 1));\n    forEachAdjacent(g, (a, b) => {\n      const origRange = new LineRange(a ? a.original.endLineNumberExclusive : origFullRange.startLineNumber, b ? b.original.startLineNumber : origFullRange.endLineNumberExclusive);\n      const modifiedRange = new LineRange(a ? a.modified.endLineNumberExclusive : modifiedFullRange.startLineNumber, b ? b.modified.startLineNumber : modifiedFullRange.endLineNumberExclusive);\n      origRange.forEach(origLineNumber => {\n        viewElements.push(new UnchangedLineViewElement(origLineNumber, modifiedRange.startLineNumber + (origLineNumber - origRange.startLineNumber)));\n      });\n      if (b) {\n        b.original.forEach(origLineNumber => {\n          viewElements.push(new DeletedLineViewElement(b, origLineNumber));\n        });\n        b.modified.forEach(modifiedLineNumber => {\n          viewElements.push(new AddedLineViewElement(b, modifiedLineNumber));\n        });\n      }\n    });\n    const modifiedRange = g[0].modified.join(g[g.length - 1].modified);\n    const originalRange = g[0].original.join(g[g.length - 1].original);\n    result.push(new ViewElementGroup(new LineRangeMapping(modifiedRange, originalRange), viewElements));\n  }\n  return result;\n}\nvar LineType = /*#__PURE__*/function (LineType) {\n  LineType[LineType[\"Header\"] = 0] = \"Header\";\n  LineType[LineType[\"Unchanged\"] = 1] = \"Unchanged\";\n  LineType[LineType[\"Deleted\"] = 2] = \"Deleted\";\n  LineType[LineType[\"Added\"] = 3] = \"Added\";\n  return LineType;\n}(LineType || {});\nclass ViewElementGroup {\n  constructor(range, lines) {\n    this.range = range;\n    this.lines = lines;\n  }\n}\nclass HeaderViewElement {\n  constructor() {\n    this.type = LineType.Header;\n  }\n}\nclass DeletedLineViewElement {\n  constructor(diff, originalLineNumber) {\n    this.diff = diff;\n    this.originalLineNumber = originalLineNumber;\n    this.type = LineType.Deleted;\n    this.modifiedLineNumber = undefined;\n  }\n}\nclass AddedLineViewElement {\n  constructor(diff, modifiedLineNumber) {\n    this.diff = diff;\n    this.modifiedLineNumber = modifiedLineNumber;\n    this.type = LineType.Added;\n    this.originalLineNumber = undefined;\n  }\n}\nclass UnchangedLineViewElement {\n  constructor(originalLineNumber, modifiedLineNumber) {\n    this.originalLineNumber = originalLineNumber;\n    this.modifiedLineNumber = modifiedLineNumber;\n    this.type = LineType.Unchanged;\n  }\n}\nlet View = class View extends Disposable {\n  constructor(_element, _model, _width, _height, _models, _languageService) {\n    super();\n    this._element = _element;\n    this._model = _model;\n    this._width = _width;\n    this._height = _height;\n    this._models = _models;\n    this._languageService = _languageService;\n    this.domNode = this._element;\n    this.domNode.className = 'monaco-component diff-review monaco-editor-background';\n    const actionBarContainer = document.createElement('div');\n    actionBarContainer.className = 'diff-review-actions';\n    this._actionBar = this._register(new ActionBar(actionBarContainer));\n    this._register(autorun(reader => {\n      /** @description update actions */\n      this._actionBar.clear();\n      if (this._model.canClose.read(reader)) {\n        this._actionBar.push(new Action('diffreview.close', localize('label.close', \"Close\"), 'close-diff-review ' + ThemeIcon.asClassName(accessibleDiffViewerCloseIcon), true, /*#__PURE__*/_asyncToGenerator(function* () {\n          return _model.close();\n        })), {\n          label: false,\n          icon: true\n        });\n      }\n    }));\n    this._content = document.createElement('div');\n    this._content.className = 'diff-review-content';\n    this._content.setAttribute('role', 'code');\n    this._scrollbar = this._register(new DomScrollableElement(this._content, {}));\n    reset(this.domNode, this._scrollbar.getDomNode(), actionBarContainer);\n    this._register(autorun(r => {\n      this._height.read(r);\n      this._width.read(r);\n      this._scrollbar.scanDomNode();\n    }));\n    this._register(toDisposable(() => {\n      reset(this.domNode);\n    }));\n    this._register(applyStyle(this.domNode, {\n      width: this._width,\n      height: this._height\n    }));\n    this._register(applyStyle(this._content, {\n      width: this._width,\n      height: this._height\n    }));\n    this._register(autorunWithStore((reader, store) => {\n      /** @description render */\n      this._model.currentGroup.read(reader);\n      this._render(store);\n    }));\n    // TODO@hediet use commands\n    this._register(addStandardDisposableListener(this.domNode, 'keydown', e => {\n      if (e.equals(18 /* KeyCode.DownArrow */) || e.equals(2048 /* KeyMod.CtrlCmd */ | 18 /* KeyCode.DownArrow */) || e.equals(512 /* KeyMod.Alt */ | 18 /* KeyCode.DownArrow */)) {\n        e.preventDefault();\n        this._model.goToNextLine();\n      }\n      if (e.equals(16 /* KeyCode.UpArrow */) || e.equals(2048 /* KeyMod.CtrlCmd */ | 16 /* KeyCode.UpArrow */) || e.equals(512 /* KeyMod.Alt */ | 16 /* KeyCode.UpArrow */)) {\n        e.preventDefault();\n        this._model.goToPreviousLine();\n      }\n      if (e.equals(9 /* KeyCode.Escape */) || e.equals(2048 /* KeyMod.CtrlCmd */ | 9 /* KeyCode.Escape */) || e.equals(512 /* KeyMod.Alt */ | 9 /* KeyCode.Escape */) || e.equals(1024 /* KeyMod.Shift */ | 9 /* KeyCode.Escape */)) {\n        e.preventDefault();\n        this._model.close();\n      }\n      if (e.equals(10 /* KeyCode.Space */) || e.equals(3 /* KeyCode.Enter */)) {\n        e.preventDefault();\n        this._model.revealCurrentElementInEditor();\n      }\n    }));\n  }\n  _render(store) {\n    const originalOptions = this._models.getOriginalOptions();\n    const modifiedOptions = this._models.getModifiedOptions();\n    const container = document.createElement('div');\n    container.className = 'diff-review-table';\n    container.setAttribute('role', 'list');\n    container.setAttribute('aria-label', localize('ariaLabel', 'Accessible Diff Viewer. Use arrow up and down to navigate.'));\n    applyFontInfo(container, modifiedOptions.get(50 /* EditorOption.fontInfo */));\n    reset(this._content, container);\n    const originalModel = this._models.getOriginalModel();\n    const modifiedModel = this._models.getModifiedModel();\n    if (!originalModel || !modifiedModel) {\n      return;\n    }\n    const originalModelOpts = originalModel.getOptions();\n    const modifiedModelOpts = modifiedModel.getOptions();\n    const lineHeight = modifiedOptions.get(67 /* EditorOption.lineHeight */);\n    const group = this._model.currentGroup.get();\n    for (const viewItem of group?.lines || []) {\n      if (!group) {\n        break;\n      }\n      let row;\n      if (viewItem.type === LineType.Header) {\n        const header = document.createElement('div');\n        header.className = 'diff-review-row';\n        header.setAttribute('role', 'listitem');\n        const r = group.range;\n        const diffIndex = this._model.currentGroupIndex.get();\n        const diffsLength = this._model.groups.get().length;\n        const getAriaLines = lines => lines === 0 ? localize('no_lines_changed', \"no lines changed\") : lines === 1 ? localize('one_line_changed', \"1 line changed\") : localize('more_lines_changed', \"{0} lines changed\", lines);\n        const originalChangedLinesCntAria = getAriaLines(r.original.length);\n        const modifiedChangedLinesCntAria = getAriaLines(r.modified.length);\n        header.setAttribute('aria-label', localize({\n          key: 'header',\n          comment: ['This is the ARIA label for a git diff header.', 'A git diff header looks like this: @@ -154,12 +159,39 @@.', 'That encodes that at original line 154 (which is now line 159), 12 lines were removed/changed with 39 lines.', 'Variables 0 and 1 refer to the diff index out of total number of diffs.', 'Variables 2 and 4 will be numbers (a line number).', 'Variables 3 and 5 will be \"no lines changed\", \"1 line changed\" or \"X lines changed\", localized separately.']\n        }, \"Difference {0} of {1}: original line {2}, {3}, modified line {4}, {5}\", diffIndex + 1, diffsLength, r.original.startLineNumber, originalChangedLinesCntAria, r.modified.startLineNumber, modifiedChangedLinesCntAria));\n        const cell = document.createElement('div');\n        cell.className = 'diff-review-cell diff-review-summary';\n        // e.g.: `1/10: @@ -504,7 +517,7 @@`\n        cell.appendChild(document.createTextNode(`${diffIndex + 1}/${diffsLength}: @@ -${r.original.startLineNumber},${r.original.length} +${r.modified.startLineNumber},${r.modified.length} @@`));\n        header.appendChild(cell);\n        row = header;\n      } else {\n        row = this._createRow(viewItem, lineHeight, this._width.get(), originalOptions, originalModel, originalModelOpts, modifiedOptions, modifiedModel, modifiedModelOpts);\n      }\n      container.appendChild(row);\n      const isSelectedObs = derived(reader => /** @description isSelected */this._model.currentElement.read(reader) === viewItem);\n      store.add(autorun(reader => {\n        /** @description update tab index */\n        const isSelected = isSelectedObs.read(reader);\n        row.tabIndex = isSelected ? 0 : -1;\n        if (isSelected) {\n          row.focus();\n        }\n      }));\n      store.add(addDisposableListener(row, 'focus', () => {\n        this._model.goToLine(viewItem);\n      }));\n    }\n    this._scrollbar.scanDomNode();\n  }\n  _createRow(item, lineHeight, width, originalOptions, originalModel, originalModelOpts, modifiedOptions, modifiedModel, modifiedModelOpts) {\n    const originalLayoutInfo = originalOptions.get(146 /* EditorOption.layoutInfo */);\n    const originalLineNumbersWidth = originalLayoutInfo.glyphMarginWidth + originalLayoutInfo.lineNumbersWidth;\n    const modifiedLayoutInfo = modifiedOptions.get(146 /* EditorOption.layoutInfo */);\n    const modifiedLineNumbersWidth = 10 + modifiedLayoutInfo.glyphMarginWidth + modifiedLayoutInfo.lineNumbersWidth;\n    let rowClassName = 'diff-review-row';\n    let lineNumbersExtraClassName = '';\n    const spacerClassName = 'diff-review-spacer';\n    let spacerIcon = null;\n    switch (item.type) {\n      case LineType.Added:\n        rowClassName = 'diff-review-row line-insert';\n        lineNumbersExtraClassName = ' char-insert';\n        spacerIcon = accessibleDiffViewerInsertIcon;\n        break;\n      case LineType.Deleted:\n        rowClassName = 'diff-review-row line-delete';\n        lineNumbersExtraClassName = ' char-delete';\n        spacerIcon = accessibleDiffViewerRemoveIcon;\n        break;\n    }\n    const row = document.createElement('div');\n    row.style.minWidth = width + 'px';\n    row.className = rowClassName;\n    row.setAttribute('role', 'listitem');\n    row.ariaLevel = '';\n    const cell = document.createElement('div');\n    cell.className = 'diff-review-cell';\n    cell.style.height = `${lineHeight}px`;\n    row.appendChild(cell);\n    const originalLineNumber = document.createElement('span');\n    originalLineNumber.style.width = originalLineNumbersWidth + 'px';\n    originalLineNumber.style.minWidth = originalLineNumbersWidth + 'px';\n    originalLineNumber.className = 'diff-review-line-number' + lineNumbersExtraClassName;\n    if (item.originalLineNumber !== undefined) {\n      originalLineNumber.appendChild(document.createTextNode(String(item.originalLineNumber)));\n    } else {\n      originalLineNumber.innerText = '\\u00a0';\n    }\n    cell.appendChild(originalLineNumber);\n    const modifiedLineNumber = document.createElement('span');\n    modifiedLineNumber.style.width = modifiedLineNumbersWidth + 'px';\n    modifiedLineNumber.style.minWidth = modifiedLineNumbersWidth + 'px';\n    modifiedLineNumber.style.paddingRight = '10px';\n    modifiedLineNumber.className = 'diff-review-line-number' + lineNumbersExtraClassName;\n    if (item.modifiedLineNumber !== undefined) {\n      modifiedLineNumber.appendChild(document.createTextNode(String(item.modifiedLineNumber)));\n    } else {\n      modifiedLineNumber.innerText = '\\u00a0';\n    }\n    cell.appendChild(modifiedLineNumber);\n    const spacer = document.createElement('span');\n    spacer.className = spacerClassName;\n    if (spacerIcon) {\n      const spacerCodicon = document.createElement('span');\n      spacerCodicon.className = ThemeIcon.asClassName(spacerIcon);\n      spacerCodicon.innerText = '\\u00a0\\u00a0';\n      spacer.appendChild(spacerCodicon);\n    } else {\n      spacer.innerText = '\\u00a0\\u00a0';\n    }\n    cell.appendChild(spacer);\n    let lineContent;\n    if (item.modifiedLineNumber !== undefined) {\n      let html = this._getLineHtml(modifiedModel, modifiedOptions, modifiedModelOpts.tabSize, item.modifiedLineNumber, this._languageService.languageIdCodec);\n      if (AccessibleDiffViewer._ttPolicy) {\n        html = AccessibleDiffViewer._ttPolicy.createHTML(html);\n      }\n      cell.insertAdjacentHTML('beforeend', html);\n      lineContent = modifiedModel.getLineContent(item.modifiedLineNumber);\n    } else {\n      let html = this._getLineHtml(originalModel, originalOptions, originalModelOpts.tabSize, item.originalLineNumber, this._languageService.languageIdCodec);\n      if (AccessibleDiffViewer._ttPolicy) {\n        html = AccessibleDiffViewer._ttPolicy.createHTML(html);\n      }\n      cell.insertAdjacentHTML('beforeend', html);\n      lineContent = originalModel.getLineContent(item.originalLineNumber);\n    }\n    if (lineContent.length === 0) {\n      lineContent = localize('blankLine', \"blank\");\n    }\n    let ariaLabel = '';\n    switch (item.type) {\n      case LineType.Unchanged:\n        if (item.originalLineNumber === item.modifiedLineNumber) {\n          ariaLabel = localize({\n            key: 'unchangedLine',\n            comment: ['The placeholders are contents of the line and should not be translated.']\n          }, \"{0} unchanged line {1}\", lineContent, item.originalLineNumber);\n        } else {\n          ariaLabel = localize('equalLine', \"{0} original line {1} modified line {2}\", lineContent, item.originalLineNumber, item.modifiedLineNumber);\n        }\n        break;\n      case LineType.Added:\n        ariaLabel = localize('insertLine', \"+ {0} modified line {1}\", lineContent, item.modifiedLineNumber);\n        break;\n      case LineType.Deleted:\n        ariaLabel = localize('deleteLine', \"- {0} original line {1}\", lineContent, item.originalLineNumber);\n        break;\n    }\n    row.setAttribute('aria-label', ariaLabel);\n    return row;\n  }\n  _getLineHtml(model, options, tabSize, lineNumber, languageIdCodec) {\n    const lineContent = model.getLineContent(lineNumber);\n    const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n    const lineTokens = LineTokens.createEmpty(lineContent, languageIdCodec);\n    const isBasicASCII = ViewLineRenderingData.isBasicASCII(lineContent, model.mightContainNonBasicASCII());\n    const containsRTL = ViewLineRenderingData.containsRTL(lineContent, isBasicASCII, model.mightContainRTL());\n    const r = renderViewLine2(new RenderLineInput(fontInfo.isMonospace && !options.get(33 /* EditorOption.disableMonospaceOptimizations */), fontInfo.canUseHalfwidthRightwardsArrow, lineContent, false, isBasicASCII, containsRTL, 0, lineTokens, [], tabSize, 0, fontInfo.spaceWidth, fontInfo.middotWidth, fontInfo.wsmiddotWidth, options.get(118 /* EditorOption.stopRenderingLineAfter */), options.get(100 /* EditorOption.renderWhitespace */), options.get(95 /* EditorOption.renderControlCharacters */), options.get(51 /* EditorOption.fontLigatures */) !== EditorFontLigatures.OFF, null));\n    return r.html;\n  }\n};\nView = __decorate([__param(5, ILanguageService)], View);\nexport class AccessibleDiffViewerModelFromEditors {\n  constructor(editors) {\n    this.editors = editors;\n  }\n  getOriginalModel() {\n    return this.editors.original.getModel();\n  }\n  getOriginalOptions() {\n    return this.editors.original.getOptions();\n  }\n  originalReveal(range) {\n    this.editors.original.revealRange(range);\n    this.editors.original.setSelection(range);\n    this.editors.original.focus();\n  }\n  getModifiedModel() {\n    return this.editors.modified.getModel();\n  }\n  getModifiedOptions() {\n    return this.editors.modified.getOptions();\n  }\n  modifiedReveal(range) {\n    if (range) {\n      this.editors.modified.revealRange(range);\n      this.editors.modified.setSelection(range);\n    }\n    this.editors.modified.focus();\n  }\n  modifiedSetSelection(range) {\n    this.editors.modified.setSelection(range);\n  }\n  modifiedFocus() {\n    this.editors.modified.focus();\n  }\n  getModifiedPosition() {\n    return this.editors.modified.getPosition() ?? undefined;\n  }\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "addDisposableListener", "addStandardDisposableListener", "reset", "createTrustedTypesPolicy", "ActionBar", "DomScrollableElement", "Action", "forEachAdjacent", "groupAdjacentBy", "Codicon", "Disposable", "toDisposable", "autorun", "autorunWithStore", "derived", "derivedWithStore", "observableValue", "subtransaction", "transaction", "ThemeIcon", "applyFontInfo", "applyStyle", "EditorFontLigatures", "LineRange", "OffsetRange", "Position", "Range", "LineRangeMapping", "ILanguageService", "LineTokens", "RenderLineInput", "renderViewLine2", "ViewLineRenderingData", "localize", "AccessibilitySignal", "IAccessibilitySignalService", "IInstantiationService", "registerIcon", "accessibleDiffViewerInsertIcon", "add", "accessibleDiffViewerRemoveIcon", "remove", "accessibleDiffViewerCloseIcon", "close", "AccessibleDiffViewer", "_ttPolicy", "createHTML", "value", "constructor", "_parentNode", "_visible", "_setVisible", "_canClose", "_width", "_height", "_diffs", "_models", "_instantiationService", "_state", "reader", "store", "visible", "read", "style", "visibility", "model", "createInstance", "ViewModel", "view", "View", "recomputeInitiallyAndOnChange", "_store", "next", "tx", "isVisible", "get", "nextGroup", "prev", "previousGroup", "canClose", "_accessibilitySignalService", "_groups", "_currentGroupIdx", "_currentElementIdx", "groups", "currentGroup", "map", "idx", "currentGroupIndex", "currentElement", "lines", "_register", "diffs", "set", "undefined", "computeViewElementGroups", "getOriginalModel", "getLineCount", "getModifiedModel", "p", "getModifiedPosition", "findIndex", "g", "lineNumber", "range", "modified", "endLineNumberExclusive", "currentViewItem", "type", "LineType", "Deleted", "playSignal", "diffLineDeleted", "source", "Added", "diffLineInserted", "Header", "modifiedLineNumber", "diff", "startLineNumber", "modifiedSetSelection", "fromPositions", "_goToGroupDelta", "delta", "of<PERSON>ength", "clipCyclic", "_goToLineDelta", "group", "clip", "goToNextLine", "goToPreviousLine", "goToLine", "line", "indexOf", "revealCurrentElementInEditor", "curE<PERSON>", "originalReveal", "originalLineNumber", "modifiedReveal", "modifiedFocus", "viewElementGroupLineMargin", "originalLineCount", "modifiedLineCount", "result", "a", "b", "viewElements", "push", "Head<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "origFullRange", "Math", "max", "original", "min", "modifiedFullRange", "origRange", "modifiedRange", "for<PERSON>ach", "origLineNumber", "UnchangedLineViewElement", "DeletedLineViewElement", "AddedLineViewElement", "join", "originalRange", "ViewElementGroup", "Unchanged", "_element", "_model", "_languageService", "domNode", "className", "actionBarContainer", "document", "createElement", "_actionBar", "clear", "asClassName", "_asyncToGenerator", "label", "icon", "_content", "setAttribute", "_scrollbar", "getDomNode", "scanDomNode", "width", "height", "_render", "e", "equals", "preventDefault", "originalOptions", "getOriginalOptions", "modifiedOptions", "getModifiedOptions", "container", "originalModel", "modifiedModel", "originalModelOpts", "getOptions", "modifiedModelOpts", "lineHeight", "viewItem", "row", "header", "diffIndex", "diffsLength", "getAriaLines", "originalChangedLinesCntAria", "modifiedChangedLinesCntAria", "comment", "cell", "append<PERSON><PERSON><PERSON>", "createTextNode", "_createRow", "isSelectedObs", "isSelected", "tabIndex", "focus", "item", "originalLayoutInfo", "originalLineNumbersWidth", "glyph<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumbersWidth", "modifiedLayoutInfo", "modifiedLineNumbersWidth", "rowClassName", "lineNumbersExtraClassName", "spacerClassName", "spacerIcon", "min<PERSON><PERSON><PERSON>", "ariaLevel", "String", "innerText", "paddingRight", "spacer", "spacerCodicon", "lineContent", "html", "_getLineHtml", "tabSize", "languageIdCodec", "insertAdjacentHTML", "get<PERSON>ineC<PERSON>nt", "aria<PERSON><PERSON><PERSON>", "options", "fontInfo", "lineTokens", "createEmpty", "isBasicASCII", "mightContainNonBasicASCII", "containsRTL", "mightContainRTL", "isMonospace", "canUseHalfwidthRightwardsArrow", "spaceWidth", "middotWidth", "wsmid<PERSON>t<PERSON><PERSON>th", "OFF", "AccessibleDiffViewerModelFromEditors", "editors", "getModel", "revealRange", "setSelection", "getPosition"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { addDisposableListener, addStandardDisposableListener, reset } from '../../../../../base/browser/dom.js';\nimport { createTrustedTypesPolicy } from '../../../../../base/browser/trustedTypes.js';\nimport { ActionBar } from '../../../../../base/browser/ui/actionbar/actionbar.js';\nimport { DomScrollableElement } from '../../../../../base/browser/ui/scrollbar/scrollableElement.js';\nimport { Action } from '../../../../../base/common/actions.js';\nimport { forEachAdjacent, groupAdjacentBy } from '../../../../../base/common/arrays.js';\nimport { Codicon } from '../../../../../base/common/codicons.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, derivedWithStore, observableValue, subtransaction, transaction } from '../../../../../base/common/observable.js';\nimport { ThemeIcon } from '../../../../../base/common/themables.js';\nimport { applyFontInfo } from '../../../config/domFontInfo.js';\nimport { applyStyle } from '../utils.js';\nimport { EditorFontLigatures } from '../../../../common/config/editorOptions.js';\nimport { LineRange } from '../../../../common/core/lineRange.js';\nimport { OffsetRange } from '../../../../common/core/offsetRange.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { LineRangeMapping } from '../../../../common/diff/rangeMapping.js';\nimport { ILanguageService } from '../../../../common/languages/language.js';\nimport { LineTokens } from '../../../../common/tokens/lineTokens.js';\nimport { RenderLineInput, renderViewLine2 } from '../../../../common/viewLayout/viewLineRenderer.js';\nimport { ViewLineRenderingData } from '../../../../common/viewModel.js';\nimport { localize } from '../../../../../nls.js';\nimport { AccessibilitySignal, IAccessibilitySignalService } from '../../../../../platform/accessibilitySignal/browser/accessibilitySignalService.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { registerIcon } from '../../../../../platform/theme/common/iconRegistry.js';\nimport './accessibleDiffViewer.css';\nconst accessibleDiffViewerInsertIcon = registerIcon('diff-review-insert', Codicon.add, localize('accessibleDiffViewerInsertIcon', 'Icon for \\'Insert\\' in accessible diff viewer.'));\nconst accessibleDiffViewerRemoveIcon = registerIcon('diff-review-remove', Codicon.remove, localize('accessibleDiffViewerRemoveIcon', 'Icon for \\'Remove\\' in accessible diff viewer.'));\nconst accessibleDiffViewerCloseIcon = registerIcon('diff-review-close', Codicon.close, localize('accessibleDiffViewerCloseIcon', 'Icon for \\'Close\\' in accessible diff viewer.'));\nlet AccessibleDiffViewer = class AccessibleDiffViewer extends Disposable {\n    static { this._ttPolicy = createTrustedTypesPolicy('diffReview', { createHTML: value => value }); }\n    constructor(_parentNode, _visible, _setVisible, _canClose, _width, _height, _diffs, _models, _instantiationService) {\n        super();\n        this._parentNode = _parentNode;\n        this._visible = _visible;\n        this._setVisible = _setVisible;\n        this._canClose = _canClose;\n        this._width = _width;\n        this._height = _height;\n        this._diffs = _diffs;\n        this._models = _models;\n        this._instantiationService = _instantiationService;\n        this._state = derivedWithStore(this, (reader, store) => {\n            const visible = this._visible.read(reader);\n            this._parentNode.style.visibility = visible ? 'visible' : 'hidden';\n            if (!visible) {\n                return null;\n            }\n            const model = store.add(this._instantiationService.createInstance(ViewModel, this._diffs, this._models, this._setVisible, this._canClose));\n            const view = store.add(this._instantiationService.createInstance(View, this._parentNode, model, this._width, this._height, this._models));\n            return { model, view, };\n        }).recomputeInitiallyAndOnChange(this._store);\n    }\n    next() {\n        transaction(tx => {\n            const isVisible = this._visible.get();\n            this._setVisible(true, tx);\n            if (isVisible) {\n                this._state.get().model.nextGroup(tx);\n            }\n        });\n    }\n    prev() {\n        transaction(tx => {\n            this._setVisible(true, tx);\n            this._state.get().model.previousGroup(tx);\n        });\n    }\n    close() {\n        transaction(tx => {\n            this._setVisible(false, tx);\n        });\n    }\n};\nAccessibleDiffViewer = __decorate([\n    __param(8, IInstantiationService)\n], AccessibleDiffViewer);\nexport { AccessibleDiffViewer };\nlet ViewModel = class ViewModel extends Disposable {\n    constructor(_diffs, _models, _setVisible, canClose, _accessibilitySignalService) {\n        super();\n        this._diffs = _diffs;\n        this._models = _models;\n        this._setVisible = _setVisible;\n        this.canClose = canClose;\n        this._accessibilitySignalService = _accessibilitySignalService;\n        this._groups = observableValue(this, []);\n        this._currentGroupIdx = observableValue(this, 0);\n        this._currentElementIdx = observableValue(this, 0);\n        this.groups = this._groups;\n        this.currentGroup = this._currentGroupIdx.map((idx, r) => this._groups.read(r)[idx]);\n        this.currentGroupIndex = this._currentGroupIdx;\n        this.currentElement = this._currentElementIdx.map((idx, r) => this.currentGroup.read(r)?.lines[idx]);\n        this._register(autorun(reader => {\n            /** @description update groups */\n            const diffs = this._diffs.read(reader);\n            if (!diffs) {\n                this._groups.set([], undefined);\n                return;\n            }\n            const groups = computeViewElementGroups(diffs, this._models.getOriginalModel().getLineCount(), this._models.getModifiedModel().getLineCount());\n            transaction(tx => {\n                const p = this._models.getModifiedPosition();\n                if (p) {\n                    const nextGroup = groups.findIndex(g => p?.lineNumber < g.range.modified.endLineNumberExclusive);\n                    if (nextGroup !== -1) {\n                        this._currentGroupIdx.set(nextGroup, tx);\n                    }\n                }\n                this._groups.set(groups, tx);\n            });\n        }));\n        this._register(autorun(reader => {\n            /** @description play audio-cue for diff */\n            const currentViewItem = this.currentElement.read(reader);\n            if (currentViewItem?.type === LineType.Deleted) {\n                this._accessibilitySignalService.playSignal(AccessibilitySignal.diffLineDeleted, { source: 'accessibleDiffViewer.currentElementChanged' });\n            }\n            else if (currentViewItem?.type === LineType.Added) {\n                this._accessibilitySignalService.playSignal(AccessibilitySignal.diffLineInserted, { source: 'accessibleDiffViewer.currentElementChanged' });\n            }\n        }));\n        this._register(autorun(reader => {\n            /** @description select lines in editor */\n            // This ensures editor commands (like revert/stage) work\n            const currentViewItem = this.currentElement.read(reader);\n            if (currentViewItem && currentViewItem.type !== LineType.Header) {\n                const lineNumber = currentViewItem.modifiedLineNumber ?? currentViewItem.diff.modified.startLineNumber;\n                this._models.modifiedSetSelection(Range.fromPositions(new Position(lineNumber, 1)));\n            }\n        }));\n    }\n    _goToGroupDelta(delta, tx) {\n        const groups = this.groups.get();\n        if (!groups || groups.length <= 1) {\n            return;\n        }\n        subtransaction(tx, tx => {\n            this._currentGroupIdx.set(OffsetRange.ofLength(groups.length).clipCyclic(this._currentGroupIdx.get() + delta), tx);\n            this._currentElementIdx.set(0, tx);\n        });\n    }\n    nextGroup(tx) { this._goToGroupDelta(1, tx); }\n    previousGroup(tx) { this._goToGroupDelta(-1, tx); }\n    _goToLineDelta(delta) {\n        const group = this.currentGroup.get();\n        if (!group || group.lines.length <= 1) {\n            return;\n        }\n        transaction(tx => {\n            this._currentElementIdx.set(OffsetRange.ofLength(group.lines.length).clip(this._currentElementIdx.get() + delta), tx);\n        });\n    }\n    goToNextLine() { this._goToLineDelta(1); }\n    goToPreviousLine() { this._goToLineDelta(-1); }\n    goToLine(line) {\n        const group = this.currentGroup.get();\n        if (!group) {\n            return;\n        }\n        const idx = group.lines.indexOf(line);\n        if (idx === -1) {\n            return;\n        }\n        transaction(tx => {\n            this._currentElementIdx.set(idx, tx);\n        });\n    }\n    revealCurrentElementInEditor() {\n        if (!this.canClose.get()) {\n            return;\n        }\n        this._setVisible(false, undefined);\n        const curElem = this.currentElement.get();\n        if (curElem) {\n            if (curElem.type === LineType.Deleted) {\n                this._models.originalReveal(Range.fromPositions(new Position(curElem.originalLineNumber, 1)));\n            }\n            else {\n                this._models.modifiedReveal(curElem.type !== LineType.Header\n                    ? Range.fromPositions(new Position(curElem.modifiedLineNumber, 1))\n                    : undefined);\n            }\n        }\n    }\n    close() {\n        if (!this.canClose.get()) {\n            return;\n        }\n        this._setVisible(false, undefined);\n        this._models.modifiedFocus();\n    }\n};\nViewModel = __decorate([\n    __param(4, IAccessibilitySignalService)\n], ViewModel);\nconst viewElementGroupLineMargin = 3;\nfunction computeViewElementGroups(diffs, originalLineCount, modifiedLineCount) {\n    const result = [];\n    for (const g of groupAdjacentBy(diffs, (a, b) => (b.modified.startLineNumber - a.modified.endLineNumberExclusive < 2 * viewElementGroupLineMargin))) {\n        const viewElements = [];\n        viewElements.push(new HeaderViewElement());\n        const origFullRange = new LineRange(Math.max(1, g[0].original.startLineNumber - viewElementGroupLineMargin), Math.min(g[g.length - 1].original.endLineNumberExclusive + viewElementGroupLineMargin, originalLineCount + 1));\n        const modifiedFullRange = new LineRange(Math.max(1, g[0].modified.startLineNumber - viewElementGroupLineMargin), Math.min(g[g.length - 1].modified.endLineNumberExclusive + viewElementGroupLineMargin, modifiedLineCount + 1));\n        forEachAdjacent(g, (a, b) => {\n            const origRange = new LineRange(a ? a.original.endLineNumberExclusive : origFullRange.startLineNumber, b ? b.original.startLineNumber : origFullRange.endLineNumberExclusive);\n            const modifiedRange = new LineRange(a ? a.modified.endLineNumberExclusive : modifiedFullRange.startLineNumber, b ? b.modified.startLineNumber : modifiedFullRange.endLineNumberExclusive);\n            origRange.forEach(origLineNumber => {\n                viewElements.push(new UnchangedLineViewElement(origLineNumber, modifiedRange.startLineNumber + (origLineNumber - origRange.startLineNumber)));\n            });\n            if (b) {\n                b.original.forEach(origLineNumber => {\n                    viewElements.push(new DeletedLineViewElement(b, origLineNumber));\n                });\n                b.modified.forEach(modifiedLineNumber => {\n                    viewElements.push(new AddedLineViewElement(b, modifiedLineNumber));\n                });\n            }\n        });\n        const modifiedRange = g[0].modified.join(g[g.length - 1].modified);\n        const originalRange = g[0].original.join(g[g.length - 1].original);\n        result.push(new ViewElementGroup(new LineRangeMapping(modifiedRange, originalRange), viewElements));\n    }\n    return result;\n}\nvar LineType;\n(function (LineType) {\n    LineType[LineType[\"Header\"] = 0] = \"Header\";\n    LineType[LineType[\"Unchanged\"] = 1] = \"Unchanged\";\n    LineType[LineType[\"Deleted\"] = 2] = \"Deleted\";\n    LineType[LineType[\"Added\"] = 3] = \"Added\";\n})(LineType || (LineType = {}));\nclass ViewElementGroup {\n    constructor(range, lines) {\n        this.range = range;\n        this.lines = lines;\n    }\n}\nclass HeaderViewElement {\n    constructor() {\n        this.type = LineType.Header;\n    }\n}\nclass DeletedLineViewElement {\n    constructor(diff, originalLineNumber) {\n        this.diff = diff;\n        this.originalLineNumber = originalLineNumber;\n        this.type = LineType.Deleted;\n        this.modifiedLineNumber = undefined;\n    }\n}\nclass AddedLineViewElement {\n    constructor(diff, modifiedLineNumber) {\n        this.diff = diff;\n        this.modifiedLineNumber = modifiedLineNumber;\n        this.type = LineType.Added;\n        this.originalLineNumber = undefined;\n    }\n}\nclass UnchangedLineViewElement {\n    constructor(originalLineNumber, modifiedLineNumber) {\n        this.originalLineNumber = originalLineNumber;\n        this.modifiedLineNumber = modifiedLineNumber;\n        this.type = LineType.Unchanged;\n    }\n}\nlet View = class View extends Disposable {\n    constructor(_element, _model, _width, _height, _models, _languageService) {\n        super();\n        this._element = _element;\n        this._model = _model;\n        this._width = _width;\n        this._height = _height;\n        this._models = _models;\n        this._languageService = _languageService;\n        this.domNode = this._element;\n        this.domNode.className = 'monaco-component diff-review monaco-editor-background';\n        const actionBarContainer = document.createElement('div');\n        actionBarContainer.className = 'diff-review-actions';\n        this._actionBar = this._register(new ActionBar(actionBarContainer));\n        this._register(autorun(reader => {\n            /** @description update actions */\n            this._actionBar.clear();\n            if (this._model.canClose.read(reader)) {\n                this._actionBar.push(new Action('diffreview.close', localize('label.close', \"Close\"), 'close-diff-review ' + ThemeIcon.asClassName(accessibleDiffViewerCloseIcon), true, async () => _model.close()), { label: false, icon: true });\n            }\n        }));\n        this._content = document.createElement('div');\n        this._content.className = 'diff-review-content';\n        this._content.setAttribute('role', 'code');\n        this._scrollbar = this._register(new DomScrollableElement(this._content, {}));\n        reset(this.domNode, this._scrollbar.getDomNode(), actionBarContainer);\n        this._register(autorun(r => {\n            this._height.read(r);\n            this._width.read(r);\n            this._scrollbar.scanDomNode();\n        }));\n        this._register(toDisposable(() => { reset(this.domNode); }));\n        this._register(applyStyle(this.domNode, { width: this._width, height: this._height }));\n        this._register(applyStyle(this._content, { width: this._width, height: this._height }));\n        this._register(autorunWithStore((reader, store) => {\n            /** @description render */\n            this._model.currentGroup.read(reader);\n            this._render(store);\n        }));\n        // TODO@hediet use commands\n        this._register(addStandardDisposableListener(this.domNode, 'keydown', (e) => {\n            if (e.equals(18 /* KeyCode.DownArrow */)\n                || e.equals(2048 /* KeyMod.CtrlCmd */ | 18 /* KeyCode.DownArrow */)\n                || e.equals(512 /* KeyMod.Alt */ | 18 /* KeyCode.DownArrow */)) {\n                e.preventDefault();\n                this._model.goToNextLine();\n            }\n            if (e.equals(16 /* KeyCode.UpArrow */)\n                || e.equals(2048 /* KeyMod.CtrlCmd */ | 16 /* KeyCode.UpArrow */)\n                || e.equals(512 /* KeyMod.Alt */ | 16 /* KeyCode.UpArrow */)) {\n                e.preventDefault();\n                this._model.goToPreviousLine();\n            }\n            if (e.equals(9 /* KeyCode.Escape */)\n                || e.equals(2048 /* KeyMod.CtrlCmd */ | 9 /* KeyCode.Escape */)\n                || e.equals(512 /* KeyMod.Alt */ | 9 /* KeyCode.Escape */)\n                || e.equals(1024 /* KeyMod.Shift */ | 9 /* KeyCode.Escape */)) {\n                e.preventDefault();\n                this._model.close();\n            }\n            if (e.equals(10 /* KeyCode.Space */)\n                || e.equals(3 /* KeyCode.Enter */)) {\n                e.preventDefault();\n                this._model.revealCurrentElementInEditor();\n            }\n        }));\n    }\n    _render(store) {\n        const originalOptions = this._models.getOriginalOptions();\n        const modifiedOptions = this._models.getModifiedOptions();\n        const container = document.createElement('div');\n        container.className = 'diff-review-table';\n        container.setAttribute('role', 'list');\n        container.setAttribute('aria-label', localize('ariaLabel', 'Accessible Diff Viewer. Use arrow up and down to navigate.'));\n        applyFontInfo(container, modifiedOptions.get(50 /* EditorOption.fontInfo */));\n        reset(this._content, container);\n        const originalModel = this._models.getOriginalModel();\n        const modifiedModel = this._models.getModifiedModel();\n        if (!originalModel || !modifiedModel) {\n            return;\n        }\n        const originalModelOpts = originalModel.getOptions();\n        const modifiedModelOpts = modifiedModel.getOptions();\n        const lineHeight = modifiedOptions.get(67 /* EditorOption.lineHeight */);\n        const group = this._model.currentGroup.get();\n        for (const viewItem of group?.lines || []) {\n            if (!group) {\n                break;\n            }\n            let row;\n            if (viewItem.type === LineType.Header) {\n                const header = document.createElement('div');\n                header.className = 'diff-review-row';\n                header.setAttribute('role', 'listitem');\n                const r = group.range;\n                const diffIndex = this._model.currentGroupIndex.get();\n                const diffsLength = this._model.groups.get().length;\n                const getAriaLines = (lines) => lines === 0 ? localize('no_lines_changed', \"no lines changed\")\n                    : lines === 1 ? localize('one_line_changed', \"1 line changed\")\n                        : localize('more_lines_changed', \"{0} lines changed\", lines);\n                const originalChangedLinesCntAria = getAriaLines(r.original.length);\n                const modifiedChangedLinesCntAria = getAriaLines(r.modified.length);\n                header.setAttribute('aria-label', localize({\n                    key: 'header',\n                    comment: [\n                        'This is the ARIA label for a git diff header.',\n                        'A git diff header looks like this: @@ -154,12 +159,39 @@.',\n                        'That encodes that at original line 154 (which is now line 159), 12 lines were removed/changed with 39 lines.',\n                        'Variables 0 and 1 refer to the diff index out of total number of diffs.',\n                        'Variables 2 and 4 will be numbers (a line number).',\n                        'Variables 3 and 5 will be \"no lines changed\", \"1 line changed\" or \"X lines changed\", localized separately.'\n                    ]\n                }, \"Difference {0} of {1}: original line {2}, {3}, modified line {4}, {5}\", (diffIndex + 1), diffsLength, r.original.startLineNumber, originalChangedLinesCntAria, r.modified.startLineNumber, modifiedChangedLinesCntAria));\n                const cell = document.createElement('div');\n                cell.className = 'diff-review-cell diff-review-summary';\n                // e.g.: `1/10: @@ -504,7 +517,7 @@`\n                cell.appendChild(document.createTextNode(`${diffIndex + 1}/${diffsLength}: @@ -${r.original.startLineNumber},${r.original.length} +${r.modified.startLineNumber},${r.modified.length} @@`));\n                header.appendChild(cell);\n                row = header;\n            }\n            else {\n                row = this._createRow(viewItem, lineHeight, this._width.get(), originalOptions, originalModel, originalModelOpts, modifiedOptions, modifiedModel, modifiedModelOpts);\n            }\n            container.appendChild(row);\n            const isSelectedObs = derived(reader => /** @description isSelected */ this._model.currentElement.read(reader) === viewItem);\n            store.add(autorun(reader => {\n                /** @description update tab index */\n                const isSelected = isSelectedObs.read(reader);\n                row.tabIndex = isSelected ? 0 : -1;\n                if (isSelected) {\n                    row.focus();\n                }\n            }));\n            store.add(addDisposableListener(row, 'focus', () => {\n                this._model.goToLine(viewItem);\n            }));\n        }\n        this._scrollbar.scanDomNode();\n    }\n    _createRow(item, lineHeight, width, originalOptions, originalModel, originalModelOpts, modifiedOptions, modifiedModel, modifiedModelOpts) {\n        const originalLayoutInfo = originalOptions.get(146 /* EditorOption.layoutInfo */);\n        const originalLineNumbersWidth = originalLayoutInfo.glyphMarginWidth + originalLayoutInfo.lineNumbersWidth;\n        const modifiedLayoutInfo = modifiedOptions.get(146 /* EditorOption.layoutInfo */);\n        const modifiedLineNumbersWidth = 10 + modifiedLayoutInfo.glyphMarginWidth + modifiedLayoutInfo.lineNumbersWidth;\n        let rowClassName = 'diff-review-row';\n        let lineNumbersExtraClassName = '';\n        const spacerClassName = 'diff-review-spacer';\n        let spacerIcon = null;\n        switch (item.type) {\n            case LineType.Added:\n                rowClassName = 'diff-review-row line-insert';\n                lineNumbersExtraClassName = ' char-insert';\n                spacerIcon = accessibleDiffViewerInsertIcon;\n                break;\n            case LineType.Deleted:\n                rowClassName = 'diff-review-row line-delete';\n                lineNumbersExtraClassName = ' char-delete';\n                spacerIcon = accessibleDiffViewerRemoveIcon;\n                break;\n        }\n        const row = document.createElement('div');\n        row.style.minWidth = width + 'px';\n        row.className = rowClassName;\n        row.setAttribute('role', 'listitem');\n        row.ariaLevel = '';\n        const cell = document.createElement('div');\n        cell.className = 'diff-review-cell';\n        cell.style.height = `${lineHeight}px`;\n        row.appendChild(cell);\n        const originalLineNumber = document.createElement('span');\n        originalLineNumber.style.width = (originalLineNumbersWidth + 'px');\n        originalLineNumber.style.minWidth = (originalLineNumbersWidth + 'px');\n        originalLineNumber.className = 'diff-review-line-number' + lineNumbersExtraClassName;\n        if (item.originalLineNumber !== undefined) {\n            originalLineNumber.appendChild(document.createTextNode(String(item.originalLineNumber)));\n        }\n        else {\n            originalLineNumber.innerText = '\\u00a0';\n        }\n        cell.appendChild(originalLineNumber);\n        const modifiedLineNumber = document.createElement('span');\n        modifiedLineNumber.style.width = (modifiedLineNumbersWidth + 'px');\n        modifiedLineNumber.style.minWidth = (modifiedLineNumbersWidth + 'px');\n        modifiedLineNumber.style.paddingRight = '10px';\n        modifiedLineNumber.className = 'diff-review-line-number' + lineNumbersExtraClassName;\n        if (item.modifiedLineNumber !== undefined) {\n            modifiedLineNumber.appendChild(document.createTextNode(String(item.modifiedLineNumber)));\n        }\n        else {\n            modifiedLineNumber.innerText = '\\u00a0';\n        }\n        cell.appendChild(modifiedLineNumber);\n        const spacer = document.createElement('span');\n        spacer.className = spacerClassName;\n        if (spacerIcon) {\n            const spacerCodicon = document.createElement('span');\n            spacerCodicon.className = ThemeIcon.asClassName(spacerIcon);\n            spacerCodicon.innerText = '\\u00a0\\u00a0';\n            spacer.appendChild(spacerCodicon);\n        }\n        else {\n            spacer.innerText = '\\u00a0\\u00a0';\n        }\n        cell.appendChild(spacer);\n        let lineContent;\n        if (item.modifiedLineNumber !== undefined) {\n            let html = this._getLineHtml(modifiedModel, modifiedOptions, modifiedModelOpts.tabSize, item.modifiedLineNumber, this._languageService.languageIdCodec);\n            if (AccessibleDiffViewer._ttPolicy) {\n                html = AccessibleDiffViewer._ttPolicy.createHTML(html);\n            }\n            cell.insertAdjacentHTML('beforeend', html);\n            lineContent = modifiedModel.getLineContent(item.modifiedLineNumber);\n        }\n        else {\n            let html = this._getLineHtml(originalModel, originalOptions, originalModelOpts.tabSize, item.originalLineNumber, this._languageService.languageIdCodec);\n            if (AccessibleDiffViewer._ttPolicy) {\n                html = AccessibleDiffViewer._ttPolicy.createHTML(html);\n            }\n            cell.insertAdjacentHTML('beforeend', html);\n            lineContent = originalModel.getLineContent(item.originalLineNumber);\n        }\n        if (lineContent.length === 0) {\n            lineContent = localize('blankLine', \"blank\");\n        }\n        let ariaLabel = '';\n        switch (item.type) {\n            case LineType.Unchanged:\n                if (item.originalLineNumber === item.modifiedLineNumber) {\n                    ariaLabel = localize({ key: 'unchangedLine', comment: ['The placeholders are contents of the line and should not be translated.'] }, \"{0} unchanged line {1}\", lineContent, item.originalLineNumber);\n                }\n                else {\n                    ariaLabel = localize('equalLine', \"{0} original line {1} modified line {2}\", lineContent, item.originalLineNumber, item.modifiedLineNumber);\n                }\n                break;\n            case LineType.Added:\n                ariaLabel = localize('insertLine', \"+ {0} modified line {1}\", lineContent, item.modifiedLineNumber);\n                break;\n            case LineType.Deleted:\n                ariaLabel = localize('deleteLine', \"- {0} original line {1}\", lineContent, item.originalLineNumber);\n                break;\n        }\n        row.setAttribute('aria-label', ariaLabel);\n        return row;\n    }\n    _getLineHtml(model, options, tabSize, lineNumber, languageIdCodec) {\n        const lineContent = model.getLineContent(lineNumber);\n        const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n        const lineTokens = LineTokens.createEmpty(lineContent, languageIdCodec);\n        const isBasicASCII = ViewLineRenderingData.isBasicASCII(lineContent, model.mightContainNonBasicASCII());\n        const containsRTL = ViewLineRenderingData.containsRTL(lineContent, isBasicASCII, model.mightContainRTL());\n        const r = renderViewLine2(new RenderLineInput((fontInfo.isMonospace && !options.get(33 /* EditorOption.disableMonospaceOptimizations */)), fontInfo.canUseHalfwidthRightwardsArrow, lineContent, false, isBasicASCII, containsRTL, 0, lineTokens, [], tabSize, 0, fontInfo.spaceWidth, fontInfo.middotWidth, fontInfo.wsmiddotWidth, options.get(118 /* EditorOption.stopRenderingLineAfter */), options.get(100 /* EditorOption.renderWhitespace */), options.get(95 /* EditorOption.renderControlCharacters */), options.get(51 /* EditorOption.fontLigatures */) !== EditorFontLigatures.OFF, null));\n        return r.html;\n    }\n};\nView = __decorate([\n    __param(5, ILanguageService)\n], View);\nexport class AccessibleDiffViewerModelFromEditors {\n    constructor(editors) {\n        this.editors = editors;\n    }\n    getOriginalModel() {\n        return this.editors.original.getModel();\n    }\n    getOriginalOptions() {\n        return this.editors.original.getOptions();\n    }\n    originalReveal(range) {\n        this.editors.original.revealRange(range);\n        this.editors.original.setSelection(range);\n        this.editors.original.focus();\n    }\n    getModifiedModel() {\n        return this.editors.modified.getModel();\n    }\n    getModifiedOptions() {\n        return this.editors.modified.getOptions();\n    }\n    modifiedReveal(range) {\n        if (range) {\n            this.editors.modified.revealRange(range);\n            this.editors.modified.setSelection(range);\n        }\n        this.editors.modified.focus();\n    }\n    modifiedSetSelection(range) {\n        this.editors.modified.setSelection(range);\n    }\n    modifiedFocus() {\n        this.editors.modified.focus();\n    }\n    getModifiedPosition() {\n        return this.editors.modified.getPosition() ?? undefined;\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,qBAAqB,EAAEC,6BAA6B,EAAEC,KAAK,QAAQ,oCAAoC;AAChH,SAASC,wBAAwB,QAAQ,6CAA6C;AACtF,SAASC,SAAS,QAAQ,uDAAuD;AACjF,SAASC,oBAAoB,QAAQ,+DAA+D;AACpG,SAASC,MAAM,QAAQ,uCAAuC;AAC9D,SAASC,eAAe,EAAEC,eAAe,QAAQ,sCAAsC;AACvF,SAASC,OAAO,QAAQ,wCAAwC;AAChE,SAASC,UAAU,EAAEC,YAAY,QAAQ,yCAAyC;AAClF,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,WAAW,QAAQ,0CAA0C;AAC7J,SAASC,SAAS,QAAQ,yCAAyC;AACnE,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,mBAAmB,QAAQ,4CAA4C;AAChF,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,WAAW,QAAQ,wCAAwC;AACpE,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,eAAe,EAAEC,eAAe,QAAQ,mDAAmD;AACpG,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,mBAAmB,EAAEC,2BAA2B,QAAQ,mFAAmF;AACpJ,SAASC,qBAAqB,QAAQ,+DAA+D;AACrG,SAASC,YAAY,QAAQ,sDAAsD;AACnF,OAAO,4BAA4B;AACnC,MAAMC,8BAA8B,GAAGD,YAAY,CAAC,oBAAoB,EAAE5B,OAAO,CAAC8B,GAAG,EAAEN,QAAQ,CAAC,gCAAgC,EAAE,gDAAgD,CAAC,CAAC;AACpL,MAAMO,8BAA8B,GAAGH,YAAY,CAAC,oBAAoB,EAAE5B,OAAO,CAACgC,MAAM,EAAER,QAAQ,CAAC,gCAAgC,EAAE,gDAAgD,CAAC,CAAC;AACvL,MAAMS,6BAA6B,GAAGL,YAAY,CAAC,mBAAmB,EAAE5B,OAAO,CAACkC,KAAK,EAAEV,QAAQ,CAAC,+BAA+B,EAAE,+CAA+C,CAAC,CAAC;AAClL,IAAIW,oBAAoB,GAAG,MAAMA,oBAAoB,SAASlC,UAAU,CAAC;EACrE;IAAS,IAAI,CAACmC,SAAS,GAAG1C,wBAAwB,CAAC,YAAY,EAAE;MAAE2C,UAAU,EAAEC,KAAK,IAAIA;IAAM,CAAC,CAAC;EAAE;EAClGC,WAAWA,CAACC,WAAW,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,qBAAqB,EAAE;IAChH,KAAK,CAAC,CAAC;IACP,IAAI,CAACR,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,MAAM,GAAG3C,gBAAgB,CAAC,IAAI,EAAE,CAAC4C,MAAM,EAAEC,KAAK,KAAK;MACpD,MAAMC,OAAO,GAAG,IAAI,CAACX,QAAQ,CAACY,IAAI,CAACH,MAAM,CAAC;MAC1C,IAAI,CAACV,WAAW,CAACc,KAAK,CAACC,UAAU,GAAGH,OAAO,GAAG,SAAS,GAAG,QAAQ;MAClE,IAAI,CAACA,OAAO,EAAE;QACV,OAAO,IAAI;MACf;MACA,MAAMI,KAAK,GAAGL,KAAK,CAACrB,GAAG,CAAC,IAAI,CAACkB,qBAAqB,CAACS,cAAc,CAACC,SAAS,EAAE,IAAI,CAACZ,MAAM,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACL,WAAW,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;MAC1I,MAAMgB,IAAI,GAAGR,KAAK,CAACrB,GAAG,CAAC,IAAI,CAACkB,qBAAqB,CAACS,cAAc,CAACG,IAAI,EAAE,IAAI,CAACpB,WAAW,EAAEgB,KAAK,EAAE,IAAI,CAACZ,MAAM,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACE,OAAO,CAAC,CAAC;MACzI,OAAO;QAAES,KAAK;QAAEG;MAAM,CAAC;IAC3B,CAAC,CAAC,CAACE,6BAA6B,CAAC,IAAI,CAACC,MAAM,CAAC;EACjD;EACAC,IAAIA,CAAA,EAAG;IACHtD,WAAW,CAACuD,EAAE,IAAI;MACd,MAAMC,SAAS,GAAG,IAAI,CAACxB,QAAQ,CAACyB,GAAG,CAAC,CAAC;MACrC,IAAI,CAACxB,WAAW,CAAC,IAAI,EAAEsB,EAAE,CAAC;MAC1B,IAAIC,SAAS,EAAE;QACX,IAAI,CAAChB,MAAM,CAACiB,GAAG,CAAC,CAAC,CAACV,KAAK,CAACW,SAAS,CAACH,EAAE,CAAC;MACzC;IACJ,CAAC,CAAC;EACN;EACAI,IAAIA,CAAA,EAAG;IACH3D,WAAW,CAACuD,EAAE,IAAI;MACd,IAAI,CAACtB,WAAW,CAAC,IAAI,EAAEsB,EAAE,CAAC;MAC1B,IAAI,CAACf,MAAM,CAACiB,GAAG,CAAC,CAAC,CAACV,KAAK,CAACa,aAAa,CAACL,EAAE,CAAC;IAC7C,CAAC,CAAC;EACN;EACA9B,KAAKA,CAAA,EAAG;IACJzB,WAAW,CAACuD,EAAE,IAAI;MACd,IAAI,CAACtB,WAAW,CAAC,KAAK,EAAEsB,EAAE,CAAC;IAC/B,CAAC,CAAC;EACN;AACJ,CAAC;AACD7B,oBAAoB,GAAG/D,UAAU,CAAC,CAC9BgB,OAAO,CAAC,CAAC,EAAEuC,qBAAqB,CAAC,CACpC,EAAEQ,oBAAoB,CAAC;AACxB,SAASA,oBAAoB;AAC7B,IAAIuB,SAAS,GAAG,MAAMA,SAAS,SAASzD,UAAU,CAAC;EAC/CsC,WAAWA,CAACO,MAAM,EAAEC,OAAO,EAAEL,WAAW,EAAE4B,QAAQ,EAAEC,2BAA2B,EAAE;IAC7E,KAAK,CAAC,CAAC;IACP,IAAI,CAACzB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACL,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC4B,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;IAC9D,IAAI,CAACC,OAAO,GAAGjE,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC;IACxC,IAAI,CAACkE,gBAAgB,GAAGlE,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;IAChD,IAAI,CAACmE,kBAAkB,GAAGnE,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;IAClD,IAAI,CAACoE,MAAM,GAAG,IAAI,CAACH,OAAO;IAC1B,IAAI,CAACI,YAAY,GAAG,IAAI,CAACH,gBAAgB,CAACI,GAAG,CAAC,CAACC,GAAG,EAAElG,CAAC,KAAK,IAAI,CAAC4F,OAAO,CAACnB,IAAI,CAACzE,CAAC,CAAC,CAACkG,GAAG,CAAC,CAAC;IACpF,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACN,gBAAgB;IAC9C,IAAI,CAACO,cAAc,GAAG,IAAI,CAACN,kBAAkB,CAACG,GAAG,CAAC,CAACC,GAAG,EAAElG,CAAC,KAAK,IAAI,CAACgG,YAAY,CAACvB,IAAI,CAACzE,CAAC,CAAC,EAAEqG,KAAK,CAACH,GAAG,CAAC,CAAC;IACpG,IAAI,CAACI,SAAS,CAAC/E,OAAO,CAAC+C,MAAM,IAAI;MAC7B;MACA,MAAMiC,KAAK,GAAG,IAAI,CAACrC,MAAM,CAACO,IAAI,CAACH,MAAM,CAAC;MACtC,IAAI,CAACiC,KAAK,EAAE;QACR,IAAI,CAACX,OAAO,CAACY,GAAG,CAAC,EAAE,EAAEC,SAAS,CAAC;QAC/B;MACJ;MACA,MAAMV,MAAM,GAAGW,wBAAwB,CAACH,KAAK,EAAE,IAAI,CAACpC,OAAO,CAACwC,gBAAgB,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,EAAE,IAAI,CAACzC,OAAO,CAAC0C,gBAAgB,CAAC,CAAC,CAACD,YAAY,CAAC,CAAC,CAAC;MAC9I/E,WAAW,CAACuD,EAAE,IAAI;QACd,MAAM0B,CAAC,GAAG,IAAI,CAAC3C,OAAO,CAAC4C,mBAAmB,CAAC,CAAC;QAC5C,IAAID,CAAC,EAAE;UACH,MAAMvB,SAAS,GAAGQ,MAAM,CAACiB,SAAS,CAACC,CAAC,IAAIH,CAAC,EAAEI,UAAU,GAAGD,CAAC,CAACE,KAAK,CAACC,QAAQ,CAACC,sBAAsB,CAAC;UAChG,IAAI9B,SAAS,KAAK,CAAC,CAAC,EAAE;YAClB,IAAI,CAACM,gBAAgB,CAACW,GAAG,CAACjB,SAAS,EAAEH,EAAE,CAAC;UAC5C;QACJ;QACA,IAAI,CAACQ,OAAO,CAACY,GAAG,CAACT,MAAM,EAAEX,EAAE,CAAC;MAChC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;IACH,IAAI,CAACkB,SAAS,CAAC/E,OAAO,CAAC+C,MAAM,IAAI;MAC7B;MACA,MAAMgD,eAAe,GAAG,IAAI,CAAClB,cAAc,CAAC3B,IAAI,CAACH,MAAM,CAAC;MACxD,IAAIgD,eAAe,EAAEC,IAAI,KAAKC,QAAQ,CAACC,OAAO,EAAE;QAC5C,IAAI,CAAC9B,2BAA2B,CAAC+B,UAAU,CAAC7E,mBAAmB,CAAC8E,eAAe,EAAE;UAAEC,MAAM,EAAE;QAA6C,CAAC,CAAC;MAC9I,CAAC,MACI,IAAIN,eAAe,EAAEC,IAAI,KAAKC,QAAQ,CAACK,KAAK,EAAE;QAC/C,IAAI,CAAClC,2BAA2B,CAAC+B,UAAU,CAAC7E,mBAAmB,CAACiF,gBAAgB,EAAE;UAAEF,MAAM,EAAE;QAA6C,CAAC,CAAC;MAC/I;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACtB,SAAS,CAAC/E,OAAO,CAAC+C,MAAM,IAAI;MAC7B;MACA;MACA,MAAMgD,eAAe,GAAG,IAAI,CAAClB,cAAc,CAAC3B,IAAI,CAACH,MAAM,CAAC;MACxD,IAAIgD,eAAe,IAAIA,eAAe,CAACC,IAAI,KAAKC,QAAQ,CAACO,MAAM,EAAE;QAC7D,MAAMb,UAAU,GAAGI,eAAe,CAACU,kBAAkB,IAAIV,eAAe,CAACW,IAAI,CAACb,QAAQ,CAACc,eAAe;QACtG,IAAI,CAAC/D,OAAO,CAACgE,oBAAoB,CAAC9F,KAAK,CAAC+F,aAAa,CAAC,IAAIhG,QAAQ,CAAC8E,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;MACvF;IACJ,CAAC,CAAC,CAAC;EACP;EACAmB,eAAeA,CAACC,KAAK,EAAElD,EAAE,EAAE;IACvB,MAAMW,MAAM,GAAG,IAAI,CAACA,MAAM,CAACT,GAAG,CAAC,CAAC;IAChC,IAAI,CAACS,MAAM,IAAIA,MAAM,CAAChG,MAAM,IAAI,CAAC,EAAE;MAC/B;IACJ;IACA6B,cAAc,CAACwD,EAAE,EAAEA,EAAE,IAAI;MACrB,IAAI,CAACS,gBAAgB,CAACW,GAAG,CAACrE,WAAW,CAACoG,QAAQ,CAACxC,MAAM,CAAChG,MAAM,CAAC,CAACyI,UAAU,CAAC,IAAI,CAAC3C,gBAAgB,CAACP,GAAG,CAAC,CAAC,GAAGgD,KAAK,CAAC,EAAElD,EAAE,CAAC;MAClH,IAAI,CAACU,kBAAkB,CAACU,GAAG,CAAC,CAAC,EAAEpB,EAAE,CAAC;IACtC,CAAC,CAAC;EACN;EACAG,SAASA,CAACH,EAAE,EAAE;IAAE,IAAI,CAACiD,eAAe,CAAC,CAAC,EAAEjD,EAAE,CAAC;EAAE;EAC7CK,aAAaA,CAACL,EAAE,EAAE;IAAE,IAAI,CAACiD,eAAe,CAAC,CAAC,CAAC,EAAEjD,EAAE,CAAC;EAAE;EAClDqD,cAAcA,CAACH,KAAK,EAAE;IAClB,MAAMI,KAAK,GAAG,IAAI,CAAC1C,YAAY,CAACV,GAAG,CAAC,CAAC;IACrC,IAAI,CAACoD,KAAK,IAAIA,KAAK,CAACrC,KAAK,CAACtG,MAAM,IAAI,CAAC,EAAE;MACnC;IACJ;IACA8B,WAAW,CAACuD,EAAE,IAAI;MACd,IAAI,CAACU,kBAAkB,CAACU,GAAG,CAACrE,WAAW,CAACoG,QAAQ,CAACG,KAAK,CAACrC,KAAK,CAACtG,MAAM,CAAC,CAAC4I,IAAI,CAAC,IAAI,CAAC7C,kBAAkB,CAACR,GAAG,CAAC,CAAC,GAAGgD,KAAK,CAAC,EAAElD,EAAE,CAAC;IACzH,CAAC,CAAC;EACN;EACAwD,YAAYA,CAAA,EAAG;IAAE,IAAI,CAACH,cAAc,CAAC,CAAC,CAAC;EAAE;EACzCI,gBAAgBA,CAAA,EAAG;IAAE,IAAI,CAACJ,cAAc,CAAC,CAAC,CAAC,CAAC;EAAE;EAC9CK,QAAQA,CAACC,IAAI,EAAE;IACX,MAAML,KAAK,GAAG,IAAI,CAAC1C,YAAY,CAACV,GAAG,CAAC,CAAC;IACrC,IAAI,CAACoD,KAAK,EAAE;MACR;IACJ;IACA,MAAMxC,GAAG,GAAGwC,KAAK,CAACrC,KAAK,CAAC2C,OAAO,CAACD,IAAI,CAAC;IACrC,IAAI7C,GAAG,KAAK,CAAC,CAAC,EAAE;MACZ;IACJ;IACArE,WAAW,CAACuD,EAAE,IAAI;MACd,IAAI,CAACU,kBAAkB,CAACU,GAAG,CAACN,GAAG,EAAEd,EAAE,CAAC;IACxC,CAAC,CAAC;EACN;EACA6D,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,CAAC,IAAI,CAACvD,QAAQ,CAACJ,GAAG,CAAC,CAAC,EAAE;MACtB;IACJ;IACA,IAAI,CAACxB,WAAW,CAAC,KAAK,EAAE2C,SAAS,CAAC;IAClC,MAAMyC,OAAO,GAAG,IAAI,CAAC9C,cAAc,CAACd,GAAG,CAAC,CAAC;IACzC,IAAI4D,OAAO,EAAE;MACT,IAAIA,OAAO,CAAC3B,IAAI,KAAKC,QAAQ,CAACC,OAAO,EAAE;QACnC,IAAI,CAACtD,OAAO,CAACgF,cAAc,CAAC9G,KAAK,CAAC+F,aAAa,CAAC,IAAIhG,QAAQ,CAAC8G,OAAO,CAACE,kBAAkB,EAAE,CAAC,CAAC,CAAC,CAAC;MACjG,CAAC,MACI;QACD,IAAI,CAACjF,OAAO,CAACkF,cAAc,CAACH,OAAO,CAAC3B,IAAI,KAAKC,QAAQ,CAACO,MAAM,GACtD1F,KAAK,CAAC+F,aAAa,CAAC,IAAIhG,QAAQ,CAAC8G,OAAO,CAAClB,kBAAkB,EAAE,CAAC,CAAC,CAAC,GAChEvB,SAAS,CAAC;MACpB;IACJ;EACJ;EACAnD,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACoC,QAAQ,CAACJ,GAAG,CAAC,CAAC,EAAE;MACtB;IACJ;IACA,IAAI,CAACxB,WAAW,CAAC,KAAK,EAAE2C,SAAS,CAAC;IAClC,IAAI,CAACtC,OAAO,CAACmF,aAAa,CAAC,CAAC;EAChC;AACJ,CAAC;AACDxE,SAAS,GAAGtF,UAAU,CAAC,CACnBgB,OAAO,CAAC,CAAC,EAAEsC,2BAA2B,CAAC,CAC1C,EAAEgC,SAAS,CAAC;AACb,MAAMyE,0BAA0B,GAAG,CAAC;AACpC,SAAS7C,wBAAwBA,CAACH,KAAK,EAAEiD,iBAAiB,EAAEC,iBAAiB,EAAE;EAC3E,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,MAAMzC,CAAC,IAAI9F,eAAe,CAACoF,KAAK,EAAE,CAACoD,CAAC,EAAEC,CAAC,KAAMA,CAAC,CAACxC,QAAQ,CAACc,eAAe,GAAGyB,CAAC,CAACvC,QAAQ,CAACC,sBAAsB,GAAG,CAAC,GAAGkC,0BAA2B,CAAC,EAAE;IACjJ,MAAMM,YAAY,GAAG,EAAE;IACvBA,YAAY,CAACC,IAAI,CAAC,IAAIC,iBAAiB,CAAC,CAAC,CAAC;IAC1C,MAAMC,aAAa,GAAG,IAAI9H,SAAS,CAAC+H,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,CAAC,CAAC,CAAC,CAAC,CAACkD,QAAQ,CAACjC,eAAe,GAAGqB,0BAA0B,CAAC,EAAEU,IAAI,CAACG,GAAG,CAACnD,CAAC,CAACA,CAAC,CAAClH,MAAM,GAAG,CAAC,CAAC,CAACoK,QAAQ,CAAC9C,sBAAsB,GAAGkC,0BAA0B,EAAEC,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC3N,MAAMa,iBAAiB,GAAG,IAAInI,SAAS,CAAC+H,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,CAAC,CAAC,CAAC,CAAC,CAACG,QAAQ,CAACc,eAAe,GAAGqB,0BAA0B,CAAC,EAAEU,IAAI,CAACG,GAAG,CAACnD,CAAC,CAACA,CAAC,CAAClH,MAAM,GAAG,CAAC,CAAC,CAACqH,QAAQ,CAACC,sBAAsB,GAAGkC,0BAA0B,EAAEE,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC/NvI,eAAe,CAAC+F,CAAC,EAAE,CAAC0C,CAAC,EAAEC,CAAC,KAAK;MACzB,MAAMU,SAAS,GAAG,IAAIpI,SAAS,CAACyH,CAAC,GAAGA,CAAC,CAACQ,QAAQ,CAAC9C,sBAAsB,GAAG2C,aAAa,CAAC9B,eAAe,EAAE0B,CAAC,GAAGA,CAAC,CAACO,QAAQ,CAACjC,eAAe,GAAG8B,aAAa,CAAC3C,sBAAsB,CAAC;MAC7K,MAAMkD,aAAa,GAAG,IAAIrI,SAAS,CAACyH,CAAC,GAAGA,CAAC,CAACvC,QAAQ,CAACC,sBAAsB,GAAGgD,iBAAiB,CAACnC,eAAe,EAAE0B,CAAC,GAAGA,CAAC,CAACxC,QAAQ,CAACc,eAAe,GAAGmC,iBAAiB,CAAChD,sBAAsB,CAAC;MACzLiD,SAAS,CAACE,OAAO,CAACC,cAAc,IAAI;QAChCZ,YAAY,CAACC,IAAI,CAAC,IAAIY,wBAAwB,CAACD,cAAc,EAAEF,aAAa,CAACrC,eAAe,IAAIuC,cAAc,GAAGH,SAAS,CAACpC,eAAe,CAAC,CAAC,CAAC;MACjJ,CAAC,CAAC;MACF,IAAI0B,CAAC,EAAE;QACHA,CAAC,CAACO,QAAQ,CAACK,OAAO,CAACC,cAAc,IAAI;UACjCZ,YAAY,CAACC,IAAI,CAAC,IAAIa,sBAAsB,CAACf,CAAC,EAAEa,cAAc,CAAC,CAAC;QACpE,CAAC,CAAC;QACFb,CAAC,CAACxC,QAAQ,CAACoD,OAAO,CAACxC,kBAAkB,IAAI;UACrC6B,YAAY,CAACC,IAAI,CAAC,IAAIc,oBAAoB,CAAChB,CAAC,EAAE5B,kBAAkB,CAAC,CAAC;QACtE,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,MAAMuC,aAAa,GAAGtD,CAAC,CAAC,CAAC,CAAC,CAACG,QAAQ,CAACyD,IAAI,CAAC5D,CAAC,CAACA,CAAC,CAAClH,MAAM,GAAG,CAAC,CAAC,CAACqH,QAAQ,CAAC;IAClE,MAAM0D,aAAa,GAAG7D,CAAC,CAAC,CAAC,CAAC,CAACkD,QAAQ,CAACU,IAAI,CAAC5D,CAAC,CAACA,CAAC,CAAClH,MAAM,GAAG,CAAC,CAAC,CAACoK,QAAQ,CAAC;IAClET,MAAM,CAACI,IAAI,CAAC,IAAIiB,gBAAgB,CAAC,IAAIzI,gBAAgB,CAACiI,aAAa,EAAEO,aAAa,CAAC,EAAEjB,YAAY,CAAC,CAAC;EACvG;EACA,OAAOH,MAAM;AACjB;AACA,IAAIlC,QAAQ,gBACX,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAACA,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC3CA,QAAQ,CAACA,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACjDA,QAAQ,CAACA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC7CA,QAAQ,CAACA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAAC,OAJnCA,QAAQ;AAKnB,CAAC,CAAEA,QAAQ,IAAgB,CAAC,CAAE,CANlB;AAOZ,MAAMuD,gBAAgB,CAAC;EACnBpH,WAAWA,CAACwD,KAAK,EAAEd,KAAK,EAAE;IACtB,IAAI,CAACc,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACd,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA,MAAM0D,iBAAiB,CAAC;EACpBpG,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4D,IAAI,GAAGC,QAAQ,CAACO,MAAM;EAC/B;AACJ;AACA,MAAM4C,sBAAsB,CAAC;EACzBhH,WAAWA,CAACsE,IAAI,EAAEmB,kBAAkB,EAAE;IAClC,IAAI,CAACnB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACmB,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC7B,IAAI,GAAGC,QAAQ,CAACC,OAAO;IAC5B,IAAI,CAACO,kBAAkB,GAAGvB,SAAS;EACvC;AACJ;AACA,MAAMmE,oBAAoB,CAAC;EACvBjH,WAAWA,CAACsE,IAAI,EAAED,kBAAkB,EAAE;IAClC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACT,IAAI,GAAGC,QAAQ,CAACK,KAAK;IAC1B,IAAI,CAACuB,kBAAkB,GAAG3C,SAAS;EACvC;AACJ;AACA,MAAMiE,wBAAwB,CAAC;EAC3B/G,WAAWA,CAACyF,kBAAkB,EAAEpB,kBAAkB,EAAE;IAChD,IAAI,CAACoB,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACpB,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACT,IAAI,GAAGC,QAAQ,CAACwD,SAAS;EAClC;AACJ;AACA,IAAIhG,IAAI,GAAG,MAAMA,IAAI,SAAS3D,UAAU,CAAC;EACrCsC,WAAWA,CAACsH,QAAQ,EAAEC,MAAM,EAAElH,MAAM,EAAEC,OAAO,EAAEE,OAAO,EAAEgH,gBAAgB,EAAE;IACtE,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAClH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACgH,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACH,QAAQ;IAC5B,IAAI,CAACG,OAAO,CAACC,SAAS,GAAG,uDAAuD;IAChF,MAAMC,kBAAkB,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACxDF,kBAAkB,CAACD,SAAS,GAAG,qBAAqB;IACpD,IAAI,CAACI,UAAU,GAAG,IAAI,CAACnF,SAAS,CAAC,IAAIvF,SAAS,CAACuK,kBAAkB,CAAC,CAAC;IACnE,IAAI,CAAChF,SAAS,CAAC/E,OAAO,CAAC+C,MAAM,IAAI;MAC7B;MACA,IAAI,CAACmH,UAAU,CAACC,KAAK,CAAC,CAAC;MACvB,IAAI,IAAI,CAACR,MAAM,CAACxF,QAAQ,CAACjB,IAAI,CAACH,MAAM,CAAC,EAAE;QACnC,IAAI,CAACmH,UAAU,CAAC3B,IAAI,CAAC,IAAI7I,MAAM,CAAC,kBAAkB,EAAE2B,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,EAAE,oBAAoB,GAAGd,SAAS,CAAC6J,WAAW,CAACtI,6BAA6B,CAAC,EAAE,IAAI,eAAAuI,iBAAA,CAAE;UAAA,OAAYV,MAAM,CAAC5H,KAAK,CAAC,CAAC;QAAA,GAAC,EAAE;UAAEuI,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MACvO;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACC,QAAQ,GAAGR,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7C,IAAI,CAACO,QAAQ,CAACV,SAAS,GAAG,qBAAqB;IAC/C,IAAI,CAACU,QAAQ,CAACC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;IAC1C,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC3F,SAAS,CAAC,IAAItF,oBAAoB,CAAC,IAAI,CAAC+K,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7ElL,KAAK,CAAC,IAAI,CAACuK,OAAO,EAAE,IAAI,CAACa,UAAU,CAACC,UAAU,CAAC,CAAC,EAAEZ,kBAAkB,CAAC;IACrE,IAAI,CAAChF,SAAS,CAAC/E,OAAO,CAACvB,CAAC,IAAI;MACxB,IAAI,CAACiE,OAAO,CAACQ,IAAI,CAACzE,CAAC,CAAC;MACpB,IAAI,CAACgE,MAAM,CAACS,IAAI,CAACzE,CAAC,CAAC;MACnB,IAAI,CAACiM,UAAU,CAACE,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IACH,IAAI,CAAC7F,SAAS,CAAChF,YAAY,CAAC,MAAM;MAAET,KAAK,CAAC,IAAI,CAACuK,OAAO,CAAC;IAAE,CAAC,CAAC,CAAC;IAC5D,IAAI,CAAC9E,SAAS,CAACtE,UAAU,CAAC,IAAI,CAACoJ,OAAO,EAAE;MAAEgB,KAAK,EAAE,IAAI,CAACpI,MAAM;MAAEqI,MAAM,EAAE,IAAI,CAACpI;IAAQ,CAAC,CAAC,CAAC;IACtF,IAAI,CAACqC,SAAS,CAACtE,UAAU,CAAC,IAAI,CAAC+J,QAAQ,EAAE;MAAEK,KAAK,EAAE,IAAI,CAACpI,MAAM;MAAEqI,MAAM,EAAE,IAAI,CAACpI;IAAQ,CAAC,CAAC,CAAC;IACvF,IAAI,CAACqC,SAAS,CAAC9E,gBAAgB,CAAC,CAAC8C,MAAM,EAAEC,KAAK,KAAK;MAC/C;MACA,IAAI,CAAC2G,MAAM,CAAClF,YAAY,CAACvB,IAAI,CAACH,MAAM,CAAC;MACrC,IAAI,CAACgI,OAAO,CAAC/H,KAAK,CAAC;IACvB,CAAC,CAAC,CAAC;IACH;IACA,IAAI,CAAC+B,SAAS,CAAC1F,6BAA6B,CAAC,IAAI,CAACwK,OAAO,EAAE,SAAS,EAAGmB,CAAC,IAAK;MACzE,IAAIA,CAAC,CAACC,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,IACjCD,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,uBAAuB,CAAC,IAChED,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC,uBAAuB,CAAC,EAAE;QAChED,CAAC,CAACE,cAAc,CAAC,CAAC;QAClB,IAAI,CAACvB,MAAM,CAACtC,YAAY,CAAC,CAAC;MAC9B;MACA,IAAI2D,CAAC,CAACC,MAAM,CAAC,EAAE,CAAC,qBAAqB,CAAC,IAC/BD,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,qBAAqB,CAAC,IAC9DD,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC,qBAAqB,CAAC,EAAE;QAC9DD,CAAC,CAACE,cAAc,CAAC,CAAC;QAClB,IAAI,CAACvB,MAAM,CAACrC,gBAAgB,CAAC,CAAC;MAClC;MACA,IAAI0D,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAC7BD,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,oBAAoB,CAAC,IAC5DD,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,CAAC,IACvDD,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,oBAAoB,CAAC,EAAE;QAC/DD,CAAC,CAACE,cAAc,CAAC,CAAC;QAClB,IAAI,CAACvB,MAAM,CAAC5H,KAAK,CAAC,CAAC;MACvB;MACA,IAAIiJ,CAAC,CAACC,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC,IAC7BD,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE;QACpCD,CAAC,CAACE,cAAc,CAAC,CAAC;QAClB,IAAI,CAACvB,MAAM,CAACjC,4BAA4B,CAAC,CAAC;MAC9C;IACJ,CAAC,CAAC,CAAC;EACP;EACAqD,OAAOA,CAAC/H,KAAK,EAAE;IACX,MAAMmI,eAAe,GAAG,IAAI,CAACvI,OAAO,CAACwI,kBAAkB,CAAC,CAAC;IACzD,MAAMC,eAAe,GAAG,IAAI,CAACzI,OAAO,CAAC0I,kBAAkB,CAAC,CAAC;IACzD,MAAMC,SAAS,GAAGvB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC/CsB,SAAS,CAACzB,SAAS,GAAG,mBAAmB;IACzCyB,SAAS,CAACd,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;IACtCc,SAAS,CAACd,YAAY,CAAC,YAAY,EAAEpJ,QAAQ,CAAC,WAAW,EAAE,4DAA4D,CAAC,CAAC;IACzHb,aAAa,CAAC+K,SAAS,EAAEF,eAAe,CAACtH,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC,CAAC;IAC7EzE,KAAK,CAAC,IAAI,CAACkL,QAAQ,EAAEe,SAAS,CAAC;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAAC5I,OAAO,CAACwC,gBAAgB,CAAC,CAAC;IACrD,MAAMqG,aAAa,GAAG,IAAI,CAAC7I,OAAO,CAAC0C,gBAAgB,CAAC,CAAC;IACrD,IAAI,CAACkG,aAAa,IAAI,CAACC,aAAa,EAAE;MAClC;IACJ;IACA,MAAMC,iBAAiB,GAAGF,aAAa,CAACG,UAAU,CAAC,CAAC;IACpD,MAAMC,iBAAiB,GAAGH,aAAa,CAACE,UAAU,CAAC,CAAC;IACpD,MAAME,UAAU,GAAGR,eAAe,CAACtH,GAAG,CAAC,EAAE,CAAC,6BAA6B,CAAC;IACxE,MAAMoD,KAAK,GAAG,IAAI,CAACwC,MAAM,CAAClF,YAAY,CAACV,GAAG,CAAC,CAAC;IAC5C,KAAK,MAAM+H,QAAQ,IAAI3E,KAAK,EAAErC,KAAK,IAAI,EAAE,EAAE;MACvC,IAAI,CAACqC,KAAK,EAAE;QACR;MACJ;MACA,IAAI4E,GAAG;MACP,IAAID,QAAQ,CAAC9F,IAAI,KAAKC,QAAQ,CAACO,MAAM,EAAE;QACnC,MAAMwF,MAAM,GAAGhC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC5C+B,MAAM,CAAClC,SAAS,GAAG,iBAAiB;QACpCkC,MAAM,CAACvB,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;QACvC,MAAMhM,CAAC,GAAG0I,KAAK,CAACvB,KAAK;QACrB,MAAMqG,SAAS,GAAG,IAAI,CAACtC,MAAM,CAAC/E,iBAAiB,CAACb,GAAG,CAAC,CAAC;QACrD,MAAMmI,WAAW,GAAG,IAAI,CAACvC,MAAM,CAACnF,MAAM,CAACT,GAAG,CAAC,CAAC,CAACvF,MAAM;QACnD,MAAM2N,YAAY,GAAIrH,KAAK,IAAKA,KAAK,KAAK,CAAC,GAAGzD,QAAQ,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,GACxFyD,KAAK,KAAK,CAAC,GAAGzD,QAAQ,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,GACxDA,QAAQ,CAAC,oBAAoB,EAAE,mBAAmB,EAAEyD,KAAK,CAAC;QACpE,MAAMsH,2BAA2B,GAAGD,YAAY,CAAC1N,CAAC,CAACmK,QAAQ,CAACpK,MAAM,CAAC;QACnE,MAAM6N,2BAA2B,GAAGF,YAAY,CAAC1N,CAAC,CAACoH,QAAQ,CAACrH,MAAM,CAAC;QACnEwN,MAAM,CAACvB,YAAY,CAAC,YAAY,EAAEpJ,QAAQ,CAAC;UACvCjD,GAAG,EAAE,QAAQ;UACbkO,OAAO,EAAE,CACL,+CAA+C,EAC/C,2DAA2D,EAC3D,8GAA8G,EAC9G,yEAAyE,EACzE,oDAAoD,EACpD,4GAA4G;QAEpH,CAAC,EAAE,uEAAuE,EAAGL,SAAS,GAAG,CAAC,EAAGC,WAAW,EAAEzN,CAAC,CAACmK,QAAQ,CAACjC,eAAe,EAAEyF,2BAA2B,EAAE3N,CAAC,CAACoH,QAAQ,CAACc,eAAe,EAAE0F,2BAA2B,CAAC,CAAC;QAC5N,MAAME,IAAI,GAAGvC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC1CsC,IAAI,CAACzC,SAAS,GAAG,sCAAsC;QACvD;QACAyC,IAAI,CAACC,WAAW,CAACxC,QAAQ,CAACyC,cAAc,CAAC,GAAGR,SAAS,GAAG,CAAC,IAAIC,WAAW,SAASzN,CAAC,CAACmK,QAAQ,CAACjC,eAAe,IAAIlI,CAAC,CAACmK,QAAQ,CAACpK,MAAM,KAAKC,CAAC,CAACoH,QAAQ,CAACc,eAAe,IAAIlI,CAAC,CAACoH,QAAQ,CAACrH,MAAM,KAAK,CAAC,CAAC;QAC3LwN,MAAM,CAACQ,WAAW,CAACD,IAAI,CAAC;QACxBR,GAAG,GAAGC,MAAM;MAChB,CAAC,MACI;QACDD,GAAG,GAAG,IAAI,CAACW,UAAU,CAACZ,QAAQ,EAAED,UAAU,EAAE,IAAI,CAACpJ,MAAM,CAACsB,GAAG,CAAC,CAAC,EAAEoH,eAAe,EAAEK,aAAa,EAAEE,iBAAiB,EAAEL,eAAe,EAAEI,aAAa,EAAEG,iBAAiB,CAAC;MACxK;MACAL,SAAS,CAACiB,WAAW,CAACT,GAAG,CAAC;MAC1B,MAAMY,aAAa,GAAGzM,OAAO,CAAC6C,MAAM,IAAI,8BAA+B,IAAI,CAAC4G,MAAM,CAAC9E,cAAc,CAAC3B,IAAI,CAACH,MAAM,CAAC,KAAK+I,QAAQ,CAAC;MAC5H9I,KAAK,CAACrB,GAAG,CAAC3B,OAAO,CAAC+C,MAAM,IAAI;QACxB;QACA,MAAM6J,UAAU,GAAGD,aAAa,CAACzJ,IAAI,CAACH,MAAM,CAAC;QAC7CgJ,GAAG,CAACc,QAAQ,GAAGD,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;QAClC,IAAIA,UAAU,EAAE;UACZb,GAAG,CAACe,KAAK,CAAC,CAAC;QACf;MACJ,CAAC,CAAC,CAAC;MACH9J,KAAK,CAACrB,GAAG,CAACvC,qBAAqB,CAAC2M,GAAG,EAAE,OAAO,EAAE,MAAM;QAChD,IAAI,CAACpC,MAAM,CAACpC,QAAQ,CAACuE,QAAQ,CAAC;MAClC,CAAC,CAAC,CAAC;IACP;IACA,IAAI,CAACpB,UAAU,CAACE,WAAW,CAAC,CAAC;EACjC;EACA8B,UAAUA,CAACK,IAAI,EAAElB,UAAU,EAAEhB,KAAK,EAAEM,eAAe,EAAEK,aAAa,EAAEE,iBAAiB,EAAEL,eAAe,EAAEI,aAAa,EAAEG,iBAAiB,EAAE;IACtI,MAAMoB,kBAAkB,GAAG7B,eAAe,CAACpH,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC;IACjF,MAAMkJ,wBAAwB,GAAGD,kBAAkB,CAACE,gBAAgB,GAAGF,kBAAkB,CAACG,gBAAgB;IAC1G,MAAMC,kBAAkB,GAAG/B,eAAe,CAACtH,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC;IACjF,MAAMsJ,wBAAwB,GAAG,EAAE,GAAGD,kBAAkB,CAACF,gBAAgB,GAAGE,kBAAkB,CAACD,gBAAgB;IAC/G,IAAIG,YAAY,GAAG,iBAAiB;IACpC,IAAIC,yBAAyB,GAAG,EAAE;IAClC,MAAMC,eAAe,GAAG,oBAAoB;IAC5C,IAAIC,UAAU,GAAG,IAAI;IACrB,QAAQV,IAAI,CAAC/G,IAAI;MACb,KAAKC,QAAQ,CAACK,KAAK;QACfgH,YAAY,GAAG,6BAA6B;QAC5CC,yBAAyB,GAAG,cAAc;QAC1CE,UAAU,GAAG/L,8BAA8B;QAC3C;MACJ,KAAKuE,QAAQ,CAACC,OAAO;QACjBoH,YAAY,GAAG,6BAA6B;QAC5CC,yBAAyB,GAAG,cAAc;QAC1CE,UAAU,GAAG7L,8BAA8B;QAC3C;IACR;IACA,MAAMmK,GAAG,GAAG/B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzC8B,GAAG,CAAC5I,KAAK,CAACuK,QAAQ,GAAG7C,KAAK,GAAG,IAAI;IACjCkB,GAAG,CAACjC,SAAS,GAAGwD,YAAY;IAC5BvB,GAAG,CAACtB,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;IACpCsB,GAAG,CAAC4B,SAAS,GAAG,EAAE;IAClB,MAAMpB,IAAI,GAAGvC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAACzC,SAAS,GAAG,kBAAkB;IACnCyC,IAAI,CAACpJ,KAAK,CAAC2H,MAAM,GAAG,GAAGe,UAAU,IAAI;IACrCE,GAAG,CAACS,WAAW,CAACD,IAAI,CAAC;IACrB,MAAM1E,kBAAkB,GAAGmC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IACzDpC,kBAAkB,CAAC1E,KAAK,CAAC0H,KAAK,GAAIoC,wBAAwB,GAAG,IAAK;IAClEpF,kBAAkB,CAAC1E,KAAK,CAACuK,QAAQ,GAAIT,wBAAwB,GAAG,IAAK;IACrEpF,kBAAkB,CAACiC,SAAS,GAAG,yBAAyB,GAAGyD,yBAAyB;IACpF,IAAIR,IAAI,CAAClF,kBAAkB,KAAK3C,SAAS,EAAE;MACvC2C,kBAAkB,CAAC2E,WAAW,CAACxC,QAAQ,CAACyC,cAAc,CAACmB,MAAM,CAACb,IAAI,CAAClF,kBAAkB,CAAC,CAAC,CAAC;IAC5F,CAAC,MACI;MACDA,kBAAkB,CAACgG,SAAS,GAAG,QAAQ;IAC3C;IACAtB,IAAI,CAACC,WAAW,CAAC3E,kBAAkB,CAAC;IACpC,MAAMpB,kBAAkB,GAAGuD,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IACzDxD,kBAAkB,CAACtD,KAAK,CAAC0H,KAAK,GAAIwC,wBAAwB,GAAG,IAAK;IAClE5G,kBAAkB,CAACtD,KAAK,CAACuK,QAAQ,GAAIL,wBAAwB,GAAG,IAAK;IACrE5G,kBAAkB,CAACtD,KAAK,CAAC2K,YAAY,GAAG,MAAM;IAC9CrH,kBAAkB,CAACqD,SAAS,GAAG,yBAAyB,GAAGyD,yBAAyB;IACpF,IAAIR,IAAI,CAACtG,kBAAkB,KAAKvB,SAAS,EAAE;MACvCuB,kBAAkB,CAAC+F,WAAW,CAACxC,QAAQ,CAACyC,cAAc,CAACmB,MAAM,CAACb,IAAI,CAACtG,kBAAkB,CAAC,CAAC,CAAC;IAC5F,CAAC,MACI;MACDA,kBAAkB,CAACoH,SAAS,GAAG,QAAQ;IAC3C;IACAtB,IAAI,CAACC,WAAW,CAAC/F,kBAAkB,CAAC;IACpC,MAAMsH,MAAM,GAAG/D,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC7C8D,MAAM,CAACjE,SAAS,GAAG0D,eAAe;IAClC,IAAIC,UAAU,EAAE;MACZ,MAAMO,aAAa,GAAGhE,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MACpD+D,aAAa,CAAClE,SAAS,GAAGvJ,SAAS,CAAC6J,WAAW,CAACqD,UAAU,CAAC;MAC3DO,aAAa,CAACH,SAAS,GAAG,cAAc;MACxCE,MAAM,CAACvB,WAAW,CAACwB,aAAa,CAAC;IACrC,CAAC,MACI;MACDD,MAAM,CAACF,SAAS,GAAG,cAAc;IACrC;IACAtB,IAAI,CAACC,WAAW,CAACuB,MAAM,CAAC;IACxB,IAAIE,WAAW;IACf,IAAIlB,IAAI,CAACtG,kBAAkB,KAAKvB,SAAS,EAAE;MACvC,IAAIgJ,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC1C,aAAa,EAAEJ,eAAe,EAAEO,iBAAiB,CAACwC,OAAO,EAAErB,IAAI,CAACtG,kBAAkB,EAAE,IAAI,CAACmD,gBAAgB,CAACyE,eAAe,CAAC;MACvJ,IAAIrM,oBAAoB,CAACC,SAAS,EAAE;QAChCiM,IAAI,GAAGlM,oBAAoB,CAACC,SAAS,CAACC,UAAU,CAACgM,IAAI,CAAC;MAC1D;MACA3B,IAAI,CAAC+B,kBAAkB,CAAC,WAAW,EAAEJ,IAAI,CAAC;MAC1CD,WAAW,GAAGxC,aAAa,CAAC8C,cAAc,CAACxB,IAAI,CAACtG,kBAAkB,CAAC;IACvE,CAAC,MACI;MACD,IAAIyH,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC3C,aAAa,EAAEL,eAAe,EAAEO,iBAAiB,CAAC0C,OAAO,EAAErB,IAAI,CAAClF,kBAAkB,EAAE,IAAI,CAAC+B,gBAAgB,CAACyE,eAAe,CAAC;MACvJ,IAAIrM,oBAAoB,CAACC,SAAS,EAAE;QAChCiM,IAAI,GAAGlM,oBAAoB,CAACC,SAAS,CAACC,UAAU,CAACgM,IAAI,CAAC;MAC1D;MACA3B,IAAI,CAAC+B,kBAAkB,CAAC,WAAW,EAAEJ,IAAI,CAAC;MAC1CD,WAAW,GAAGzC,aAAa,CAAC+C,cAAc,CAACxB,IAAI,CAAClF,kBAAkB,CAAC;IACvE;IACA,IAAIoG,WAAW,CAACzP,MAAM,KAAK,CAAC,EAAE;MAC1ByP,WAAW,GAAG5M,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;IAChD;IACA,IAAImN,SAAS,GAAG,EAAE;IAClB,QAAQzB,IAAI,CAAC/G,IAAI;MACb,KAAKC,QAAQ,CAACwD,SAAS;QACnB,IAAIsD,IAAI,CAAClF,kBAAkB,KAAKkF,IAAI,CAACtG,kBAAkB,EAAE;UACrD+H,SAAS,GAAGnN,QAAQ,CAAC;YAAEjD,GAAG,EAAE,eAAe;YAAEkO,OAAO,EAAE,CAAC,yEAAyE;UAAE,CAAC,EAAE,wBAAwB,EAAE2B,WAAW,EAAElB,IAAI,CAAClF,kBAAkB,CAAC;QACxM,CAAC,MACI;UACD2G,SAAS,GAAGnN,QAAQ,CAAC,WAAW,EAAE,yCAAyC,EAAE4M,WAAW,EAAElB,IAAI,CAAClF,kBAAkB,EAAEkF,IAAI,CAACtG,kBAAkB,CAAC;QAC/I;QACA;MACJ,KAAKR,QAAQ,CAACK,KAAK;QACfkI,SAAS,GAAGnN,QAAQ,CAAC,YAAY,EAAE,yBAAyB,EAAE4M,WAAW,EAAElB,IAAI,CAACtG,kBAAkB,CAAC;QACnG;MACJ,KAAKR,QAAQ,CAACC,OAAO;QACjBsI,SAAS,GAAGnN,QAAQ,CAAC,YAAY,EAAE,yBAAyB,EAAE4M,WAAW,EAAElB,IAAI,CAAClF,kBAAkB,CAAC;QACnG;IACR;IACAkE,GAAG,CAACtB,YAAY,CAAC,YAAY,EAAE+D,SAAS,CAAC;IACzC,OAAOzC,GAAG;EACd;EACAoC,YAAYA,CAAC9K,KAAK,EAAEoL,OAAO,EAAEL,OAAO,EAAEzI,UAAU,EAAE0I,eAAe,EAAE;IAC/D,MAAMJ,WAAW,GAAG5K,KAAK,CAACkL,cAAc,CAAC5I,UAAU,CAAC;IACpD,MAAM+I,QAAQ,GAAGD,OAAO,CAAC1K,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;IAC5D,MAAM4K,UAAU,GAAG1N,UAAU,CAAC2N,WAAW,CAACX,WAAW,EAAEI,eAAe,CAAC;IACvE,MAAMQ,YAAY,GAAGzN,qBAAqB,CAACyN,YAAY,CAACZ,WAAW,EAAE5K,KAAK,CAACyL,yBAAyB,CAAC,CAAC,CAAC;IACvG,MAAMC,WAAW,GAAG3N,qBAAqB,CAAC2N,WAAW,CAACd,WAAW,EAAEY,YAAY,EAAExL,KAAK,CAAC2L,eAAe,CAAC,CAAC,CAAC;IACzG,MAAMvQ,CAAC,GAAG0C,eAAe,CAAC,IAAID,eAAe,CAAEwN,QAAQ,CAACO,WAAW,IAAI,CAACR,OAAO,CAAC1K,GAAG,CAAC,EAAE,CAAC,gDAAgD,CAAC,EAAG2K,QAAQ,CAACQ,8BAA8B,EAAEjB,WAAW,EAAE,KAAK,EAAEY,YAAY,EAAEE,WAAW,EAAE,CAAC,EAAEJ,UAAU,EAAE,EAAE,EAAEP,OAAO,EAAE,CAAC,EAAEM,QAAQ,CAACS,UAAU,EAAET,QAAQ,CAACU,WAAW,EAAEV,QAAQ,CAACW,aAAa,EAAEZ,OAAO,CAAC1K,GAAG,CAAC,GAAG,CAAC,yCAAyC,CAAC,EAAE0K,OAAO,CAAC1K,GAAG,CAAC,GAAG,CAAC,mCAAmC,CAAC,EAAE0K,OAAO,CAAC1K,GAAG,CAAC,EAAE,CAAC,0CAA0C,CAAC,EAAE0K,OAAO,CAAC1K,GAAG,CAAC,EAAE,CAAC,gCAAgC,CAAC,KAAKrD,mBAAmB,CAAC4O,GAAG,EAAE,IAAI,CAAC,CAAC;IACvkB,OAAO7Q,CAAC,CAACyP,IAAI;EACjB;AACJ,CAAC;AACDzK,IAAI,GAAGxF,UAAU,CAAC,CACdgB,OAAO,CAAC,CAAC,EAAE+B,gBAAgB,CAAC,CAC/B,EAAEyC,IAAI,CAAC;AACR,OAAO,MAAM8L,oCAAoC,CAAC;EAC9CnN,WAAWA,CAACoN,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACApK,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACoK,OAAO,CAAC5G,QAAQ,CAAC6G,QAAQ,CAAC,CAAC;EAC3C;EACArE,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACoE,OAAO,CAAC5G,QAAQ,CAAC+C,UAAU,CAAC,CAAC;EAC7C;EACA/D,cAAcA,CAAChC,KAAK,EAAE;IAClB,IAAI,CAAC4J,OAAO,CAAC5G,QAAQ,CAAC8G,WAAW,CAAC9J,KAAK,CAAC;IACxC,IAAI,CAAC4J,OAAO,CAAC5G,QAAQ,CAAC+G,YAAY,CAAC/J,KAAK,CAAC;IACzC,IAAI,CAAC4J,OAAO,CAAC5G,QAAQ,CAACkE,KAAK,CAAC,CAAC;EACjC;EACAxH,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACkK,OAAO,CAAC3J,QAAQ,CAAC4J,QAAQ,CAAC,CAAC;EAC3C;EACAnE,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACkE,OAAO,CAAC3J,QAAQ,CAAC8F,UAAU,CAAC,CAAC;EAC7C;EACA7D,cAAcA,CAAClC,KAAK,EAAE;IAClB,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC4J,OAAO,CAAC3J,QAAQ,CAAC6J,WAAW,CAAC9J,KAAK,CAAC;MACxC,IAAI,CAAC4J,OAAO,CAAC3J,QAAQ,CAAC8J,YAAY,CAAC/J,KAAK,CAAC;IAC7C;IACA,IAAI,CAAC4J,OAAO,CAAC3J,QAAQ,CAACiH,KAAK,CAAC,CAAC;EACjC;EACAlG,oBAAoBA,CAAChB,KAAK,EAAE;IACxB,IAAI,CAAC4J,OAAO,CAAC3J,QAAQ,CAAC8J,YAAY,CAAC/J,KAAK,CAAC;EAC7C;EACAmC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACyH,OAAO,CAAC3J,QAAQ,CAACiH,KAAK,CAAC,CAAC;EACjC;EACAtH,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACgK,OAAO,CAAC3J,QAAQ,CAAC+J,WAAW,CAAC,CAAC,IAAI1K,SAAS;EAC3D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}