{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { VSBuffer } from './buffer.js';\nimport { URI } from './uri.js';\nexport function stringify(obj) {\n  return JSON.stringify(obj, replacer);\n}\nexport function parse(text) {\n  let data = JSON.parse(text);\n  data = revive(data);\n  return data;\n}\nfunction replacer(key, value) {\n  // URI is done via toJSON-member\n  if (value instanceof RegExp) {\n    return {\n      $mid: 2 /* MarshalledId.Regexp */,\n      source: value.source,\n      flags: value.flags\n    };\n  }\n  return value;\n}\nexport function revive(obj, depth = 0) {\n  if (!obj || depth > 200) {\n    return obj;\n  }\n  if (typeof obj === 'object') {\n    switch (obj.$mid) {\n      case 1 /* MarshalledId.Uri */:\n        return URI.revive(obj);\n      case 2 /* MarshalledId.Regexp */:\n        return new RegExp(obj.source, obj.flags);\n      case 17 /* MarshalledId.Date */:\n        return new Date(obj.source);\n    }\n    if (obj instanceof VSBuffer || obj instanceof Uint8Array) {\n      return obj;\n    }\n    if (Array.isArray(obj)) {\n      for (let i = 0; i < obj.length; ++i) {\n        obj[i] = revive(obj[i], depth + 1);\n      }\n    } else {\n      // walk object\n      for (const key in obj) {\n        if (Object.hasOwnProperty.call(obj, key)) {\n          obj[key] = revive(obj[key], depth + 1);\n        }\n      }\n    }\n  }\n  return obj;\n}", "map": {"version": 3, "names": ["VSBuffer", "URI", "stringify", "obj", "JSON", "replacer", "parse", "text", "data", "revive", "key", "value", "RegExp", "$mid", "source", "flags", "depth", "Date", "Uint8Array", "Array", "isArray", "i", "length", "Object", "hasOwnProperty", "call"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/marshalling.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { VSBuffer } from './buffer.js';\nimport { URI } from './uri.js';\nexport function stringify(obj) {\n    return JSON.stringify(obj, replacer);\n}\nexport function parse(text) {\n    let data = JSON.parse(text);\n    data = revive(data);\n    return data;\n}\nfunction replacer(key, value) {\n    // URI is done via toJSON-member\n    if (value instanceof RegExp) {\n        return {\n            $mid: 2 /* MarshalledId.Regexp */,\n            source: value.source,\n            flags: value.flags,\n        };\n    }\n    return value;\n}\nexport function revive(obj, depth = 0) {\n    if (!obj || depth > 200) {\n        return obj;\n    }\n    if (typeof obj === 'object') {\n        switch (obj.$mid) {\n            case 1 /* MarshalledId.Uri */: return URI.revive(obj);\n            case 2 /* MarshalledId.Regexp */: return new RegExp(obj.source, obj.flags);\n            case 17 /* MarshalledId.Date */: return new Date(obj.source);\n        }\n        if (obj instanceof VSBuffer\n            || obj instanceof Uint8Array) {\n            return obj;\n        }\n        if (Array.isArray(obj)) {\n            for (let i = 0; i < obj.length; ++i) {\n                obj[i] = revive(obj[i], depth + 1);\n            }\n        }\n        else {\n            // walk object\n            for (const key in obj) {\n                if (Object.hasOwnProperty.call(obj, key)) {\n                    obj[key] = revive(obj[key], depth + 1);\n                }\n            }\n        }\n    }\n    return obj;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,aAAa;AACtC,SAASC,GAAG,QAAQ,UAAU;AAC9B,OAAO,SAASC,SAASA,CAACC,GAAG,EAAE;EAC3B,OAAOC,IAAI,CAACF,SAAS,CAACC,GAAG,EAAEE,QAAQ,CAAC;AACxC;AACA,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EACxB,IAAIC,IAAI,GAAGJ,IAAI,CAACE,KAAK,CAACC,IAAI,CAAC;EAC3BC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAC;EACnB,OAAOA,IAAI;AACf;AACA,SAASH,QAAQA,CAACK,GAAG,EAAEC,KAAK,EAAE;EAC1B;EACA,IAAIA,KAAK,YAAYC,MAAM,EAAE;IACzB,OAAO;MACHC,IAAI,EAAE,CAAC,CAAC;MACRC,MAAM,EAAEH,KAAK,CAACG,MAAM;MACpBC,KAAK,EAAEJ,KAAK,CAACI;IACjB,CAAC;EACL;EACA,OAAOJ,KAAK;AAChB;AACA,OAAO,SAASF,MAAMA,CAACN,GAAG,EAAEa,KAAK,GAAG,CAAC,EAAE;EACnC,IAAI,CAACb,GAAG,IAAIa,KAAK,GAAG,GAAG,EAAE;IACrB,OAAOb,GAAG;EACd;EACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzB,QAAQA,GAAG,CAACU,IAAI;MACZ,KAAK,CAAC,CAAC;QAAwB,OAAOZ,GAAG,CAACQ,MAAM,CAACN,GAAG,CAAC;MACrD,KAAK,CAAC,CAAC;QAA2B,OAAO,IAAIS,MAAM,CAACT,GAAG,CAACW,MAAM,EAAEX,GAAG,CAACY,KAAK,CAAC;MAC1E,KAAK,EAAE,CAAC;QAAyB,OAAO,IAAIE,IAAI,CAACd,GAAG,CAACW,MAAM,CAAC;IAChE;IACA,IAAIX,GAAG,YAAYH,QAAQ,IACpBG,GAAG,YAAYe,UAAU,EAAE;MAC9B,OAAOf,GAAG;IACd;IACA,IAAIgB,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;MACpB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,GAAG,CAACmB,MAAM,EAAE,EAAED,CAAC,EAAE;QACjClB,GAAG,CAACkB,CAAC,CAAC,GAAGZ,MAAM,CAACN,GAAG,CAACkB,CAAC,CAAC,EAAEL,KAAK,GAAG,CAAC,CAAC;MACtC;IACJ,CAAC,MACI;MACD;MACA,KAAK,MAAMN,GAAG,IAAIP,GAAG,EAAE;QACnB,IAAIoB,MAAM,CAACC,cAAc,CAACC,IAAI,CAACtB,GAAG,EAAEO,GAAG,CAAC,EAAE;UACtCP,GAAG,CAACO,GAAG,CAAC,GAAGD,MAAM,CAACN,GAAG,CAACO,GAAG,CAAC,EAAEM,KAAK,GAAG,CAAC,CAAC;QAC1C;MACJ;IACJ;EACJ;EACA,OAAOb,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}