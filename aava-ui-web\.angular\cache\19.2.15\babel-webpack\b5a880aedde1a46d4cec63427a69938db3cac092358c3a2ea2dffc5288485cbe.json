{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { isFirefox } from '../../browser.js';\nimport { EventType as TouchEventType, Gesture } from '../../touch.js';\nimport { $, addDisposableListener, append, clearNode, createStyleSheet, Dimension, EventHelper, EventType, getActiveElement, getWindow, isAncestor, isInShadowDOM } from '../../dom.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { StandardMouseEvent } from '../../mouseEvent.js';\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport { ActionViewItem, BaseActionViewItem } from '../actionbar/actionViewItems.js';\nimport { layout } from '../contextview/contextview.js';\nimport { DomScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { EmptySubmenuAction, Separator, SubmenuAction } from '../../../common/actions.js';\nimport { RunOnceScheduler } from '../../../common/async.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { getCodiconFontCharacters } from '../../../common/codiconsUtil.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { stripIcons } from '../../../common/iconLabels.js';\nimport { DisposableStore } from '../../../common/lifecycle.js';\nimport { isLinux, isMacintosh } from '../../../common/platform.js';\nimport * as strings from '../../../common/strings.js';\nexport const MENU_MNEMONIC_REGEX = /\\(&([^\\s&])\\)|(^|[^&])&([^\\s&])/;\nexport const MENU_ESCAPED_MNEMONIC_REGEX = /(&amp;)?(&amp;)([^\\s&])/g;\nexport var HorizontalDirection = /*#__PURE__*/function (HorizontalDirection) {\n  HorizontalDirection[HorizontalDirection[\"Right\"] = 0] = \"Right\";\n  HorizontalDirection[HorizontalDirection[\"Left\"] = 1] = \"Left\";\n  return HorizontalDirection;\n}(HorizontalDirection || {});\nexport var VerticalDirection = /*#__PURE__*/function (VerticalDirection) {\n  VerticalDirection[VerticalDirection[\"Above\"] = 0] = \"Above\";\n  VerticalDirection[VerticalDirection[\"Below\"] = 1] = \"Below\";\n  return VerticalDirection;\n}(VerticalDirection || {});\nexport class Menu extends ActionBar {\n  constructor(container, actions, options, menuStyles) {\n    container.classList.add('monaco-menu-container');\n    container.setAttribute('role', 'presentation');\n    const menuElement = document.createElement('div');\n    menuElement.classList.add('monaco-menu');\n    menuElement.setAttribute('role', 'presentation');\n    super(menuElement, {\n      orientation: 1 /* ActionsOrientation.VERTICAL */,\n      actionViewItemProvider: action => this.doGetActionViewItem(action, options, parentData),\n      context: options.context,\n      actionRunner: options.actionRunner,\n      ariaLabel: options.ariaLabel,\n      ariaRole: 'menu',\n      focusOnlyEnabledItems: true,\n      triggerKeys: {\n        keys: [3 /* KeyCode.Enter */, ...(isMacintosh || isLinux ? [10 /* KeyCode.Space */] : [])],\n        keyDown: true\n      }\n    });\n    this.menuStyles = menuStyles;\n    this.menuElement = menuElement;\n    this.actionsList.tabIndex = 0;\n    this.initializeOrUpdateStyleSheet(container, menuStyles);\n    this._register(Gesture.addTarget(menuElement));\n    this._register(addDisposableListener(menuElement, EventType.KEY_DOWN, e => {\n      const event = new StandardKeyboardEvent(e);\n      // Stop tab navigation of menus\n      if (event.equals(2 /* KeyCode.Tab */)) {\n        e.preventDefault();\n      }\n    }));\n    if (options.enableMnemonics) {\n      this._register(addDisposableListener(menuElement, EventType.KEY_DOWN, e => {\n        const key = e.key.toLocaleLowerCase();\n        if (this.mnemonics.has(key)) {\n          EventHelper.stop(e, true);\n          const actions = this.mnemonics.get(key);\n          if (actions.length === 1) {\n            if (actions[0] instanceof SubmenuMenuActionViewItem && actions[0].container) {\n              this.focusItemByElement(actions[0].container);\n            }\n            actions[0].onClick(e);\n          }\n          if (actions.length > 1) {\n            const action = actions.shift();\n            if (action && action.container) {\n              this.focusItemByElement(action.container);\n              actions.push(action);\n            }\n            this.mnemonics.set(key, actions);\n          }\n        }\n      }));\n    }\n    if (isLinux) {\n      this._register(addDisposableListener(menuElement, EventType.KEY_DOWN, e => {\n        const event = new StandardKeyboardEvent(e);\n        if (event.equals(14 /* KeyCode.Home */) || event.equals(11 /* KeyCode.PageUp */)) {\n          this.focusedItem = this.viewItems.length - 1;\n          this.focusNext();\n          EventHelper.stop(e, true);\n        } else if (event.equals(13 /* KeyCode.End */) || event.equals(12 /* KeyCode.PageDown */)) {\n          this.focusedItem = 0;\n          this.focusPrevious();\n          EventHelper.stop(e, true);\n        }\n      }));\n    }\n    this._register(addDisposableListener(this.domNode, EventType.MOUSE_OUT, e => {\n      const relatedTarget = e.relatedTarget;\n      if (!isAncestor(relatedTarget, this.domNode)) {\n        this.focusedItem = undefined;\n        this.updateFocus();\n        e.stopPropagation();\n      }\n    }));\n    this._register(addDisposableListener(this.actionsList, EventType.MOUSE_OVER, e => {\n      let target = e.target;\n      if (!target || !isAncestor(target, this.actionsList) || target === this.actionsList) {\n        return;\n      }\n      while (target.parentElement !== this.actionsList && target.parentElement !== null) {\n        target = target.parentElement;\n      }\n      if (target.classList.contains('action-item')) {\n        const lastFocusedItem = this.focusedItem;\n        this.setFocusedItem(target);\n        if (lastFocusedItem !== this.focusedItem) {\n          this.updateFocus();\n        }\n      }\n    }));\n    // Support touch on actions list to focus items (needed for submenus)\n    this._register(Gesture.addTarget(this.actionsList));\n    this._register(addDisposableListener(this.actionsList, TouchEventType.Tap, e => {\n      let target = e.initialTarget;\n      if (!target || !isAncestor(target, this.actionsList) || target === this.actionsList) {\n        return;\n      }\n      while (target.parentElement !== this.actionsList && target.parentElement !== null) {\n        target = target.parentElement;\n      }\n      if (target.classList.contains('action-item')) {\n        const lastFocusedItem = this.focusedItem;\n        this.setFocusedItem(target);\n        if (lastFocusedItem !== this.focusedItem) {\n          this.updateFocus();\n        }\n      }\n    }));\n    const parentData = {\n      parent: this\n    };\n    this.mnemonics = new Map();\n    // Scroll Logic\n    this.scrollableElement = this._register(new DomScrollableElement(menuElement, {\n      alwaysConsumeMouseWheel: true,\n      horizontal: 2 /* ScrollbarVisibility.Hidden */,\n      vertical: 3 /* ScrollbarVisibility.Visible */,\n      verticalScrollbarSize: 7,\n      handleMouseWheel: true,\n      useShadows: true\n    }));\n    const scrollElement = this.scrollableElement.getDomNode();\n    scrollElement.style.position = '';\n    this.styleScrollElement(scrollElement, menuStyles);\n    // Support scroll on menu drag\n    this._register(addDisposableListener(menuElement, TouchEventType.Change, e => {\n      EventHelper.stop(e, true);\n      const scrollTop = this.scrollableElement.getScrollPosition().scrollTop;\n      this.scrollableElement.setScrollPosition({\n        scrollTop: scrollTop - e.translationY\n      });\n    }));\n    this._register(addDisposableListener(scrollElement, EventType.MOUSE_UP, e => {\n      // Absorb clicks in menu dead space https://github.com/microsoft/vscode/issues/63575\n      // We do this on the scroll element so the scroll bar doesn't dismiss the menu either\n      e.preventDefault();\n    }));\n    const window = getWindow(container);\n    menuElement.style.maxHeight = `${Math.max(10, window.innerHeight - container.getBoundingClientRect().top - 35)}px`;\n    actions = actions.filter((a, idx) => {\n      if (options.submenuIds?.has(a.id)) {\n        console.warn(`Found submenu cycle: ${a.id}`);\n        return false;\n      }\n      // Filter out consecutive or useless separators\n      if (a instanceof Separator) {\n        if (idx === actions.length - 1 || idx === 0) {\n          return false;\n        }\n        const prevAction = actions[idx - 1];\n        if (prevAction instanceof Separator) {\n          return false;\n        }\n      }\n      return true;\n    });\n    this.push(actions, {\n      icon: true,\n      label: true,\n      isMenu: true\n    });\n    container.appendChild(this.scrollableElement.getDomNode());\n    this.scrollableElement.scanDomNode();\n    this.viewItems.filter(item => !(item instanceof MenuSeparatorActionViewItem)).forEach((item, index, array) => {\n      item.updatePositionInSet(index + 1, array.length);\n    });\n  }\n  initializeOrUpdateStyleSheet(container, style) {\n    if (!this.styleSheet) {\n      if (isInShadowDOM(container)) {\n        this.styleSheet = createStyleSheet(container);\n      } else {\n        if (!Menu.globalStyleSheet) {\n          Menu.globalStyleSheet = createStyleSheet();\n        }\n        this.styleSheet = Menu.globalStyleSheet;\n      }\n    }\n    this.styleSheet.textContent = getMenuWidgetCSS(style, isInShadowDOM(container));\n  }\n  styleScrollElement(scrollElement, style) {\n    const fgColor = style.foregroundColor ?? '';\n    const bgColor = style.backgroundColor ?? '';\n    const border = style.borderColor ? `1px solid ${style.borderColor}` : '';\n    const borderRadius = '5px';\n    const shadow = style.shadowColor ? `0 2px 8px ${style.shadowColor}` : '';\n    scrollElement.style.outline = border;\n    scrollElement.style.borderRadius = borderRadius;\n    scrollElement.style.color = fgColor;\n    scrollElement.style.backgroundColor = bgColor;\n    scrollElement.style.boxShadow = shadow;\n  }\n  getContainer() {\n    return this.scrollableElement.getDomNode();\n  }\n  get onScroll() {\n    return this.scrollableElement.onScroll;\n  }\n  focusItemByElement(element) {\n    const lastFocusedItem = this.focusedItem;\n    this.setFocusedItem(element);\n    if (lastFocusedItem !== this.focusedItem) {\n      this.updateFocus();\n    }\n  }\n  setFocusedItem(element) {\n    for (let i = 0; i < this.actionsList.children.length; i++) {\n      const elem = this.actionsList.children[i];\n      if (element === elem) {\n        this.focusedItem = i;\n        break;\n      }\n    }\n  }\n  updateFocus(fromRight) {\n    super.updateFocus(fromRight, true, true);\n    if (typeof this.focusedItem !== 'undefined') {\n      // Workaround for #80047 caused by an issue in chromium\n      // https://bugs.chromium.org/p/chromium/issues/detail?id=414283\n      // When that's fixed, just call this.scrollableElement.scanDomNode()\n      this.scrollableElement.setScrollPosition({\n        scrollTop: Math.round(this.menuElement.scrollTop)\n      });\n    }\n  }\n  doGetActionViewItem(action, options, parentData) {\n    if (action instanceof Separator) {\n      return new MenuSeparatorActionViewItem(options.context, action, {\n        icon: true\n      }, this.menuStyles);\n    } else if (action instanceof SubmenuAction) {\n      const menuActionViewItem = new SubmenuMenuActionViewItem(action, action.actions, parentData, {\n        ...options,\n        submenuIds: new Set([...(options.submenuIds || []), action.id])\n      }, this.menuStyles);\n      if (options.enableMnemonics) {\n        const mnemonic = menuActionViewItem.getMnemonic();\n        if (mnemonic && menuActionViewItem.isEnabled()) {\n          let actionViewItems = [];\n          if (this.mnemonics.has(mnemonic)) {\n            actionViewItems = this.mnemonics.get(mnemonic);\n          }\n          actionViewItems.push(menuActionViewItem);\n          this.mnemonics.set(mnemonic, actionViewItems);\n        }\n      }\n      return menuActionViewItem;\n    } else {\n      const menuItemOptions = {\n        enableMnemonics: options.enableMnemonics,\n        useEventAsContext: options.useEventAsContext\n      };\n      if (options.getKeyBinding) {\n        const keybinding = options.getKeyBinding(action);\n        if (keybinding) {\n          const keybindingLabel = keybinding.getLabel();\n          if (keybindingLabel) {\n            menuItemOptions.keybinding = keybindingLabel;\n          }\n        }\n      }\n      const menuActionViewItem = new BaseMenuActionViewItem(options.context, action, menuItemOptions, this.menuStyles);\n      if (options.enableMnemonics) {\n        const mnemonic = menuActionViewItem.getMnemonic();\n        if (mnemonic && menuActionViewItem.isEnabled()) {\n          let actionViewItems = [];\n          if (this.mnemonics.has(mnemonic)) {\n            actionViewItems = this.mnemonics.get(mnemonic);\n          }\n          actionViewItems.push(menuActionViewItem);\n          this.mnemonics.set(mnemonic, actionViewItems);\n        }\n      }\n      return menuActionViewItem;\n    }\n  }\n}\nclass BaseMenuActionViewItem extends BaseActionViewItem {\n  constructor(ctx, action, options, menuStyle) {\n    options.isMenu = true;\n    super(action, action, options);\n    this.menuStyle = menuStyle;\n    this.options = options;\n    this.options.icon = options.icon !== undefined ? options.icon : false;\n    this.options.label = options.label !== undefined ? options.label : true;\n    this.cssClass = '';\n    // Set mnemonic\n    if (this.options.label && options.enableMnemonics) {\n      const label = this.action.label;\n      if (label) {\n        const matches = MENU_MNEMONIC_REGEX.exec(label);\n        if (matches) {\n          this.mnemonic = (!!matches[1] ? matches[1] : matches[3]).toLocaleLowerCase();\n        }\n      }\n    }\n    // Add mouse up listener later to avoid accidental clicks\n    this.runOnceToEnableMouseUp = new RunOnceScheduler(() => {\n      if (!this.element) {\n        return;\n      }\n      this._register(addDisposableListener(this.element, EventType.MOUSE_UP, e => {\n        // removed default prevention as it conflicts\n        // with BaseActionViewItem #101537\n        // add back if issues arise and link new issue\n        EventHelper.stop(e, true);\n        // See https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Interact_with_the_clipboard\n        // > Writing to the clipboard\n        // > You can use the \"cut\" and \"copy\" commands without any special\n        // permission if you are using them in a short-lived event handler\n        // for a user action (for example, a click handler).\n        // => to get the Copy and Paste context menu actions working on Firefox,\n        // there should be no timeout here\n        if (isFirefox) {\n          const mouseEvent = new StandardMouseEvent(getWindow(this.element), e);\n          // Allowing right click to trigger the event causes the issue described below,\n          // but since the solution below does not work in FF, we must disable right click\n          if (mouseEvent.rightButton) {\n            return;\n          }\n          this.onClick(e);\n        }\n        // In all other cases, set timeout to allow context menu cancellation to trigger\n        // otherwise the action will destroy the menu and a second context menu\n        // will still trigger for right click.\n        else {\n          setTimeout(() => {\n            this.onClick(e);\n          }, 0);\n        }\n      }));\n      this._register(addDisposableListener(this.element, EventType.CONTEXT_MENU, e => {\n        EventHelper.stop(e, true);\n      }));\n    }, 100);\n    this._register(this.runOnceToEnableMouseUp);\n  }\n  render(container) {\n    super.render(container);\n    if (!this.element) {\n      return;\n    }\n    this.container = container;\n    this.item = append(this.element, $('a.action-menu-item'));\n    if (this._action.id === Separator.ID) {\n      // A separator is a presentation item\n      this.item.setAttribute('role', 'presentation');\n    } else {\n      this.item.setAttribute('role', 'menuitem');\n      if (this.mnemonic) {\n        this.item.setAttribute('aria-keyshortcuts', `${this.mnemonic}`);\n      }\n    }\n    this.check = append(this.item, $('span.menu-item-check' + ThemeIcon.asCSSSelector(Codicon.menuSelection)));\n    this.check.setAttribute('role', 'none');\n    this.label = append(this.item, $('span.action-label'));\n    if (this.options.label && this.options.keybinding) {\n      append(this.item, $('span.keybinding')).textContent = this.options.keybinding;\n    }\n    // Adds mouse up listener to actually run the action\n    this.runOnceToEnableMouseUp.schedule();\n    this.updateClass();\n    this.updateLabel();\n    this.updateTooltip();\n    this.updateEnabled();\n    this.updateChecked();\n    this.applyStyle();\n  }\n  blur() {\n    super.blur();\n    this.applyStyle();\n  }\n  focus() {\n    super.focus();\n    this.item?.focus();\n    this.applyStyle();\n  }\n  updatePositionInSet(pos, setSize) {\n    if (this.item) {\n      this.item.setAttribute('aria-posinset', `${pos}`);\n      this.item.setAttribute('aria-setsize', `${setSize}`);\n    }\n  }\n  updateLabel() {\n    if (!this.label) {\n      return;\n    }\n    if (this.options.label) {\n      clearNode(this.label);\n      let label = stripIcons(this.action.label);\n      if (label) {\n        const cleanLabel = cleanMnemonic(label);\n        if (!this.options.enableMnemonics) {\n          label = cleanLabel;\n        }\n        this.label.setAttribute('aria-label', cleanLabel.replace(/&&/g, '&'));\n        const matches = MENU_MNEMONIC_REGEX.exec(label);\n        if (matches) {\n          label = strings.escape(label);\n          // This is global, reset it\n          MENU_ESCAPED_MNEMONIC_REGEX.lastIndex = 0;\n          let escMatch = MENU_ESCAPED_MNEMONIC_REGEX.exec(label);\n          // We can't use negative lookbehind so if we match our negative and skip\n          while (escMatch && escMatch[1]) {\n            escMatch = MENU_ESCAPED_MNEMONIC_REGEX.exec(label);\n          }\n          const replaceDoubleEscapes = str => str.replace(/&amp;&amp;/g, '&amp;');\n          if (escMatch) {\n            this.label.append(strings.ltrim(replaceDoubleEscapes(label.substr(0, escMatch.index)), ' '), $('u', {\n              'aria-hidden': 'true'\n            }, escMatch[3]), strings.rtrim(replaceDoubleEscapes(label.substr(escMatch.index + escMatch[0].length)), ' '));\n          } else {\n            this.label.innerText = replaceDoubleEscapes(label).trim();\n          }\n          this.item?.setAttribute('aria-keyshortcuts', (!!matches[1] ? matches[1] : matches[3]).toLocaleLowerCase());\n        } else {\n          this.label.innerText = label.replace(/&&/g, '&').trim();\n        }\n      }\n    }\n  }\n  updateTooltip() {\n    // menus should function like native menus and they do not have tooltips\n  }\n  updateClass() {\n    if (this.cssClass && this.item) {\n      this.item.classList.remove(...this.cssClass.split(' '));\n    }\n    if (this.options.icon && this.label) {\n      this.cssClass = this.action.class || '';\n      this.label.classList.add('icon');\n      if (this.cssClass) {\n        this.label.classList.add(...this.cssClass.split(' '));\n      }\n      this.updateEnabled();\n    } else if (this.label) {\n      this.label.classList.remove('icon');\n    }\n  }\n  updateEnabled() {\n    if (this.action.enabled) {\n      if (this.element) {\n        this.element.classList.remove('disabled');\n        this.element.removeAttribute('aria-disabled');\n      }\n      if (this.item) {\n        this.item.classList.remove('disabled');\n        this.item.removeAttribute('aria-disabled');\n        this.item.tabIndex = 0;\n      }\n    } else {\n      if (this.element) {\n        this.element.classList.add('disabled');\n        this.element.setAttribute('aria-disabled', 'true');\n      }\n      if (this.item) {\n        this.item.classList.add('disabled');\n        this.item.setAttribute('aria-disabled', 'true');\n      }\n    }\n  }\n  updateChecked() {\n    if (!this.item) {\n      return;\n    }\n    const checked = this.action.checked;\n    this.item.classList.toggle('checked', !!checked);\n    if (checked !== undefined) {\n      this.item.setAttribute('role', 'menuitemcheckbox');\n      this.item.setAttribute('aria-checked', checked ? 'true' : 'false');\n    } else {\n      this.item.setAttribute('role', 'menuitem');\n      this.item.setAttribute('aria-checked', '');\n    }\n  }\n  getMnemonic() {\n    return this.mnemonic;\n  }\n  applyStyle() {\n    const isSelected = this.element && this.element.classList.contains('focused');\n    const fgColor = isSelected && this.menuStyle.selectionForegroundColor ? this.menuStyle.selectionForegroundColor : this.menuStyle.foregroundColor;\n    const bgColor = isSelected && this.menuStyle.selectionBackgroundColor ? this.menuStyle.selectionBackgroundColor : undefined;\n    const outline = isSelected && this.menuStyle.selectionBorderColor ? `1px solid ${this.menuStyle.selectionBorderColor}` : '';\n    const outlineOffset = isSelected && this.menuStyle.selectionBorderColor ? `-1px` : '';\n    if (this.item) {\n      this.item.style.color = fgColor ?? '';\n      this.item.style.backgroundColor = bgColor ?? '';\n      this.item.style.outline = outline;\n      this.item.style.outlineOffset = outlineOffset;\n    }\n    if (this.check) {\n      this.check.style.color = fgColor ?? '';\n    }\n  }\n}\nclass SubmenuMenuActionViewItem extends BaseMenuActionViewItem {\n  constructor(action, submenuActions, parentData, submenuOptions, menuStyles) {\n    super(action, action, submenuOptions, menuStyles);\n    this.submenuActions = submenuActions;\n    this.parentData = parentData;\n    this.submenuOptions = submenuOptions;\n    this.mysubmenu = null;\n    this.submenuDisposables = this._register(new DisposableStore());\n    this.mouseOver = false;\n    this.expandDirection = submenuOptions && submenuOptions.expandDirection !== undefined ? submenuOptions.expandDirection : {\n      horizontal: HorizontalDirection.Right,\n      vertical: VerticalDirection.Below\n    };\n    this.showScheduler = new RunOnceScheduler(() => {\n      if (this.mouseOver) {\n        this.cleanupExistingSubmenu(false);\n        this.createSubmenu(false);\n      }\n    }, 250);\n    this.hideScheduler = new RunOnceScheduler(() => {\n      if (this.element && !isAncestor(getActiveElement(), this.element) && this.parentData.submenu === this.mysubmenu) {\n        this.parentData.parent.focus(false);\n        this.cleanupExistingSubmenu(true);\n      }\n    }, 750);\n  }\n  render(container) {\n    super.render(container);\n    if (!this.element) {\n      return;\n    }\n    if (this.item) {\n      this.item.classList.add('monaco-submenu-item');\n      this.item.tabIndex = 0;\n      this.item.setAttribute('aria-haspopup', 'true');\n      this.updateAriaExpanded('false');\n      this.submenuIndicator = append(this.item, $('span.submenu-indicator' + ThemeIcon.asCSSSelector(Codicon.menuSubmenu)));\n      this.submenuIndicator.setAttribute('aria-hidden', 'true');\n    }\n    this._register(addDisposableListener(this.element, EventType.KEY_UP, e => {\n      const event = new StandardKeyboardEvent(e);\n      if (event.equals(17 /* KeyCode.RightArrow */) || event.equals(3 /* KeyCode.Enter */)) {\n        EventHelper.stop(e, true);\n        this.createSubmenu(true);\n      }\n    }));\n    this._register(addDisposableListener(this.element, EventType.KEY_DOWN, e => {\n      const event = new StandardKeyboardEvent(e);\n      if (getActiveElement() === this.item) {\n        if (event.equals(17 /* KeyCode.RightArrow */) || event.equals(3 /* KeyCode.Enter */)) {\n          EventHelper.stop(e, true);\n        }\n      }\n    }));\n    this._register(addDisposableListener(this.element, EventType.MOUSE_OVER, e => {\n      if (!this.mouseOver) {\n        this.mouseOver = true;\n        this.showScheduler.schedule();\n      }\n    }));\n    this._register(addDisposableListener(this.element, EventType.MOUSE_LEAVE, e => {\n      this.mouseOver = false;\n    }));\n    this._register(addDisposableListener(this.element, EventType.FOCUS_OUT, e => {\n      if (this.element && !isAncestor(getActiveElement(), this.element)) {\n        this.hideScheduler.schedule();\n      }\n    }));\n    this._register(this.parentData.parent.onScroll(() => {\n      if (this.parentData.submenu === this.mysubmenu) {\n        this.parentData.parent.focus(false);\n        this.cleanupExistingSubmenu(true);\n      }\n    }));\n  }\n  updateEnabled() {\n    // override on submenu entry\n    // native menus do not observe enablement on sumbenus\n    // we mimic that behavior\n  }\n  onClick(e) {\n    // stop clicking from trying to run an action\n    EventHelper.stop(e, true);\n    this.cleanupExistingSubmenu(false);\n    this.createSubmenu(true);\n  }\n  cleanupExistingSubmenu(force) {\n    if (this.parentData.submenu && (force || this.parentData.submenu !== this.mysubmenu)) {\n      // disposal may throw if the submenu has already been removed\n      try {\n        this.parentData.submenu.dispose();\n      } catch {}\n      this.parentData.submenu = undefined;\n      this.updateAriaExpanded('false');\n      if (this.submenuContainer) {\n        this.submenuDisposables.clear();\n        this.submenuContainer = undefined;\n      }\n    }\n  }\n  calculateSubmenuMenuLayout(windowDimensions, submenu, entry, expandDirection) {\n    const ret = {\n      top: 0,\n      left: 0\n    };\n    // Start with horizontal\n    ret.left = layout(windowDimensions.width, submenu.width, {\n      position: expandDirection.horizontal === HorizontalDirection.Right ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */,\n      offset: entry.left,\n      size: entry.width\n    });\n    // We don't have enough room to layout the menu fully, so we are overlapping the menu\n    if (ret.left >= entry.left && ret.left < entry.left + entry.width) {\n      if (entry.left + 10 + submenu.width <= windowDimensions.width) {\n        ret.left = entry.left + 10;\n      }\n      entry.top += 10;\n      entry.height = 0;\n    }\n    // Now that we have a horizontal position, try layout vertically\n    ret.top = layout(windowDimensions.height, submenu.height, {\n      position: 0 /* LayoutAnchorPosition.Before */,\n      offset: entry.top,\n      size: 0\n    });\n    // We didn't have enough room below, but we did above, so we shift down to align the menu\n    if (ret.top + submenu.height === entry.top && ret.top + entry.height + submenu.height <= windowDimensions.height) {\n      ret.top += entry.height;\n    }\n    return ret;\n  }\n  createSubmenu(selectFirstItem = true) {\n    if (!this.element) {\n      return;\n    }\n    if (!this.parentData.submenu) {\n      this.updateAriaExpanded('true');\n      this.submenuContainer = append(this.element, $('div.monaco-submenu'));\n      this.submenuContainer.classList.add('menubar-menu-items-holder', 'context-view');\n      // Set the top value of the menu container before construction\n      // This allows the menu constructor to calculate the proper max height\n      const computedStyles = getWindow(this.parentData.parent.domNode).getComputedStyle(this.parentData.parent.domNode);\n      const paddingTop = parseFloat(computedStyles.paddingTop || '0') || 0;\n      // this.submenuContainer.style.top = `${this.element.offsetTop - this.parentData.parent.scrollOffset - paddingTop}px`;\n      this.submenuContainer.style.zIndex = '1';\n      this.submenuContainer.style.position = 'fixed';\n      this.submenuContainer.style.top = '0';\n      this.submenuContainer.style.left = '0';\n      this.parentData.submenu = new Menu(this.submenuContainer, this.submenuActions.length ? this.submenuActions : [new EmptySubmenuAction()], this.submenuOptions, this.menuStyle);\n      // layout submenu\n      const entryBox = this.element.getBoundingClientRect();\n      const entryBoxUpdated = {\n        top: entryBox.top - paddingTop,\n        left: entryBox.left,\n        height: entryBox.height + 2 * paddingTop,\n        width: entryBox.width\n      };\n      const viewBox = this.submenuContainer.getBoundingClientRect();\n      const window = getWindow(this.element);\n      const {\n        top,\n        left\n      } = this.calculateSubmenuMenuLayout(new Dimension(window.innerWidth, window.innerHeight), Dimension.lift(viewBox), entryBoxUpdated, this.expandDirection);\n      // subtract offsets caused by transform parent\n      this.submenuContainer.style.left = `${left - viewBox.left}px`;\n      this.submenuContainer.style.top = `${top - viewBox.top}px`;\n      this.submenuDisposables.add(addDisposableListener(this.submenuContainer, EventType.KEY_UP, e => {\n        const event = new StandardKeyboardEvent(e);\n        if (event.equals(15 /* KeyCode.LeftArrow */)) {\n          EventHelper.stop(e, true);\n          this.parentData.parent.focus();\n          this.cleanupExistingSubmenu(true);\n        }\n      }));\n      this.submenuDisposables.add(addDisposableListener(this.submenuContainer, EventType.KEY_DOWN, e => {\n        const event = new StandardKeyboardEvent(e);\n        if (event.equals(15 /* KeyCode.LeftArrow */)) {\n          EventHelper.stop(e, true);\n        }\n      }));\n      this.submenuDisposables.add(this.parentData.submenu.onDidCancel(() => {\n        this.parentData.parent.focus();\n        this.cleanupExistingSubmenu(true);\n      }));\n      this.parentData.submenu.focus(selectFirstItem);\n      this.mysubmenu = this.parentData.submenu;\n    } else {\n      this.parentData.submenu.focus(false);\n    }\n  }\n  updateAriaExpanded(value) {\n    if (this.item) {\n      this.item?.setAttribute('aria-expanded', value);\n    }\n  }\n  applyStyle() {\n    super.applyStyle();\n    const isSelected = this.element && this.element.classList.contains('focused');\n    const fgColor = isSelected && this.menuStyle.selectionForegroundColor ? this.menuStyle.selectionForegroundColor : this.menuStyle.foregroundColor;\n    if (this.submenuIndicator) {\n      this.submenuIndicator.style.color = fgColor ?? '';\n    }\n  }\n  dispose() {\n    super.dispose();\n    this.hideScheduler.dispose();\n    if (this.mysubmenu) {\n      this.mysubmenu.dispose();\n      this.mysubmenu = null;\n    }\n    if (this.submenuContainer) {\n      this.submenuContainer = undefined;\n    }\n  }\n}\nclass MenuSeparatorActionViewItem extends ActionViewItem {\n  constructor(context, action, options, menuStyles) {\n    super(context, action, options);\n    this.menuStyles = menuStyles;\n  }\n  render(container) {\n    super.render(container);\n    if (this.label) {\n      this.label.style.borderBottomColor = this.menuStyles.separatorColor ? `${this.menuStyles.separatorColor}` : '';\n    }\n  }\n}\nexport function cleanMnemonic(label) {\n  const regex = MENU_MNEMONIC_REGEX;\n  const matches = regex.exec(label);\n  if (!matches) {\n    return label;\n  }\n  const mnemonicInText = !matches[1];\n  return label.replace(regex, mnemonicInText ? '$2$3' : '').trim();\n}\nexport function formatRule(c) {\n  const fontCharacter = getCodiconFontCharacters()[c.id];\n  return `.codicon-${c.id}:before { content: '\\\\${fontCharacter.toString(16)}'; }`;\n}\nfunction getMenuWidgetCSS(style, isForShadowDom) {\n  let result = /* css */`\n.monaco-menu {\n\tfont-size: 13px;\n\tborder-radius: 5px;\n\tmin-width: 160px;\n}\n\n${formatRule(Codicon.menuSelection)}\n${formatRule(Codicon.menuSubmenu)}\n\n.monaco-menu .monaco-action-bar {\n\ttext-align: right;\n\toverflow: hidden;\n\twhite-space: nowrap;\n}\n\n.monaco-menu .monaco-action-bar .actions-container {\n\tdisplay: flex;\n\tmargin: 0 auto;\n\tpadding: 0;\n\twidth: 100%;\n\tjustify-content: flex-end;\n}\n\n.monaco-menu .monaco-action-bar.vertical .actions-container {\n\tdisplay: inline-block;\n}\n\n.monaco-menu .monaco-action-bar.reverse .actions-container {\n\tflex-direction: row-reverse;\n}\n\n.monaco-menu .monaco-action-bar .action-item {\n\tcursor: pointer;\n\tdisplay: inline-block;\n\ttransition: transform 50ms ease;\n\tposition: relative;  /* DO NOT REMOVE - this is the key to preventing the ghosting icon bug in Chrome 42 */\n}\n\n.monaco-menu .monaco-action-bar .action-item.disabled {\n\tcursor: default;\n}\n\n.monaco-menu .monaco-action-bar .action-item .icon,\n.monaco-menu .monaco-action-bar .action-item .codicon {\n\tdisplay: inline-block;\n}\n\n.monaco-menu .monaco-action-bar .action-item .codicon {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.monaco-menu .monaco-action-bar .action-label {\n\tfont-size: 11px;\n\tmargin-right: 4px;\n}\n\n.monaco-menu .monaco-action-bar .action-item.disabled .action-label,\n.monaco-menu .monaco-action-bar .action-item.disabled .action-label:hover {\n\tcolor: var(--vscode-disabledForeground);\n}\n\n/* Vertical actions */\n\n.monaco-menu .monaco-action-bar.vertical {\n\ttext-align: left;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item {\n\tdisplay: block;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label.separator {\n\tdisplay: block;\n\tborder-bottom: 1px solid var(--vscode-menu-separatorBackground);\n\tpadding-top: 1px;\n\tpadding: 30px;\n}\n\n.monaco-menu .secondary-actions .monaco-action-bar .action-label {\n\tmargin-left: 6px;\n}\n\n/* Action Items */\n.monaco-menu .monaco-action-bar .action-item.select-container {\n\toverflow: hidden; /* somehow the dropdown overflows its container, we prevent it here to not push */\n\tflex: 1;\n\tmax-width: 170px;\n\tmin-width: 60px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 10px;\n}\n\n.monaco-menu .monaco-action-bar.vertical {\n\tmargin-left: 0;\n\toverflow: visible;\n}\n\n.monaco-menu .monaco-action-bar.vertical .actions-container {\n\tdisplay: block;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item {\n\tpadding: 0;\n\ttransform: none;\n\tdisplay: flex;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item.active {\n\ttransform: none;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-menu-item {\n\tflex: 1 1 auto;\n\tdisplay: flex;\n\theight: 2em;\n\talign-items: center;\n\tposition: relative;\n\tmargin: 0 4px;\n\tborder-radius: 4px;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-menu-item:hover .keybinding,\n.monaco-menu .monaco-action-bar.vertical .action-menu-item:focus .keybinding {\n\topacity: unset;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label {\n\tflex: 1 1 auto;\n\ttext-decoration: none;\n\tpadding: 0 1em;\n\tbackground: none;\n\tfont-size: 12px;\n\tline-height: 1;\n}\n\n.monaco-menu .monaco-action-bar.vertical .keybinding,\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator {\n\tdisplay: inline-block;\n\tflex: 2 1 auto;\n\tpadding: 0 1em;\n\ttext-align: right;\n\tfont-size: 12px;\n\tline-height: 1;\n}\n\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator {\n\theight: 100%;\n}\n\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator.codicon {\n\tfont-size: 16px !important;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator.codicon::before {\n\tmargin-left: auto;\n\tmargin-right: -20px;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item.disabled .keybinding,\n.monaco-menu .monaco-action-bar.vertical .action-item.disabled .submenu-indicator {\n\topacity: 0.4;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label:not(.separator) {\n\tdisplay: inline-block;\n\tbox-sizing: border-box;\n\tmargin: 0;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item {\n\tposition: static;\n\toverflow: visible;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item .monaco-submenu {\n\tposition: absolute;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label.separator {\n\twidth: 100%;\n\theight: 0px !important;\n\topacity: 1;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label.separator.text {\n\tpadding: 0.7em 1em 0.1em 1em;\n\tfont-weight: bold;\n\topacity: 1;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label:hover {\n\tcolor: inherit;\n}\n\n.monaco-menu .monaco-action-bar.vertical .menu-item-check {\n\tposition: absolute;\n\tvisibility: hidden;\n\twidth: 1em;\n\theight: 100%;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-menu-item.checked .menu-item-check {\n\tvisibility: visible;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n/* Context Menu */\n\n.context-view.monaco-menu-container {\n\toutline: 0;\n\tborder: none;\n\tanimation: fadeIn 0.083s linear;\n\t-webkit-app-region: no-drag;\n}\n\n.context-view.monaco-menu-container :focus,\n.context-view.monaco-menu-container .monaco-action-bar.vertical:focus,\n.context-view.monaco-menu-container .monaco-action-bar.vertical :focus {\n\toutline: 0;\n}\n\n.hc-black .context-view.monaco-menu-container,\n.hc-light .context-view.monaco-menu-container,\n:host-context(.hc-black) .context-view.monaco-menu-container,\n:host-context(.hc-light) .context-view.monaco-menu-container {\n\tbox-shadow: none;\n}\n\n.hc-black .monaco-menu .monaco-action-bar.vertical .action-item.focused,\n.hc-light .monaco-menu .monaco-action-bar.vertical .action-item.focused,\n:host-context(.hc-black) .monaco-menu .monaco-action-bar.vertical .action-item.focused,\n:host-context(.hc-light) .monaco-menu .monaco-action-bar.vertical .action-item.focused {\n\tbackground: none;\n}\n\n/* Vertical Action Bar Styles */\n\n.monaco-menu .monaco-action-bar.vertical {\n\tpadding: 4px 0;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-menu-item {\n\theight: 2em;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label:not(.separator),\n.monaco-menu .monaco-action-bar.vertical .keybinding {\n\tfont-size: inherit;\n\tpadding: 0 2em;\n\tmax-height: 100%;\n}\n\n.monaco-menu .monaco-action-bar.vertical .menu-item-check {\n\tfont-size: inherit;\n\twidth: 2em;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label.separator {\n\tfont-size: inherit;\n\tmargin: 5px 0 !important;\n\tpadding: 0;\n\tborder-radius: 0;\n}\n\n.linux .monaco-menu .monaco-action-bar.vertical .action-label.separator,\n:host-context(.linux) .monaco-menu .monaco-action-bar.vertical .action-label.separator {\n\tmargin-left: 0;\n\tmargin-right: 0;\n}\n\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator {\n\tfont-size: 60%;\n\tpadding: 0 1.8em;\n}\n\n.linux .monaco-menu .monaco-action-bar.vertical .submenu-indicator,\n:host-context(.linux) .monaco-menu .monaco-action-bar.vertical .submenu-indicator {\n\theight: 100%;\n\tmask-size: 10px 10px;\n\t-webkit-mask-size: 10px 10px;\n}\n\n.monaco-menu .action-item {\n\tcursor: default;\n}`;\n  if (isForShadowDom) {\n    // Only define scrollbar styles when used inside shadow dom,\n    // otherwise leave their styling to the global workbench styling.\n    result += `\n\t\t\t/* Arrows */\n\t\t\t.monaco-scrollable-element > .scrollbar > .scra {\n\t\t\t\tcursor: pointer;\n\t\t\t\tfont-size: 11px !important;\n\t\t\t}\n\n\t\t\t.monaco-scrollable-element > .visible {\n\t\t\t\topacity: 1;\n\n\t\t\t\t/* Background rule added for IE9 - to allow clicks on dom node */\n\t\t\t\tbackground:rgba(0,0,0,0);\n\n\t\t\t\ttransition: opacity 100ms linear;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .invisible {\n\t\t\t\topacity: 0;\n\t\t\t\tpointer-events: none;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .invisible.fade {\n\t\t\t\ttransition: opacity 800ms linear;\n\t\t\t}\n\n\t\t\t/* Scrollable Content Inset Shadow */\n\t\t\t.monaco-scrollable-element > .shadow {\n\t\t\t\tposition: absolute;\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .shadow.top {\n\t\t\t\tdisplay: block;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 3px;\n\t\t\t\theight: 3px;\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .shadow.left {\n\t\t\t\tdisplay: block;\n\t\t\t\ttop: 3px;\n\t\t\t\tleft: 0;\n\t\t\t\theight: 100%;\n\t\t\t\twidth: 3px;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .shadow.top-left-corner {\n\t\t\t\tdisplay: block;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\theight: 3px;\n\t\t\t\twidth: 3px;\n\t\t\t}\n\t\t`;\n    // Scrollbars\n    const scrollbarShadowColor = style.scrollbarShadow;\n    if (scrollbarShadowColor) {\n      result += `\n\t\t\t\t.monaco-scrollable-element > .shadow.top {\n\t\t\t\t\tbox-shadow: ${scrollbarShadowColor} 0 6px 6px -6px inset;\n\t\t\t\t}\n\n\t\t\t\t.monaco-scrollable-element > .shadow.left {\n\t\t\t\t\tbox-shadow: ${scrollbarShadowColor} 6px 0 6px -6px inset;\n\t\t\t\t}\n\n\t\t\t\t.monaco-scrollable-element > .shadow.top.left {\n\t\t\t\t\tbox-shadow: ${scrollbarShadowColor} 6px 6px 6px -6px inset;\n\t\t\t\t}\n\t\t\t`;\n    }\n    const scrollbarSliderBackgroundColor = style.scrollbarSliderBackground;\n    if (scrollbarSliderBackgroundColor) {\n      result += `\n\t\t\t\t.monaco-scrollable-element > .scrollbar > .slider {\n\t\t\t\t\tbackground: ${scrollbarSliderBackgroundColor};\n\t\t\t\t}\n\t\t\t`;\n    }\n    const scrollbarSliderHoverBackgroundColor = style.scrollbarSliderHoverBackground;\n    if (scrollbarSliderHoverBackgroundColor) {\n      result += `\n\t\t\t\t.monaco-scrollable-element > .scrollbar > .slider:hover {\n\t\t\t\t\tbackground: ${scrollbarSliderHoverBackgroundColor};\n\t\t\t\t}\n\t\t\t`;\n    }\n    const scrollbarSliderActiveBackgroundColor = style.scrollbarSliderActiveBackground;\n    if (scrollbarSliderActiveBackgroundColor) {\n      result += `\n\t\t\t\t.monaco-scrollable-element > .scrollbar > .slider.active {\n\t\t\t\t\tbackground: ${scrollbarSliderActiveBackgroundColor};\n\t\t\t\t}\n\t\t\t`;\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["isFirefox", "EventType", "TouchEventType", "Gesture", "$", "addDisposableListener", "append", "clearNode", "createStyleSheet", "Dimension", "EventHelper", "getActiveElement", "getWindow", "isAncestor", "isInShadowDOM", "StandardKeyboardEvent", "StandardMouseEvent", "ActionBar", "ActionViewItem", "BaseActionViewItem", "layout", "DomScrollableElement", "EmptySubmenuAction", "Separator", "SubmenuAction", "RunOnceScheduler", "Codicon", "getCodiconFontCharacters", "ThemeIcon", "stripIcons", "DisposableStore", "isLinux", "isMacintosh", "strings", "MENU_MNEMONIC_REGEX", "MENU_ESCAPED_MNEMONIC_REGEX", "HorizontalDirection", "VerticalDirection", "<PERSON><PERSON>", "constructor", "container", "actions", "options", "menuStyles", "classList", "add", "setAttribute", "menuElement", "document", "createElement", "orientation", "actionViewItemProvider", "action", "doGetActionViewItem", "parentData", "context", "actionRunner", "aria<PERSON><PERSON><PERSON>", "ariaRole", "focusOnlyEnabledItems", "triggerKeys", "keys", "keyDown", "actionsList", "tabIndex", "initializeOrUpdateStyleSheet", "_register", "addTarget", "KEY_DOWN", "e", "event", "equals", "preventDefault", "enableMnemonics", "key", "toLocaleLowerCase", "mnemonics", "has", "stop", "get", "length", "SubmenuMenuActionViewItem", "focusItemByElement", "onClick", "shift", "push", "set", "focusedItem", "viewItems", "focusNext", "focusPrevious", "domNode", "MOUSE_OUT", "relatedTarget", "undefined", "updateFocus", "stopPropagation", "MOUSE_OVER", "target", "parentElement", "contains", "lastFocusedItem", "setFocusedItem", "Tap", "initialTarget", "parent", "Map", "scrollableElement", "alwaysConsumeMouseWheel", "horizontal", "vertical", "verticalScrollbarSize", "handleMouseWheel", "useShadows", "scrollElement", "getDomNode", "style", "position", "styleScrollElement", "Change", "scrollTop", "getScrollPosition", "setScrollPosition", "translationY", "MOUSE_UP", "window", "maxHeight", "Math", "max", "innerHeight", "getBoundingClientRect", "top", "filter", "a", "idx", "submenuIds", "id", "console", "warn", "prevAction", "icon", "label", "isMenu", "append<PERSON><PERSON><PERSON>", "scanDomNode", "item", "MenuSeparatorActionViewItem", "for<PERSON>ach", "index", "array", "updatePositionInSet", "styleSheet", "globalStyleSheet", "textContent", "getMenuWidgetCSS", "fgColor", "foregroundColor", "bgColor", "backgroundColor", "border", "borderColor", "borderRadius", "shadow", "shadowColor", "outline", "color", "boxShadow", "getContainer", "onScroll", "element", "i", "children", "elem", "fromRight", "round", "menuActionViewItem", "Set", "mnemonic", "getMnemonic", "isEnabled", "actionViewItems", "menuItemOptions", "useEventAsContext", "getKeyBinding", "keybinding", "keybinding<PERSON>abel", "get<PERSON><PERSON><PERSON>", "BaseMenuActionViewItem", "ctx", "menuStyle", "cssClass", "matches", "exec", "runOnceToEnableMouseUp", "mouseEvent", "rightB<PERSON>on", "setTimeout", "CONTEXT_MENU", "render", "_action", "ID", "check", "asCSSSelector", "menuSelection", "schedule", "updateClass", "updateLabel", "updateTooltip", "updateEnabled", "updateChecked", "applyStyle", "blur", "focus", "pos", "setSize", "cleanLabel", "cleanMnemonic", "replace", "escape", "lastIndex", "escMatch", "replaceDoubleEscapes", "str", "ltrim", "substr", "rtrim", "innerText", "trim", "remove", "split", "class", "enabled", "removeAttribute", "checked", "toggle", "isSelected", "selectionForegroundColor", "selectionBackgroundColor", "selectionBorderColor", "outlineOffset", "submenuActions", "submenuOptions", "mysubmenu", "submenuDisposables", "mouseOver", "expandDirection", "Right", "Below", "showScheduler", "cleanupExistingSubmenu", "createSubmenu", "hideScheduler", "submenu", "updateAriaExpanded", "submenuIndicator", "menuSubmenu", "KEY_UP", "MOUSE_LEAVE", "FOCUS_OUT", "force", "dispose", "submenuContainer", "clear", "calculateSubmenuMenuLayout", "windowDimensions", "entry", "ret", "left", "width", "offset", "size", "height", "selectFirstItem", "computedStyles", "getComputedStyle", "paddingTop", "parseFloat", "zIndex", "entryBox", "entryBoxUpdated", "viewBox", "innerWidth", "lift", "onDidCancel", "value", "borderBottomColor", "separatorColor", "regex", "mnemonicInText", "formatRule", "c", "fontCharacter", "toString", "isForShadowDom", "result", "scrollbarShadowColor", "scrollbarShadow", "scrollbarSliderBackgroundColor", "scrollbarSliderBackground", "scrollbarSliderHoverBackgroundColor", "scrollbarSliderHoverBackground", "scrollbarSliderActiveBackgroundColor", "scrollbarSliderActiveBackground"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/menu/menu.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { isFirefox } from '../../browser.js';\nimport { EventType as TouchEventType, Gesture } from '../../touch.js';\nimport { $, addDisposableListener, append, clearNode, createStyleSheet, Dimension, EventHelper, EventType, getActiveElement, getWindow, isAncestor, isInShadowDOM } from '../../dom.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { StandardMouseEvent } from '../../mouseEvent.js';\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport { ActionViewItem, BaseActionViewItem } from '../actionbar/actionViewItems.js';\nimport { layout } from '../contextview/contextview.js';\nimport { DomScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { EmptySubmenuAction, Separator, SubmenuAction } from '../../../common/actions.js';\nimport { RunOnceScheduler } from '../../../common/async.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { getCodiconFontCharacters } from '../../../common/codiconsUtil.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { stripIcons } from '../../../common/iconLabels.js';\nimport { DisposableStore } from '../../../common/lifecycle.js';\nimport { isLinux, isMacintosh } from '../../../common/platform.js';\nimport * as strings from '../../../common/strings.js';\nexport const MENU_MNEMONIC_REGEX = /\\(&([^\\s&])\\)|(^|[^&])&([^\\s&])/;\nexport const MENU_ESCAPED_MNEMONIC_REGEX = /(&amp;)?(&amp;)([^\\s&])/g;\nexport var HorizontalDirection;\n(function (HorizontalDirection) {\n    HorizontalDirection[HorizontalDirection[\"Right\"] = 0] = \"Right\";\n    HorizontalDirection[HorizontalDirection[\"Left\"] = 1] = \"Left\";\n})(HorizontalDirection || (HorizontalDirection = {}));\nexport var VerticalDirection;\n(function (VerticalDirection) {\n    VerticalDirection[VerticalDirection[\"Above\"] = 0] = \"Above\";\n    VerticalDirection[VerticalDirection[\"Below\"] = 1] = \"Below\";\n})(VerticalDirection || (VerticalDirection = {}));\nexport class Menu extends ActionBar {\n    constructor(container, actions, options, menuStyles) {\n        container.classList.add('monaco-menu-container');\n        container.setAttribute('role', 'presentation');\n        const menuElement = document.createElement('div');\n        menuElement.classList.add('monaco-menu');\n        menuElement.setAttribute('role', 'presentation');\n        super(menuElement, {\n            orientation: 1 /* ActionsOrientation.VERTICAL */,\n            actionViewItemProvider: action => this.doGetActionViewItem(action, options, parentData),\n            context: options.context,\n            actionRunner: options.actionRunner,\n            ariaLabel: options.ariaLabel,\n            ariaRole: 'menu',\n            focusOnlyEnabledItems: true,\n            triggerKeys: { keys: [3 /* KeyCode.Enter */, ...(isMacintosh || isLinux ? [10 /* KeyCode.Space */] : [])], keyDown: true }\n        });\n        this.menuStyles = menuStyles;\n        this.menuElement = menuElement;\n        this.actionsList.tabIndex = 0;\n        this.initializeOrUpdateStyleSheet(container, menuStyles);\n        this._register(Gesture.addTarget(menuElement));\n        this._register(addDisposableListener(menuElement, EventType.KEY_DOWN, (e) => {\n            const event = new StandardKeyboardEvent(e);\n            // Stop tab navigation of menus\n            if (event.equals(2 /* KeyCode.Tab */)) {\n                e.preventDefault();\n            }\n        }));\n        if (options.enableMnemonics) {\n            this._register(addDisposableListener(menuElement, EventType.KEY_DOWN, (e) => {\n                const key = e.key.toLocaleLowerCase();\n                if (this.mnemonics.has(key)) {\n                    EventHelper.stop(e, true);\n                    const actions = this.mnemonics.get(key);\n                    if (actions.length === 1) {\n                        if (actions[0] instanceof SubmenuMenuActionViewItem && actions[0].container) {\n                            this.focusItemByElement(actions[0].container);\n                        }\n                        actions[0].onClick(e);\n                    }\n                    if (actions.length > 1) {\n                        const action = actions.shift();\n                        if (action && action.container) {\n                            this.focusItemByElement(action.container);\n                            actions.push(action);\n                        }\n                        this.mnemonics.set(key, actions);\n                    }\n                }\n            }));\n        }\n        if (isLinux) {\n            this._register(addDisposableListener(menuElement, EventType.KEY_DOWN, e => {\n                const event = new StandardKeyboardEvent(e);\n                if (event.equals(14 /* KeyCode.Home */) || event.equals(11 /* KeyCode.PageUp */)) {\n                    this.focusedItem = this.viewItems.length - 1;\n                    this.focusNext();\n                    EventHelper.stop(e, true);\n                }\n                else if (event.equals(13 /* KeyCode.End */) || event.equals(12 /* KeyCode.PageDown */)) {\n                    this.focusedItem = 0;\n                    this.focusPrevious();\n                    EventHelper.stop(e, true);\n                }\n            }));\n        }\n        this._register(addDisposableListener(this.domNode, EventType.MOUSE_OUT, e => {\n            const relatedTarget = e.relatedTarget;\n            if (!isAncestor(relatedTarget, this.domNode)) {\n                this.focusedItem = undefined;\n                this.updateFocus();\n                e.stopPropagation();\n            }\n        }));\n        this._register(addDisposableListener(this.actionsList, EventType.MOUSE_OVER, e => {\n            let target = e.target;\n            if (!target || !isAncestor(target, this.actionsList) || target === this.actionsList) {\n                return;\n            }\n            while (target.parentElement !== this.actionsList && target.parentElement !== null) {\n                target = target.parentElement;\n            }\n            if (target.classList.contains('action-item')) {\n                const lastFocusedItem = this.focusedItem;\n                this.setFocusedItem(target);\n                if (lastFocusedItem !== this.focusedItem) {\n                    this.updateFocus();\n                }\n            }\n        }));\n        // Support touch on actions list to focus items (needed for submenus)\n        this._register(Gesture.addTarget(this.actionsList));\n        this._register(addDisposableListener(this.actionsList, TouchEventType.Tap, e => {\n            let target = e.initialTarget;\n            if (!target || !isAncestor(target, this.actionsList) || target === this.actionsList) {\n                return;\n            }\n            while (target.parentElement !== this.actionsList && target.parentElement !== null) {\n                target = target.parentElement;\n            }\n            if (target.classList.contains('action-item')) {\n                const lastFocusedItem = this.focusedItem;\n                this.setFocusedItem(target);\n                if (lastFocusedItem !== this.focusedItem) {\n                    this.updateFocus();\n                }\n            }\n        }));\n        const parentData = {\n            parent: this\n        };\n        this.mnemonics = new Map();\n        // Scroll Logic\n        this.scrollableElement = this._register(new DomScrollableElement(menuElement, {\n            alwaysConsumeMouseWheel: true,\n            horizontal: 2 /* ScrollbarVisibility.Hidden */,\n            vertical: 3 /* ScrollbarVisibility.Visible */,\n            verticalScrollbarSize: 7,\n            handleMouseWheel: true,\n            useShadows: true\n        }));\n        const scrollElement = this.scrollableElement.getDomNode();\n        scrollElement.style.position = '';\n        this.styleScrollElement(scrollElement, menuStyles);\n        // Support scroll on menu drag\n        this._register(addDisposableListener(menuElement, TouchEventType.Change, e => {\n            EventHelper.stop(e, true);\n            const scrollTop = this.scrollableElement.getScrollPosition().scrollTop;\n            this.scrollableElement.setScrollPosition({ scrollTop: scrollTop - e.translationY });\n        }));\n        this._register(addDisposableListener(scrollElement, EventType.MOUSE_UP, e => {\n            // Absorb clicks in menu dead space https://github.com/microsoft/vscode/issues/63575\n            // We do this on the scroll element so the scroll bar doesn't dismiss the menu either\n            e.preventDefault();\n        }));\n        const window = getWindow(container);\n        menuElement.style.maxHeight = `${Math.max(10, window.innerHeight - container.getBoundingClientRect().top - 35)}px`;\n        actions = actions.filter((a, idx) => {\n            if (options.submenuIds?.has(a.id)) {\n                console.warn(`Found submenu cycle: ${a.id}`);\n                return false;\n            }\n            // Filter out consecutive or useless separators\n            if (a instanceof Separator) {\n                if (idx === actions.length - 1 || idx === 0) {\n                    return false;\n                }\n                const prevAction = actions[idx - 1];\n                if (prevAction instanceof Separator) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        this.push(actions, { icon: true, label: true, isMenu: true });\n        container.appendChild(this.scrollableElement.getDomNode());\n        this.scrollableElement.scanDomNode();\n        this.viewItems.filter(item => !(item instanceof MenuSeparatorActionViewItem)).forEach((item, index, array) => {\n            item.updatePositionInSet(index + 1, array.length);\n        });\n    }\n    initializeOrUpdateStyleSheet(container, style) {\n        if (!this.styleSheet) {\n            if (isInShadowDOM(container)) {\n                this.styleSheet = createStyleSheet(container);\n            }\n            else {\n                if (!Menu.globalStyleSheet) {\n                    Menu.globalStyleSheet = createStyleSheet();\n                }\n                this.styleSheet = Menu.globalStyleSheet;\n            }\n        }\n        this.styleSheet.textContent = getMenuWidgetCSS(style, isInShadowDOM(container));\n    }\n    styleScrollElement(scrollElement, style) {\n        const fgColor = style.foregroundColor ?? '';\n        const bgColor = style.backgroundColor ?? '';\n        const border = style.borderColor ? `1px solid ${style.borderColor}` : '';\n        const borderRadius = '5px';\n        const shadow = style.shadowColor ? `0 2px 8px ${style.shadowColor}` : '';\n        scrollElement.style.outline = border;\n        scrollElement.style.borderRadius = borderRadius;\n        scrollElement.style.color = fgColor;\n        scrollElement.style.backgroundColor = bgColor;\n        scrollElement.style.boxShadow = shadow;\n    }\n    getContainer() {\n        return this.scrollableElement.getDomNode();\n    }\n    get onScroll() {\n        return this.scrollableElement.onScroll;\n    }\n    focusItemByElement(element) {\n        const lastFocusedItem = this.focusedItem;\n        this.setFocusedItem(element);\n        if (lastFocusedItem !== this.focusedItem) {\n            this.updateFocus();\n        }\n    }\n    setFocusedItem(element) {\n        for (let i = 0; i < this.actionsList.children.length; i++) {\n            const elem = this.actionsList.children[i];\n            if (element === elem) {\n                this.focusedItem = i;\n                break;\n            }\n        }\n    }\n    updateFocus(fromRight) {\n        super.updateFocus(fromRight, true, true);\n        if (typeof this.focusedItem !== 'undefined') {\n            // Workaround for #80047 caused by an issue in chromium\n            // https://bugs.chromium.org/p/chromium/issues/detail?id=414283\n            // When that's fixed, just call this.scrollableElement.scanDomNode()\n            this.scrollableElement.setScrollPosition({\n                scrollTop: Math.round(this.menuElement.scrollTop)\n            });\n        }\n    }\n    doGetActionViewItem(action, options, parentData) {\n        if (action instanceof Separator) {\n            return new MenuSeparatorActionViewItem(options.context, action, { icon: true }, this.menuStyles);\n        }\n        else if (action instanceof SubmenuAction) {\n            const menuActionViewItem = new SubmenuMenuActionViewItem(action, action.actions, parentData, { ...options, submenuIds: new Set([...(options.submenuIds || []), action.id]) }, this.menuStyles);\n            if (options.enableMnemonics) {\n                const mnemonic = menuActionViewItem.getMnemonic();\n                if (mnemonic && menuActionViewItem.isEnabled()) {\n                    let actionViewItems = [];\n                    if (this.mnemonics.has(mnemonic)) {\n                        actionViewItems = this.mnemonics.get(mnemonic);\n                    }\n                    actionViewItems.push(menuActionViewItem);\n                    this.mnemonics.set(mnemonic, actionViewItems);\n                }\n            }\n            return menuActionViewItem;\n        }\n        else {\n            const menuItemOptions = { enableMnemonics: options.enableMnemonics, useEventAsContext: options.useEventAsContext };\n            if (options.getKeyBinding) {\n                const keybinding = options.getKeyBinding(action);\n                if (keybinding) {\n                    const keybindingLabel = keybinding.getLabel();\n                    if (keybindingLabel) {\n                        menuItemOptions.keybinding = keybindingLabel;\n                    }\n                }\n            }\n            const menuActionViewItem = new BaseMenuActionViewItem(options.context, action, menuItemOptions, this.menuStyles);\n            if (options.enableMnemonics) {\n                const mnemonic = menuActionViewItem.getMnemonic();\n                if (mnemonic && menuActionViewItem.isEnabled()) {\n                    let actionViewItems = [];\n                    if (this.mnemonics.has(mnemonic)) {\n                        actionViewItems = this.mnemonics.get(mnemonic);\n                    }\n                    actionViewItems.push(menuActionViewItem);\n                    this.mnemonics.set(mnemonic, actionViewItems);\n                }\n            }\n            return menuActionViewItem;\n        }\n    }\n}\nclass BaseMenuActionViewItem extends BaseActionViewItem {\n    constructor(ctx, action, options, menuStyle) {\n        options.isMenu = true;\n        super(action, action, options);\n        this.menuStyle = menuStyle;\n        this.options = options;\n        this.options.icon = options.icon !== undefined ? options.icon : false;\n        this.options.label = options.label !== undefined ? options.label : true;\n        this.cssClass = '';\n        // Set mnemonic\n        if (this.options.label && options.enableMnemonics) {\n            const label = this.action.label;\n            if (label) {\n                const matches = MENU_MNEMONIC_REGEX.exec(label);\n                if (matches) {\n                    this.mnemonic = (!!matches[1] ? matches[1] : matches[3]).toLocaleLowerCase();\n                }\n            }\n        }\n        // Add mouse up listener later to avoid accidental clicks\n        this.runOnceToEnableMouseUp = new RunOnceScheduler(() => {\n            if (!this.element) {\n                return;\n            }\n            this._register(addDisposableListener(this.element, EventType.MOUSE_UP, e => {\n                // removed default prevention as it conflicts\n                // with BaseActionViewItem #101537\n                // add back if issues arise and link new issue\n                EventHelper.stop(e, true);\n                // See https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Interact_with_the_clipboard\n                // > Writing to the clipboard\n                // > You can use the \"cut\" and \"copy\" commands without any special\n                // permission if you are using them in a short-lived event handler\n                // for a user action (for example, a click handler).\n                // => to get the Copy and Paste context menu actions working on Firefox,\n                // there should be no timeout here\n                if (isFirefox) {\n                    const mouseEvent = new StandardMouseEvent(getWindow(this.element), e);\n                    // Allowing right click to trigger the event causes the issue described below,\n                    // but since the solution below does not work in FF, we must disable right click\n                    if (mouseEvent.rightButton) {\n                        return;\n                    }\n                    this.onClick(e);\n                }\n                // In all other cases, set timeout to allow context menu cancellation to trigger\n                // otherwise the action will destroy the menu and a second context menu\n                // will still trigger for right click.\n                else {\n                    setTimeout(() => {\n                        this.onClick(e);\n                    }, 0);\n                }\n            }));\n            this._register(addDisposableListener(this.element, EventType.CONTEXT_MENU, e => {\n                EventHelper.stop(e, true);\n            }));\n        }, 100);\n        this._register(this.runOnceToEnableMouseUp);\n    }\n    render(container) {\n        super.render(container);\n        if (!this.element) {\n            return;\n        }\n        this.container = container;\n        this.item = append(this.element, $('a.action-menu-item'));\n        if (this._action.id === Separator.ID) {\n            // A separator is a presentation item\n            this.item.setAttribute('role', 'presentation');\n        }\n        else {\n            this.item.setAttribute('role', 'menuitem');\n            if (this.mnemonic) {\n                this.item.setAttribute('aria-keyshortcuts', `${this.mnemonic}`);\n            }\n        }\n        this.check = append(this.item, $('span.menu-item-check' + ThemeIcon.asCSSSelector(Codicon.menuSelection)));\n        this.check.setAttribute('role', 'none');\n        this.label = append(this.item, $('span.action-label'));\n        if (this.options.label && this.options.keybinding) {\n            append(this.item, $('span.keybinding')).textContent = this.options.keybinding;\n        }\n        // Adds mouse up listener to actually run the action\n        this.runOnceToEnableMouseUp.schedule();\n        this.updateClass();\n        this.updateLabel();\n        this.updateTooltip();\n        this.updateEnabled();\n        this.updateChecked();\n        this.applyStyle();\n    }\n    blur() {\n        super.blur();\n        this.applyStyle();\n    }\n    focus() {\n        super.focus();\n        this.item?.focus();\n        this.applyStyle();\n    }\n    updatePositionInSet(pos, setSize) {\n        if (this.item) {\n            this.item.setAttribute('aria-posinset', `${pos}`);\n            this.item.setAttribute('aria-setsize', `${setSize}`);\n        }\n    }\n    updateLabel() {\n        if (!this.label) {\n            return;\n        }\n        if (this.options.label) {\n            clearNode(this.label);\n            let label = stripIcons(this.action.label);\n            if (label) {\n                const cleanLabel = cleanMnemonic(label);\n                if (!this.options.enableMnemonics) {\n                    label = cleanLabel;\n                }\n                this.label.setAttribute('aria-label', cleanLabel.replace(/&&/g, '&'));\n                const matches = MENU_MNEMONIC_REGEX.exec(label);\n                if (matches) {\n                    label = strings.escape(label);\n                    // This is global, reset it\n                    MENU_ESCAPED_MNEMONIC_REGEX.lastIndex = 0;\n                    let escMatch = MENU_ESCAPED_MNEMONIC_REGEX.exec(label);\n                    // We can't use negative lookbehind so if we match our negative and skip\n                    while (escMatch && escMatch[1]) {\n                        escMatch = MENU_ESCAPED_MNEMONIC_REGEX.exec(label);\n                    }\n                    const replaceDoubleEscapes = (str) => str.replace(/&amp;&amp;/g, '&amp;');\n                    if (escMatch) {\n                        this.label.append(strings.ltrim(replaceDoubleEscapes(label.substr(0, escMatch.index)), ' '), $('u', { 'aria-hidden': 'true' }, escMatch[3]), strings.rtrim(replaceDoubleEscapes(label.substr(escMatch.index + escMatch[0].length)), ' '));\n                    }\n                    else {\n                        this.label.innerText = replaceDoubleEscapes(label).trim();\n                    }\n                    this.item?.setAttribute('aria-keyshortcuts', (!!matches[1] ? matches[1] : matches[3]).toLocaleLowerCase());\n                }\n                else {\n                    this.label.innerText = label.replace(/&&/g, '&').trim();\n                }\n            }\n        }\n    }\n    updateTooltip() {\n        // menus should function like native menus and they do not have tooltips\n    }\n    updateClass() {\n        if (this.cssClass && this.item) {\n            this.item.classList.remove(...this.cssClass.split(' '));\n        }\n        if (this.options.icon && this.label) {\n            this.cssClass = this.action.class || '';\n            this.label.classList.add('icon');\n            if (this.cssClass) {\n                this.label.classList.add(...this.cssClass.split(' '));\n            }\n            this.updateEnabled();\n        }\n        else if (this.label) {\n            this.label.classList.remove('icon');\n        }\n    }\n    updateEnabled() {\n        if (this.action.enabled) {\n            if (this.element) {\n                this.element.classList.remove('disabled');\n                this.element.removeAttribute('aria-disabled');\n            }\n            if (this.item) {\n                this.item.classList.remove('disabled');\n                this.item.removeAttribute('aria-disabled');\n                this.item.tabIndex = 0;\n            }\n        }\n        else {\n            if (this.element) {\n                this.element.classList.add('disabled');\n                this.element.setAttribute('aria-disabled', 'true');\n            }\n            if (this.item) {\n                this.item.classList.add('disabled');\n                this.item.setAttribute('aria-disabled', 'true');\n            }\n        }\n    }\n    updateChecked() {\n        if (!this.item) {\n            return;\n        }\n        const checked = this.action.checked;\n        this.item.classList.toggle('checked', !!checked);\n        if (checked !== undefined) {\n            this.item.setAttribute('role', 'menuitemcheckbox');\n            this.item.setAttribute('aria-checked', checked ? 'true' : 'false');\n        }\n        else {\n            this.item.setAttribute('role', 'menuitem');\n            this.item.setAttribute('aria-checked', '');\n        }\n    }\n    getMnemonic() {\n        return this.mnemonic;\n    }\n    applyStyle() {\n        const isSelected = this.element && this.element.classList.contains('focused');\n        const fgColor = isSelected && this.menuStyle.selectionForegroundColor ? this.menuStyle.selectionForegroundColor : this.menuStyle.foregroundColor;\n        const bgColor = isSelected && this.menuStyle.selectionBackgroundColor ? this.menuStyle.selectionBackgroundColor : undefined;\n        const outline = isSelected && this.menuStyle.selectionBorderColor ? `1px solid ${this.menuStyle.selectionBorderColor}` : '';\n        const outlineOffset = isSelected && this.menuStyle.selectionBorderColor ? `-1px` : '';\n        if (this.item) {\n            this.item.style.color = fgColor ?? '';\n            this.item.style.backgroundColor = bgColor ?? '';\n            this.item.style.outline = outline;\n            this.item.style.outlineOffset = outlineOffset;\n        }\n        if (this.check) {\n            this.check.style.color = fgColor ?? '';\n        }\n    }\n}\nclass SubmenuMenuActionViewItem extends BaseMenuActionViewItem {\n    constructor(action, submenuActions, parentData, submenuOptions, menuStyles) {\n        super(action, action, submenuOptions, menuStyles);\n        this.submenuActions = submenuActions;\n        this.parentData = parentData;\n        this.submenuOptions = submenuOptions;\n        this.mysubmenu = null;\n        this.submenuDisposables = this._register(new DisposableStore());\n        this.mouseOver = false;\n        this.expandDirection = submenuOptions && submenuOptions.expandDirection !== undefined ? submenuOptions.expandDirection : { horizontal: HorizontalDirection.Right, vertical: VerticalDirection.Below };\n        this.showScheduler = new RunOnceScheduler(() => {\n            if (this.mouseOver) {\n                this.cleanupExistingSubmenu(false);\n                this.createSubmenu(false);\n            }\n        }, 250);\n        this.hideScheduler = new RunOnceScheduler(() => {\n            if (this.element && (!isAncestor(getActiveElement(), this.element) && this.parentData.submenu === this.mysubmenu)) {\n                this.parentData.parent.focus(false);\n                this.cleanupExistingSubmenu(true);\n            }\n        }, 750);\n    }\n    render(container) {\n        super.render(container);\n        if (!this.element) {\n            return;\n        }\n        if (this.item) {\n            this.item.classList.add('monaco-submenu-item');\n            this.item.tabIndex = 0;\n            this.item.setAttribute('aria-haspopup', 'true');\n            this.updateAriaExpanded('false');\n            this.submenuIndicator = append(this.item, $('span.submenu-indicator' + ThemeIcon.asCSSSelector(Codicon.menuSubmenu)));\n            this.submenuIndicator.setAttribute('aria-hidden', 'true');\n        }\n        this._register(addDisposableListener(this.element, EventType.KEY_UP, e => {\n            const event = new StandardKeyboardEvent(e);\n            if (event.equals(17 /* KeyCode.RightArrow */) || event.equals(3 /* KeyCode.Enter */)) {\n                EventHelper.stop(e, true);\n                this.createSubmenu(true);\n            }\n        }));\n        this._register(addDisposableListener(this.element, EventType.KEY_DOWN, e => {\n            const event = new StandardKeyboardEvent(e);\n            if (getActiveElement() === this.item) {\n                if (event.equals(17 /* KeyCode.RightArrow */) || event.equals(3 /* KeyCode.Enter */)) {\n                    EventHelper.stop(e, true);\n                }\n            }\n        }));\n        this._register(addDisposableListener(this.element, EventType.MOUSE_OVER, e => {\n            if (!this.mouseOver) {\n                this.mouseOver = true;\n                this.showScheduler.schedule();\n            }\n        }));\n        this._register(addDisposableListener(this.element, EventType.MOUSE_LEAVE, e => {\n            this.mouseOver = false;\n        }));\n        this._register(addDisposableListener(this.element, EventType.FOCUS_OUT, e => {\n            if (this.element && !isAncestor(getActiveElement(), this.element)) {\n                this.hideScheduler.schedule();\n            }\n        }));\n        this._register(this.parentData.parent.onScroll(() => {\n            if (this.parentData.submenu === this.mysubmenu) {\n                this.parentData.parent.focus(false);\n                this.cleanupExistingSubmenu(true);\n            }\n        }));\n    }\n    updateEnabled() {\n        // override on submenu entry\n        // native menus do not observe enablement on sumbenus\n        // we mimic that behavior\n    }\n    onClick(e) {\n        // stop clicking from trying to run an action\n        EventHelper.stop(e, true);\n        this.cleanupExistingSubmenu(false);\n        this.createSubmenu(true);\n    }\n    cleanupExistingSubmenu(force) {\n        if (this.parentData.submenu && (force || (this.parentData.submenu !== this.mysubmenu))) {\n            // disposal may throw if the submenu has already been removed\n            try {\n                this.parentData.submenu.dispose();\n            }\n            catch { }\n            this.parentData.submenu = undefined;\n            this.updateAriaExpanded('false');\n            if (this.submenuContainer) {\n                this.submenuDisposables.clear();\n                this.submenuContainer = undefined;\n            }\n        }\n    }\n    calculateSubmenuMenuLayout(windowDimensions, submenu, entry, expandDirection) {\n        const ret = { top: 0, left: 0 };\n        // Start with horizontal\n        ret.left = layout(windowDimensions.width, submenu.width, { position: expandDirection.horizontal === HorizontalDirection.Right ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */, offset: entry.left, size: entry.width });\n        // We don't have enough room to layout the menu fully, so we are overlapping the menu\n        if (ret.left >= entry.left && ret.left < entry.left + entry.width) {\n            if (entry.left + 10 + submenu.width <= windowDimensions.width) {\n                ret.left = entry.left + 10;\n            }\n            entry.top += 10;\n            entry.height = 0;\n        }\n        // Now that we have a horizontal position, try layout vertically\n        ret.top = layout(windowDimensions.height, submenu.height, { position: 0 /* LayoutAnchorPosition.Before */, offset: entry.top, size: 0 });\n        // We didn't have enough room below, but we did above, so we shift down to align the menu\n        if (ret.top + submenu.height === entry.top && ret.top + entry.height + submenu.height <= windowDimensions.height) {\n            ret.top += entry.height;\n        }\n        return ret;\n    }\n    createSubmenu(selectFirstItem = true) {\n        if (!this.element) {\n            return;\n        }\n        if (!this.parentData.submenu) {\n            this.updateAriaExpanded('true');\n            this.submenuContainer = append(this.element, $('div.monaco-submenu'));\n            this.submenuContainer.classList.add('menubar-menu-items-holder', 'context-view');\n            // Set the top value of the menu container before construction\n            // This allows the menu constructor to calculate the proper max height\n            const computedStyles = getWindow(this.parentData.parent.domNode).getComputedStyle(this.parentData.parent.domNode);\n            const paddingTop = parseFloat(computedStyles.paddingTop || '0') || 0;\n            // this.submenuContainer.style.top = `${this.element.offsetTop - this.parentData.parent.scrollOffset - paddingTop}px`;\n            this.submenuContainer.style.zIndex = '1';\n            this.submenuContainer.style.position = 'fixed';\n            this.submenuContainer.style.top = '0';\n            this.submenuContainer.style.left = '0';\n            this.parentData.submenu = new Menu(this.submenuContainer, this.submenuActions.length ? this.submenuActions : [new EmptySubmenuAction()], this.submenuOptions, this.menuStyle);\n            // layout submenu\n            const entryBox = this.element.getBoundingClientRect();\n            const entryBoxUpdated = {\n                top: entryBox.top - paddingTop,\n                left: entryBox.left,\n                height: entryBox.height + 2 * paddingTop,\n                width: entryBox.width\n            };\n            const viewBox = this.submenuContainer.getBoundingClientRect();\n            const window = getWindow(this.element);\n            const { top, left } = this.calculateSubmenuMenuLayout(new Dimension(window.innerWidth, window.innerHeight), Dimension.lift(viewBox), entryBoxUpdated, this.expandDirection);\n            // subtract offsets caused by transform parent\n            this.submenuContainer.style.left = `${left - viewBox.left}px`;\n            this.submenuContainer.style.top = `${top - viewBox.top}px`;\n            this.submenuDisposables.add(addDisposableListener(this.submenuContainer, EventType.KEY_UP, e => {\n                const event = new StandardKeyboardEvent(e);\n                if (event.equals(15 /* KeyCode.LeftArrow */)) {\n                    EventHelper.stop(e, true);\n                    this.parentData.parent.focus();\n                    this.cleanupExistingSubmenu(true);\n                }\n            }));\n            this.submenuDisposables.add(addDisposableListener(this.submenuContainer, EventType.KEY_DOWN, e => {\n                const event = new StandardKeyboardEvent(e);\n                if (event.equals(15 /* KeyCode.LeftArrow */)) {\n                    EventHelper.stop(e, true);\n                }\n            }));\n            this.submenuDisposables.add(this.parentData.submenu.onDidCancel(() => {\n                this.parentData.parent.focus();\n                this.cleanupExistingSubmenu(true);\n            }));\n            this.parentData.submenu.focus(selectFirstItem);\n            this.mysubmenu = this.parentData.submenu;\n        }\n        else {\n            this.parentData.submenu.focus(false);\n        }\n    }\n    updateAriaExpanded(value) {\n        if (this.item) {\n            this.item?.setAttribute('aria-expanded', value);\n        }\n    }\n    applyStyle() {\n        super.applyStyle();\n        const isSelected = this.element && this.element.classList.contains('focused');\n        const fgColor = isSelected && this.menuStyle.selectionForegroundColor ? this.menuStyle.selectionForegroundColor : this.menuStyle.foregroundColor;\n        if (this.submenuIndicator) {\n            this.submenuIndicator.style.color = fgColor ?? '';\n        }\n    }\n    dispose() {\n        super.dispose();\n        this.hideScheduler.dispose();\n        if (this.mysubmenu) {\n            this.mysubmenu.dispose();\n            this.mysubmenu = null;\n        }\n        if (this.submenuContainer) {\n            this.submenuContainer = undefined;\n        }\n    }\n}\nclass MenuSeparatorActionViewItem extends ActionViewItem {\n    constructor(context, action, options, menuStyles) {\n        super(context, action, options);\n        this.menuStyles = menuStyles;\n    }\n    render(container) {\n        super.render(container);\n        if (this.label) {\n            this.label.style.borderBottomColor = this.menuStyles.separatorColor ? `${this.menuStyles.separatorColor}` : '';\n        }\n    }\n}\nexport function cleanMnemonic(label) {\n    const regex = MENU_MNEMONIC_REGEX;\n    const matches = regex.exec(label);\n    if (!matches) {\n        return label;\n    }\n    const mnemonicInText = !matches[1];\n    return label.replace(regex, mnemonicInText ? '$2$3' : '').trim();\n}\nexport function formatRule(c) {\n    const fontCharacter = getCodiconFontCharacters()[c.id];\n    return `.codicon-${c.id}:before { content: '\\\\${fontCharacter.toString(16)}'; }`;\n}\nfunction getMenuWidgetCSS(style, isForShadowDom) {\n    let result = /* css */ `\n.monaco-menu {\n\tfont-size: 13px;\n\tborder-radius: 5px;\n\tmin-width: 160px;\n}\n\n${formatRule(Codicon.menuSelection)}\n${formatRule(Codicon.menuSubmenu)}\n\n.monaco-menu .monaco-action-bar {\n\ttext-align: right;\n\toverflow: hidden;\n\twhite-space: nowrap;\n}\n\n.monaco-menu .monaco-action-bar .actions-container {\n\tdisplay: flex;\n\tmargin: 0 auto;\n\tpadding: 0;\n\twidth: 100%;\n\tjustify-content: flex-end;\n}\n\n.monaco-menu .monaco-action-bar.vertical .actions-container {\n\tdisplay: inline-block;\n}\n\n.monaco-menu .monaco-action-bar.reverse .actions-container {\n\tflex-direction: row-reverse;\n}\n\n.monaco-menu .monaco-action-bar .action-item {\n\tcursor: pointer;\n\tdisplay: inline-block;\n\ttransition: transform 50ms ease;\n\tposition: relative;  /* DO NOT REMOVE - this is the key to preventing the ghosting icon bug in Chrome 42 */\n}\n\n.monaco-menu .monaco-action-bar .action-item.disabled {\n\tcursor: default;\n}\n\n.monaco-menu .monaco-action-bar .action-item .icon,\n.monaco-menu .monaco-action-bar .action-item .codicon {\n\tdisplay: inline-block;\n}\n\n.monaco-menu .monaco-action-bar .action-item .codicon {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.monaco-menu .monaco-action-bar .action-label {\n\tfont-size: 11px;\n\tmargin-right: 4px;\n}\n\n.monaco-menu .monaco-action-bar .action-item.disabled .action-label,\n.monaco-menu .monaco-action-bar .action-item.disabled .action-label:hover {\n\tcolor: var(--vscode-disabledForeground);\n}\n\n/* Vertical actions */\n\n.monaco-menu .monaco-action-bar.vertical {\n\ttext-align: left;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item {\n\tdisplay: block;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label.separator {\n\tdisplay: block;\n\tborder-bottom: 1px solid var(--vscode-menu-separatorBackground);\n\tpadding-top: 1px;\n\tpadding: 30px;\n}\n\n.monaco-menu .secondary-actions .monaco-action-bar .action-label {\n\tmargin-left: 6px;\n}\n\n/* Action Items */\n.monaco-menu .monaco-action-bar .action-item.select-container {\n\toverflow: hidden; /* somehow the dropdown overflows its container, we prevent it here to not push */\n\tflex: 1;\n\tmax-width: 170px;\n\tmin-width: 60px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 10px;\n}\n\n.monaco-menu .monaco-action-bar.vertical {\n\tmargin-left: 0;\n\toverflow: visible;\n}\n\n.monaco-menu .monaco-action-bar.vertical .actions-container {\n\tdisplay: block;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item {\n\tpadding: 0;\n\ttransform: none;\n\tdisplay: flex;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item.active {\n\ttransform: none;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-menu-item {\n\tflex: 1 1 auto;\n\tdisplay: flex;\n\theight: 2em;\n\talign-items: center;\n\tposition: relative;\n\tmargin: 0 4px;\n\tborder-radius: 4px;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-menu-item:hover .keybinding,\n.monaco-menu .monaco-action-bar.vertical .action-menu-item:focus .keybinding {\n\topacity: unset;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label {\n\tflex: 1 1 auto;\n\ttext-decoration: none;\n\tpadding: 0 1em;\n\tbackground: none;\n\tfont-size: 12px;\n\tline-height: 1;\n}\n\n.monaco-menu .monaco-action-bar.vertical .keybinding,\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator {\n\tdisplay: inline-block;\n\tflex: 2 1 auto;\n\tpadding: 0 1em;\n\ttext-align: right;\n\tfont-size: 12px;\n\tline-height: 1;\n}\n\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator {\n\theight: 100%;\n}\n\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator.codicon {\n\tfont-size: 16px !important;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator.codicon::before {\n\tmargin-left: auto;\n\tmargin-right: -20px;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item.disabled .keybinding,\n.monaco-menu .monaco-action-bar.vertical .action-item.disabled .submenu-indicator {\n\topacity: 0.4;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label:not(.separator) {\n\tdisplay: inline-block;\n\tbox-sizing: border-box;\n\tmargin: 0;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item {\n\tposition: static;\n\toverflow: visible;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-item .monaco-submenu {\n\tposition: absolute;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label.separator {\n\twidth: 100%;\n\theight: 0px !important;\n\topacity: 1;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label.separator.text {\n\tpadding: 0.7em 1em 0.1em 1em;\n\tfont-weight: bold;\n\topacity: 1;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label:hover {\n\tcolor: inherit;\n}\n\n.monaco-menu .monaco-action-bar.vertical .menu-item-check {\n\tposition: absolute;\n\tvisibility: hidden;\n\twidth: 1em;\n\theight: 100%;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-menu-item.checked .menu-item-check {\n\tvisibility: visible;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n/* Context Menu */\n\n.context-view.monaco-menu-container {\n\toutline: 0;\n\tborder: none;\n\tanimation: fadeIn 0.083s linear;\n\t-webkit-app-region: no-drag;\n}\n\n.context-view.monaco-menu-container :focus,\n.context-view.monaco-menu-container .monaco-action-bar.vertical:focus,\n.context-view.monaco-menu-container .monaco-action-bar.vertical :focus {\n\toutline: 0;\n}\n\n.hc-black .context-view.monaco-menu-container,\n.hc-light .context-view.monaco-menu-container,\n:host-context(.hc-black) .context-view.monaco-menu-container,\n:host-context(.hc-light) .context-view.monaco-menu-container {\n\tbox-shadow: none;\n}\n\n.hc-black .monaco-menu .monaco-action-bar.vertical .action-item.focused,\n.hc-light .monaco-menu .monaco-action-bar.vertical .action-item.focused,\n:host-context(.hc-black) .monaco-menu .monaco-action-bar.vertical .action-item.focused,\n:host-context(.hc-light) .monaco-menu .monaco-action-bar.vertical .action-item.focused {\n\tbackground: none;\n}\n\n/* Vertical Action Bar Styles */\n\n.monaco-menu .monaco-action-bar.vertical {\n\tpadding: 4px 0;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-menu-item {\n\theight: 2em;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label:not(.separator),\n.monaco-menu .monaco-action-bar.vertical .keybinding {\n\tfont-size: inherit;\n\tpadding: 0 2em;\n\tmax-height: 100%;\n}\n\n.monaco-menu .monaco-action-bar.vertical .menu-item-check {\n\tfont-size: inherit;\n\twidth: 2em;\n}\n\n.monaco-menu .monaco-action-bar.vertical .action-label.separator {\n\tfont-size: inherit;\n\tmargin: 5px 0 !important;\n\tpadding: 0;\n\tborder-radius: 0;\n}\n\n.linux .monaco-menu .monaco-action-bar.vertical .action-label.separator,\n:host-context(.linux) .monaco-menu .monaco-action-bar.vertical .action-label.separator {\n\tmargin-left: 0;\n\tmargin-right: 0;\n}\n\n.monaco-menu .monaco-action-bar.vertical .submenu-indicator {\n\tfont-size: 60%;\n\tpadding: 0 1.8em;\n}\n\n.linux .monaco-menu .monaco-action-bar.vertical .submenu-indicator,\n:host-context(.linux) .monaco-menu .monaco-action-bar.vertical .submenu-indicator {\n\theight: 100%;\n\tmask-size: 10px 10px;\n\t-webkit-mask-size: 10px 10px;\n}\n\n.monaco-menu .action-item {\n\tcursor: default;\n}`;\n    if (isForShadowDom) {\n        // Only define scrollbar styles when used inside shadow dom,\n        // otherwise leave their styling to the global workbench styling.\n        result += `\n\t\t\t/* Arrows */\n\t\t\t.monaco-scrollable-element > .scrollbar > .scra {\n\t\t\t\tcursor: pointer;\n\t\t\t\tfont-size: 11px !important;\n\t\t\t}\n\n\t\t\t.monaco-scrollable-element > .visible {\n\t\t\t\topacity: 1;\n\n\t\t\t\t/* Background rule added for IE9 - to allow clicks on dom node */\n\t\t\t\tbackground:rgba(0,0,0,0);\n\n\t\t\t\ttransition: opacity 100ms linear;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .invisible {\n\t\t\t\topacity: 0;\n\t\t\t\tpointer-events: none;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .invisible.fade {\n\t\t\t\ttransition: opacity 800ms linear;\n\t\t\t}\n\n\t\t\t/* Scrollable Content Inset Shadow */\n\t\t\t.monaco-scrollable-element > .shadow {\n\t\t\t\tposition: absolute;\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .shadow.top {\n\t\t\t\tdisplay: block;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 3px;\n\t\t\t\theight: 3px;\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .shadow.left {\n\t\t\t\tdisplay: block;\n\t\t\t\ttop: 3px;\n\t\t\t\tleft: 0;\n\t\t\t\theight: 100%;\n\t\t\t\twidth: 3px;\n\t\t\t}\n\t\t\t.monaco-scrollable-element > .shadow.top-left-corner {\n\t\t\t\tdisplay: block;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\theight: 3px;\n\t\t\t\twidth: 3px;\n\t\t\t}\n\t\t`;\n        // Scrollbars\n        const scrollbarShadowColor = style.scrollbarShadow;\n        if (scrollbarShadowColor) {\n            result += `\n\t\t\t\t.monaco-scrollable-element > .shadow.top {\n\t\t\t\t\tbox-shadow: ${scrollbarShadowColor} 0 6px 6px -6px inset;\n\t\t\t\t}\n\n\t\t\t\t.monaco-scrollable-element > .shadow.left {\n\t\t\t\t\tbox-shadow: ${scrollbarShadowColor} 6px 0 6px -6px inset;\n\t\t\t\t}\n\n\t\t\t\t.monaco-scrollable-element > .shadow.top.left {\n\t\t\t\t\tbox-shadow: ${scrollbarShadowColor} 6px 6px 6px -6px inset;\n\t\t\t\t}\n\t\t\t`;\n        }\n        const scrollbarSliderBackgroundColor = style.scrollbarSliderBackground;\n        if (scrollbarSliderBackgroundColor) {\n            result += `\n\t\t\t\t.monaco-scrollable-element > .scrollbar > .slider {\n\t\t\t\t\tbackground: ${scrollbarSliderBackgroundColor};\n\t\t\t\t}\n\t\t\t`;\n        }\n        const scrollbarSliderHoverBackgroundColor = style.scrollbarSliderHoverBackground;\n        if (scrollbarSliderHoverBackgroundColor) {\n            result += `\n\t\t\t\t.monaco-scrollable-element > .scrollbar > .slider:hover {\n\t\t\t\t\tbackground: ${scrollbarSliderHoverBackgroundColor};\n\t\t\t\t}\n\t\t\t`;\n        }\n        const scrollbarSliderActiveBackgroundColor = style.scrollbarSliderActiveBackground;\n        if (scrollbarSliderActiveBackgroundColor) {\n            result += `\n\t\t\t\t.monaco-scrollable-element > .scrollbar > .slider.active {\n\t\t\t\t\tbackground: ${scrollbarSliderActiveBackgroundColor};\n\t\t\t\t}\n\t\t\t`;\n        }\n    }\n    return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,IAAIC,cAAc,EAAEC,OAAO,QAAQ,gBAAgB;AACrE,SAASC,CAAC,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,WAAW,EAAET,SAAS,EAAEU,gBAAgB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,aAAa,QAAQ,cAAc;AACvL,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,kBAAkB,QAAQ,qBAAqB;AACxD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,iCAAiC;AACpF,SAASC,MAAM,QAAQ,+BAA+B;AACtD,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,kBAAkB,EAAEC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACzF,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,OAAO,EAAEC,WAAW,QAAQ,6BAA6B;AAClE,OAAO,KAAKC,OAAO,MAAM,4BAA4B;AACrD,OAAO,MAAMC,mBAAmB,GAAG,iCAAiC;AACpE,OAAO,MAAMC,2BAA2B,GAAG,0BAA0B;AACrE,OAAO,IAAIC,mBAAmB,gBAC7B,UAAUA,mBAAmB,EAAE;EAC5BA,mBAAmB,CAACA,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC/DA,mBAAmB,CAACA,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAAC,OAFvDA,mBAAmB;AAG9B,CAAC,CAAEA,mBAAmB,IAA2B,CAAC,CAAE,CAJtB;AAK9B,OAAO,IAAIC,iBAAiB,gBAC3B,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAACA,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC3DA,iBAAiB,CAACA,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAAC,OAFrDA,iBAAiB;AAG5B,CAAC,CAAEA,iBAAiB,IAAyB,CAAC,CAAE,CAJpB;AAK5B,OAAO,MAAMC,IAAI,SAASrB,SAAS,CAAC;EAChCsB,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAE;IACjDH,SAAS,CAACI,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAChDL,SAAS,CAACM,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IAC9C,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACjDF,WAAW,CAACH,SAAS,CAACC,GAAG,CAAC,aAAa,CAAC;IACxCE,WAAW,CAACD,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IAChD,KAAK,CAACC,WAAW,EAAE;MACfG,WAAW,EAAE,CAAC,CAAC;MACfC,sBAAsB,EAAEC,MAAM,IAAI,IAAI,CAACC,mBAAmB,CAACD,MAAM,EAAEV,OAAO,EAAEY,UAAU,CAAC;MACvFC,OAAO,EAAEb,OAAO,CAACa,OAAO;MACxBC,YAAY,EAAEd,OAAO,CAACc,YAAY;MAClCC,SAAS,EAAEf,OAAO,CAACe,SAAS;MAC5BC,QAAQ,EAAE,MAAM;MAChBC,qBAAqB,EAAE,IAAI;MAC3BC,WAAW,EAAE;QAAEC,IAAI,EAAE,CAAC,CAAC,CAAC,qBAAqB,IAAI7B,WAAW,IAAID,OAAO,GAAG,CAAC,EAAE,CAAC,oBAAoB,GAAG,EAAE,CAAC,CAAC;QAAE+B,OAAO,EAAE;MAAK;IAC7H,CAAC,CAAC;IACF,IAAI,CAACnB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACgB,WAAW,CAACC,QAAQ,GAAG,CAAC;IAC7B,IAAI,CAACC,4BAA4B,CAACzB,SAAS,EAAEG,UAAU,CAAC;IACxD,IAAI,CAACuB,SAAS,CAAC/D,OAAO,CAACgE,SAAS,CAACpB,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACmB,SAAS,CAAC7D,qBAAqB,CAAC0C,WAAW,EAAE9C,SAAS,CAACmE,QAAQ,EAAGC,CAAC,IAAK;MACzE,MAAMC,KAAK,GAAG,IAAIvD,qBAAqB,CAACsD,CAAC,CAAC;MAC1C;MACA,IAAIC,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,EAAE;QACnCF,CAAC,CAACG,cAAc,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI9B,OAAO,CAAC+B,eAAe,EAAE;MACzB,IAAI,CAACP,SAAS,CAAC7D,qBAAqB,CAAC0C,WAAW,EAAE9C,SAAS,CAACmE,QAAQ,EAAGC,CAAC,IAAK;QACzE,MAAMK,GAAG,GAAGL,CAAC,CAACK,GAAG,CAACC,iBAAiB,CAAC,CAAC;QACrC,IAAI,IAAI,CAACC,SAAS,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;UACzBhE,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;UACzB,MAAM5B,OAAO,GAAG,IAAI,CAACmC,SAAS,CAACG,GAAG,CAACL,GAAG,CAAC;UACvC,IAAIjC,OAAO,CAACuC,MAAM,KAAK,CAAC,EAAE;YACtB,IAAIvC,OAAO,CAAC,CAAC,CAAC,YAAYwC,yBAAyB,IAAIxC,OAAO,CAAC,CAAC,CAAC,CAACD,SAAS,EAAE;cACzE,IAAI,CAAC0C,kBAAkB,CAACzC,OAAO,CAAC,CAAC,CAAC,CAACD,SAAS,CAAC;YACjD;YACAC,OAAO,CAAC,CAAC,CAAC,CAAC0C,OAAO,CAACd,CAAC,CAAC;UACzB;UACA,IAAI5B,OAAO,CAACuC,MAAM,GAAG,CAAC,EAAE;YACpB,MAAM5B,MAAM,GAAGX,OAAO,CAAC2C,KAAK,CAAC,CAAC;YAC9B,IAAIhC,MAAM,IAAIA,MAAM,CAACZ,SAAS,EAAE;cAC5B,IAAI,CAAC0C,kBAAkB,CAAC9B,MAAM,CAACZ,SAAS,CAAC;cACzCC,OAAO,CAAC4C,IAAI,CAACjC,MAAM,CAAC;YACxB;YACA,IAAI,CAACwB,SAAS,CAACU,GAAG,CAACZ,GAAG,EAAEjC,OAAO,CAAC;UACpC;QACJ;MACJ,CAAC,CAAC,CAAC;IACP;IACA,IAAIV,OAAO,EAAE;MACT,IAAI,CAACmC,SAAS,CAAC7D,qBAAqB,CAAC0C,WAAW,EAAE9C,SAAS,CAACmE,QAAQ,EAAEC,CAAC,IAAI;QACvE,MAAMC,KAAK,GAAG,IAAIvD,qBAAqB,CAACsD,CAAC,CAAC;QAC1C,IAAIC,KAAK,CAACC,MAAM,CAAC,EAAE,CAAC,kBAAkB,CAAC,IAAID,KAAK,CAACC,MAAM,CAAC,EAAE,CAAC,oBAAoB,CAAC,EAAE;UAC9E,IAAI,CAACgB,WAAW,GAAG,IAAI,CAACC,SAAS,CAACR,MAAM,GAAG,CAAC;UAC5C,IAAI,CAACS,SAAS,CAAC,CAAC;UAChB/E,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;QAC7B,CAAC,MACI,IAAIC,KAAK,CAACC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC,IAAID,KAAK,CAACC,MAAM,CAAC,EAAE,CAAC,sBAAsB,CAAC,EAAE;UACpF,IAAI,CAACgB,WAAW,GAAG,CAAC;UACpB,IAAI,CAACG,aAAa,CAAC,CAAC;UACpBhF,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;QAC7B;MACJ,CAAC,CAAC,CAAC;IACP;IACA,IAAI,CAACH,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAACsF,OAAO,EAAE1F,SAAS,CAAC2F,SAAS,EAAEvB,CAAC,IAAI;MACzE,MAAMwB,aAAa,GAAGxB,CAAC,CAACwB,aAAa;MACrC,IAAI,CAAChF,UAAU,CAACgF,aAAa,EAAE,IAAI,CAACF,OAAO,CAAC,EAAE;QAC1C,IAAI,CAACJ,WAAW,GAAGO,SAAS;QAC5B,IAAI,CAACC,WAAW,CAAC,CAAC;QAClB1B,CAAC,CAAC2B,eAAe,CAAC,CAAC;MACvB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC9B,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAAC0D,WAAW,EAAE9D,SAAS,CAACgG,UAAU,EAAE5B,CAAC,IAAI;MAC9E,IAAI6B,MAAM,GAAG7B,CAAC,CAAC6B,MAAM;MACrB,IAAI,CAACA,MAAM,IAAI,CAACrF,UAAU,CAACqF,MAAM,EAAE,IAAI,CAACnC,WAAW,CAAC,IAAImC,MAAM,KAAK,IAAI,CAACnC,WAAW,EAAE;QACjF;MACJ;MACA,OAAOmC,MAAM,CAACC,aAAa,KAAK,IAAI,CAACpC,WAAW,IAAImC,MAAM,CAACC,aAAa,KAAK,IAAI,EAAE;QAC/ED,MAAM,GAAGA,MAAM,CAACC,aAAa;MACjC;MACA,IAAID,MAAM,CAACtD,SAAS,CAACwD,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC1C,MAAMC,eAAe,GAAG,IAAI,CAACd,WAAW;QACxC,IAAI,CAACe,cAAc,CAACJ,MAAM,CAAC;QAC3B,IAAIG,eAAe,KAAK,IAAI,CAACd,WAAW,EAAE;UACtC,IAAI,CAACQ,WAAW,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC,CAAC,CAAC;IACH;IACA,IAAI,CAAC7B,SAAS,CAAC/D,OAAO,CAACgE,SAAS,CAAC,IAAI,CAACJ,WAAW,CAAC,CAAC;IACnD,IAAI,CAACG,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAAC0D,WAAW,EAAE7D,cAAc,CAACqG,GAAG,EAAElC,CAAC,IAAI;MAC5E,IAAI6B,MAAM,GAAG7B,CAAC,CAACmC,aAAa;MAC5B,IAAI,CAACN,MAAM,IAAI,CAACrF,UAAU,CAACqF,MAAM,EAAE,IAAI,CAACnC,WAAW,CAAC,IAAImC,MAAM,KAAK,IAAI,CAACnC,WAAW,EAAE;QACjF;MACJ;MACA,OAAOmC,MAAM,CAACC,aAAa,KAAK,IAAI,CAACpC,WAAW,IAAImC,MAAM,CAACC,aAAa,KAAK,IAAI,EAAE;QAC/ED,MAAM,GAAGA,MAAM,CAACC,aAAa;MACjC;MACA,IAAID,MAAM,CAACtD,SAAS,CAACwD,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC1C,MAAMC,eAAe,GAAG,IAAI,CAACd,WAAW;QACxC,IAAI,CAACe,cAAc,CAACJ,MAAM,CAAC;QAC3B,IAAIG,eAAe,KAAK,IAAI,CAACd,WAAW,EAAE;UACtC,IAAI,CAACQ,WAAW,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC,CAAC,CAAC;IACH,MAAMzC,UAAU,GAAG;MACfmD,MAAM,EAAE;IACZ,CAAC;IACD,IAAI,CAAC7B,SAAS,GAAG,IAAI8B,GAAG,CAAC,CAAC;IAC1B;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACzC,SAAS,CAAC,IAAI7C,oBAAoB,CAAC0B,WAAW,EAAE;MAC1E6D,uBAAuB,EAAE,IAAI;MAC7BC,UAAU,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE,CAAC,CAAC;MACZC,qBAAqB,EAAE,CAAC;MACxBC,gBAAgB,EAAE,IAAI;MACtBC,UAAU,EAAE;IAChB,CAAC,CAAC,CAAC;IACH,MAAMC,aAAa,GAAG,IAAI,CAACP,iBAAiB,CAACQ,UAAU,CAAC,CAAC;IACzDD,aAAa,CAACE,KAAK,CAACC,QAAQ,GAAG,EAAE;IACjC,IAAI,CAACC,kBAAkB,CAACJ,aAAa,EAAEvE,UAAU,CAAC;IAClD;IACA,IAAI,CAACuB,SAAS,CAAC7D,qBAAqB,CAAC0C,WAAW,EAAE7C,cAAc,CAACqH,MAAM,EAAElD,CAAC,IAAI;MAC1E3D,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;MACzB,MAAMmD,SAAS,GAAG,IAAI,CAACb,iBAAiB,CAACc,iBAAiB,CAAC,CAAC,CAACD,SAAS;MACtE,IAAI,CAACb,iBAAiB,CAACe,iBAAiB,CAAC;QAAEF,SAAS,EAAEA,SAAS,GAAGnD,CAAC,CAACsD;MAAa,CAAC,CAAC;IACvF,CAAC,CAAC,CAAC;IACH,IAAI,CAACzD,SAAS,CAAC7D,qBAAqB,CAAC6G,aAAa,EAAEjH,SAAS,CAAC2H,QAAQ,EAAEvD,CAAC,IAAI;MACzE;MACA;MACAA,CAAC,CAACG,cAAc,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC;IACH,MAAMqD,MAAM,GAAGjH,SAAS,CAAC4B,SAAS,CAAC;IACnCO,WAAW,CAACqE,KAAK,CAACU,SAAS,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEH,MAAM,CAACI,WAAW,GAAGzF,SAAS,CAAC0F,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAG,EAAE,CAAC,IAAI;IAClH1F,OAAO,GAAGA,OAAO,CAAC2F,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;MACjC,IAAI5F,OAAO,CAAC6F,UAAU,EAAE1D,GAAG,CAACwD,CAAC,CAACG,EAAE,CAAC,EAAE;QAC/BC,OAAO,CAACC,IAAI,CAAC,wBAAwBL,CAAC,CAACG,EAAE,EAAE,CAAC;QAC5C,OAAO,KAAK;MAChB;MACA;MACA,IAAIH,CAAC,YAAY9G,SAAS,EAAE;QACxB,IAAI+G,GAAG,KAAK7F,OAAO,CAACuC,MAAM,GAAG,CAAC,IAAIsD,GAAG,KAAK,CAAC,EAAE;UACzC,OAAO,KAAK;QAChB;QACA,MAAMK,UAAU,GAAGlG,OAAO,CAAC6F,GAAG,GAAG,CAAC,CAAC;QACnC,IAAIK,UAAU,YAAYpH,SAAS,EAAE;UACjC,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf,CAAC,CAAC;IACF,IAAI,CAAC8D,IAAI,CAAC5C,OAAO,EAAE;MAAEmG,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;IAC7DtG,SAAS,CAACuG,WAAW,CAAC,IAAI,CAACpC,iBAAiB,CAACQ,UAAU,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACR,iBAAiB,CAACqC,WAAW,CAAC,CAAC;IACpC,IAAI,CAACxD,SAAS,CAAC4C,MAAM,CAACa,IAAI,IAAI,EAAEA,IAAI,YAAYC,2BAA2B,CAAC,CAAC,CAACC,OAAO,CAAC,CAACF,IAAI,EAAEG,KAAK,EAAEC,KAAK,KAAK;MAC1GJ,IAAI,CAACK,mBAAmB,CAACF,KAAK,GAAG,CAAC,EAAEC,KAAK,CAACrE,MAAM,CAAC;IACrD,CAAC,CAAC;EACN;EACAf,4BAA4BA,CAACzB,SAAS,EAAE4E,KAAK,EAAE;IAC3C,IAAI,CAAC,IAAI,CAACmC,UAAU,EAAE;MAClB,IAAIzI,aAAa,CAAC0B,SAAS,CAAC,EAAE;QAC1B,IAAI,CAAC+G,UAAU,GAAG/I,gBAAgB,CAACgC,SAAS,CAAC;MACjD,CAAC,MACI;QACD,IAAI,CAACF,IAAI,CAACkH,gBAAgB,EAAE;UACxBlH,IAAI,CAACkH,gBAAgB,GAAGhJ,gBAAgB,CAAC,CAAC;QAC9C;QACA,IAAI,CAAC+I,UAAU,GAAGjH,IAAI,CAACkH,gBAAgB;MAC3C;IACJ;IACA,IAAI,CAACD,UAAU,CAACE,WAAW,GAAGC,gBAAgB,CAACtC,KAAK,EAAEtG,aAAa,CAAC0B,SAAS,CAAC,CAAC;EACnF;EACA8E,kBAAkBA,CAACJ,aAAa,EAAEE,KAAK,EAAE;IACrC,MAAMuC,OAAO,GAAGvC,KAAK,CAACwC,eAAe,IAAI,EAAE;IAC3C,MAAMC,OAAO,GAAGzC,KAAK,CAAC0C,eAAe,IAAI,EAAE;IAC3C,MAAMC,MAAM,GAAG3C,KAAK,CAAC4C,WAAW,GAAG,aAAa5C,KAAK,CAAC4C,WAAW,EAAE,GAAG,EAAE;IACxE,MAAMC,YAAY,GAAG,KAAK;IAC1B,MAAMC,MAAM,GAAG9C,KAAK,CAAC+C,WAAW,GAAG,aAAa/C,KAAK,CAAC+C,WAAW,EAAE,GAAG,EAAE;IACxEjD,aAAa,CAACE,KAAK,CAACgD,OAAO,GAAGL,MAAM;IACpC7C,aAAa,CAACE,KAAK,CAAC6C,YAAY,GAAGA,YAAY;IAC/C/C,aAAa,CAACE,KAAK,CAACiD,KAAK,GAAGV,OAAO;IACnCzC,aAAa,CAACE,KAAK,CAAC0C,eAAe,GAAGD,OAAO;IAC7C3C,aAAa,CAACE,KAAK,CAACkD,SAAS,GAAGJ,MAAM;EAC1C;EACAK,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC5D,iBAAiB,CAACQ,UAAU,CAAC,CAAC;EAC9C;EACA,IAAIqD,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC7D,iBAAiB,CAAC6D,QAAQ;EAC1C;EACAtF,kBAAkBA,CAACuF,OAAO,EAAE;IACxB,MAAMpE,eAAe,GAAG,IAAI,CAACd,WAAW;IACxC,IAAI,CAACe,cAAc,CAACmE,OAAO,CAAC;IAC5B,IAAIpE,eAAe,KAAK,IAAI,CAACd,WAAW,EAAE;MACtC,IAAI,CAACQ,WAAW,CAAC,CAAC;IACtB;EACJ;EACAO,cAAcA,CAACmE,OAAO,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3G,WAAW,CAAC4G,QAAQ,CAAC3F,MAAM,EAAE0F,CAAC,EAAE,EAAE;MACvD,MAAME,IAAI,GAAG,IAAI,CAAC7G,WAAW,CAAC4G,QAAQ,CAACD,CAAC,CAAC;MACzC,IAAID,OAAO,KAAKG,IAAI,EAAE;QAClB,IAAI,CAACrF,WAAW,GAAGmF,CAAC;QACpB;MACJ;IACJ;EACJ;EACA3E,WAAWA,CAAC8E,SAAS,EAAE;IACnB,KAAK,CAAC9E,WAAW,CAAC8E,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;IACxC,IAAI,OAAO,IAAI,CAACtF,WAAW,KAAK,WAAW,EAAE;MACzC;MACA;MACA;MACA,IAAI,CAACoB,iBAAiB,CAACe,iBAAiB,CAAC;QACrCF,SAAS,EAAEO,IAAI,CAAC+C,KAAK,CAAC,IAAI,CAAC/H,WAAW,CAACyE,SAAS;MACpD,CAAC,CAAC;IACN;EACJ;EACAnE,mBAAmBA,CAACD,MAAM,EAAEV,OAAO,EAAEY,UAAU,EAAE;IAC7C,IAAIF,MAAM,YAAY7B,SAAS,EAAE;MAC7B,OAAO,IAAI2H,2BAA2B,CAACxG,OAAO,CAACa,OAAO,EAAEH,MAAM,EAAE;QAAEwF,IAAI,EAAE;MAAK,CAAC,EAAE,IAAI,CAACjG,UAAU,CAAC;IACpG,CAAC,MACI,IAAIS,MAAM,YAAY5B,aAAa,EAAE;MACtC,MAAMuJ,kBAAkB,GAAG,IAAI9F,yBAAyB,CAAC7B,MAAM,EAAEA,MAAM,CAACX,OAAO,EAAEa,UAAU,EAAE;QAAE,GAAGZ,OAAO;QAAE6F,UAAU,EAAE,IAAIyC,GAAG,CAAC,CAAC,IAAItI,OAAO,CAAC6F,UAAU,IAAI,EAAE,CAAC,EAAEnF,MAAM,CAACoF,EAAE,CAAC;MAAE,CAAC,EAAE,IAAI,CAAC7F,UAAU,CAAC;MAC9L,IAAID,OAAO,CAAC+B,eAAe,EAAE;QACzB,MAAMwG,QAAQ,GAAGF,kBAAkB,CAACG,WAAW,CAAC,CAAC;QACjD,IAAID,QAAQ,IAAIF,kBAAkB,CAACI,SAAS,CAAC,CAAC,EAAE;UAC5C,IAAIC,eAAe,GAAG,EAAE;UACxB,IAAI,IAAI,CAACxG,SAAS,CAACC,GAAG,CAACoG,QAAQ,CAAC,EAAE;YAC9BG,eAAe,GAAG,IAAI,CAACxG,SAAS,CAACG,GAAG,CAACkG,QAAQ,CAAC;UAClD;UACAG,eAAe,CAAC/F,IAAI,CAAC0F,kBAAkB,CAAC;UACxC,IAAI,CAACnG,SAAS,CAACU,GAAG,CAAC2F,QAAQ,EAAEG,eAAe,CAAC;QACjD;MACJ;MACA,OAAOL,kBAAkB;IAC7B,CAAC,MACI;MACD,MAAMM,eAAe,GAAG;QAAE5G,eAAe,EAAE/B,OAAO,CAAC+B,eAAe;QAAE6G,iBAAiB,EAAE5I,OAAO,CAAC4I;MAAkB,CAAC;MAClH,IAAI5I,OAAO,CAAC6I,aAAa,EAAE;QACvB,MAAMC,UAAU,GAAG9I,OAAO,CAAC6I,aAAa,CAACnI,MAAM,CAAC;QAChD,IAAIoI,UAAU,EAAE;UACZ,MAAMC,eAAe,GAAGD,UAAU,CAACE,QAAQ,CAAC,CAAC;UAC7C,IAAID,eAAe,EAAE;YACjBJ,eAAe,CAACG,UAAU,GAAGC,eAAe;UAChD;QACJ;MACJ;MACA,MAAMV,kBAAkB,GAAG,IAAIY,sBAAsB,CAACjJ,OAAO,CAACa,OAAO,EAAEH,MAAM,EAAEiI,eAAe,EAAE,IAAI,CAAC1I,UAAU,CAAC;MAChH,IAAID,OAAO,CAAC+B,eAAe,EAAE;QACzB,MAAMwG,QAAQ,GAAGF,kBAAkB,CAACG,WAAW,CAAC,CAAC;QACjD,IAAID,QAAQ,IAAIF,kBAAkB,CAACI,SAAS,CAAC,CAAC,EAAE;UAC5C,IAAIC,eAAe,GAAG,EAAE;UACxB,IAAI,IAAI,CAACxG,SAAS,CAACC,GAAG,CAACoG,QAAQ,CAAC,EAAE;YAC9BG,eAAe,GAAG,IAAI,CAACxG,SAAS,CAACG,GAAG,CAACkG,QAAQ,CAAC;UAClD;UACAG,eAAe,CAAC/F,IAAI,CAAC0F,kBAAkB,CAAC;UACxC,IAAI,CAACnG,SAAS,CAACU,GAAG,CAAC2F,QAAQ,EAAEG,eAAe,CAAC;QACjD;MACJ;MACA,OAAOL,kBAAkB;IAC7B;EACJ;AACJ;AACA,MAAMY,sBAAsB,SAASxK,kBAAkB,CAAC;EACpDoB,WAAWA,CAACqJ,GAAG,EAAExI,MAAM,EAAEV,OAAO,EAAEmJ,SAAS,EAAE;IACzCnJ,OAAO,CAACoG,MAAM,GAAG,IAAI;IACrB,KAAK,CAAC1F,MAAM,EAAEA,MAAM,EAAEV,OAAO,CAAC;IAC9B,IAAI,CAACmJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACnJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACA,OAAO,CAACkG,IAAI,GAAGlG,OAAO,CAACkG,IAAI,KAAK9C,SAAS,GAAGpD,OAAO,CAACkG,IAAI,GAAG,KAAK;IACrE,IAAI,CAAClG,OAAO,CAACmG,KAAK,GAAGnG,OAAO,CAACmG,KAAK,KAAK/C,SAAS,GAAGpD,OAAO,CAACmG,KAAK,GAAG,IAAI;IACvE,IAAI,CAACiD,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,IAAI,CAACpJ,OAAO,CAACmG,KAAK,IAAInG,OAAO,CAAC+B,eAAe,EAAE;MAC/C,MAAMoE,KAAK,GAAG,IAAI,CAACzF,MAAM,CAACyF,KAAK;MAC/B,IAAIA,KAAK,EAAE;QACP,MAAMkD,OAAO,GAAG7J,mBAAmB,CAAC8J,IAAI,CAACnD,KAAK,CAAC;QAC/C,IAAIkD,OAAO,EAAE;UACT,IAAI,CAACd,QAAQ,GAAG,CAAC,CAAC,CAACc,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,EAAEpH,iBAAiB,CAAC,CAAC;QAChF;MACJ;IACJ;IACA;IACA,IAAI,CAACsH,sBAAsB,GAAG,IAAIxK,gBAAgB,CAAC,MAAM;MACrD,IAAI,CAAC,IAAI,CAACgJ,OAAO,EAAE;QACf;MACJ;MACA,IAAI,CAACvG,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAACoK,OAAO,EAAExK,SAAS,CAAC2H,QAAQ,EAAEvD,CAAC,IAAI;QACxE;QACA;QACA;QACA3D,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIrE,SAAS,EAAE;UACX,MAAMkM,UAAU,GAAG,IAAIlL,kBAAkB,CAACJ,SAAS,CAAC,IAAI,CAAC6J,OAAO,CAAC,EAAEpG,CAAC,CAAC;UACrE;UACA;UACA,IAAI6H,UAAU,CAACC,WAAW,EAAE;YACxB;UACJ;UACA,IAAI,CAAChH,OAAO,CAACd,CAAC,CAAC;QACnB;QACA;QACA;QACA;QAAA,KACK;UACD+H,UAAU,CAAC,MAAM;YACb,IAAI,CAACjH,OAAO,CAACd,CAAC,CAAC;UACnB,CAAC,EAAE,CAAC,CAAC;QACT;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACH,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAACoK,OAAO,EAAExK,SAAS,CAACoM,YAAY,EAAEhI,CAAC,IAAI;QAC5E3D,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;MAC7B,CAAC,CAAC,CAAC;IACP,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC+H,sBAAsB,CAAC;EAC/C;EACAK,MAAMA,CAAC9J,SAAS,EAAE;IACd,KAAK,CAAC8J,MAAM,CAAC9J,SAAS,CAAC;IACvB,IAAI,CAAC,IAAI,CAACiI,OAAO,EAAE;MACf;IACJ;IACA,IAAI,CAACjI,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACyG,IAAI,GAAG3I,MAAM,CAAC,IAAI,CAACmK,OAAO,EAAErK,CAAC,CAAC,oBAAoB,CAAC,CAAC;IACzD,IAAI,IAAI,CAACmM,OAAO,CAAC/D,EAAE,KAAKjH,SAAS,CAACiL,EAAE,EAAE;MAClC;MACA,IAAI,CAACvD,IAAI,CAACnG,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAACmG,IAAI,CAACnG,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MAC1C,IAAI,IAAI,CAACmI,QAAQ,EAAE;QACf,IAAI,CAAChC,IAAI,CAACnG,YAAY,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAACmI,QAAQ,EAAE,CAAC;MACnE;IACJ;IACA,IAAI,CAACwB,KAAK,GAAGnM,MAAM,CAAC,IAAI,CAAC2I,IAAI,EAAE7I,CAAC,CAAC,sBAAsB,GAAGwB,SAAS,CAAC8K,aAAa,CAAChL,OAAO,CAACiL,aAAa,CAAC,CAAC,CAAC;IAC1G,IAAI,CAACF,KAAK,CAAC3J,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;IACvC,IAAI,CAAC+F,KAAK,GAAGvI,MAAM,CAAC,IAAI,CAAC2I,IAAI,EAAE7I,CAAC,CAAC,mBAAmB,CAAC,CAAC;IACtD,IAAI,IAAI,CAACsC,OAAO,CAACmG,KAAK,IAAI,IAAI,CAACnG,OAAO,CAAC8I,UAAU,EAAE;MAC/ClL,MAAM,CAAC,IAAI,CAAC2I,IAAI,EAAE7I,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAACqJ,WAAW,GAAG,IAAI,CAAC/G,OAAO,CAAC8I,UAAU;IACjF;IACA;IACA,IAAI,CAACS,sBAAsB,CAACW,QAAQ,CAAC,CAAC;IACtC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,IAAIA,CAAA,EAAG;IACH,KAAK,CAACA,IAAI,CAAC,CAAC;IACZ,IAAI,CAACD,UAAU,CAAC,CAAC;EACrB;EACAE,KAAKA,CAAA,EAAG;IACJ,KAAK,CAACA,KAAK,CAAC,CAAC;IACb,IAAI,CAACnE,IAAI,EAAEmE,KAAK,CAAC,CAAC;IAClB,IAAI,CAACF,UAAU,CAAC,CAAC;EACrB;EACA5D,mBAAmBA,CAAC+D,GAAG,EAAEC,OAAO,EAAE;IAC9B,IAAI,IAAI,CAACrE,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACnG,YAAY,CAAC,eAAe,EAAE,GAAGuK,GAAG,EAAE,CAAC;MACjD,IAAI,CAACpE,IAAI,CAACnG,YAAY,CAAC,cAAc,EAAE,GAAGwK,OAAO,EAAE,CAAC;IACxD;EACJ;EACAR,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACjE,KAAK,EAAE;MACb;IACJ;IACA,IAAI,IAAI,CAACnG,OAAO,CAACmG,KAAK,EAAE;MACpBtI,SAAS,CAAC,IAAI,CAACsI,KAAK,CAAC;MACrB,IAAIA,KAAK,GAAGhH,UAAU,CAAC,IAAI,CAACuB,MAAM,CAACyF,KAAK,CAAC;MACzC,IAAIA,KAAK,EAAE;QACP,MAAM0E,UAAU,GAAGC,aAAa,CAAC3E,KAAK,CAAC;QACvC,IAAI,CAAC,IAAI,CAACnG,OAAO,CAAC+B,eAAe,EAAE;UAC/BoE,KAAK,GAAG0E,UAAU;QACtB;QACA,IAAI,CAAC1E,KAAK,CAAC/F,YAAY,CAAC,YAAY,EAAEyK,UAAU,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACrE,MAAM1B,OAAO,GAAG7J,mBAAmB,CAAC8J,IAAI,CAACnD,KAAK,CAAC;QAC/C,IAAIkD,OAAO,EAAE;UACTlD,KAAK,GAAG5G,OAAO,CAACyL,MAAM,CAAC7E,KAAK,CAAC;UAC7B;UACA1G,2BAA2B,CAACwL,SAAS,GAAG,CAAC;UACzC,IAAIC,QAAQ,GAAGzL,2BAA2B,CAAC6J,IAAI,CAACnD,KAAK,CAAC;UACtD;UACA,OAAO+E,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC5BA,QAAQ,GAAGzL,2BAA2B,CAAC6J,IAAI,CAACnD,KAAK,CAAC;UACtD;UACA,MAAMgF,oBAAoB,GAAIC,GAAG,IAAKA,GAAG,CAACL,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC;UACzE,IAAIG,QAAQ,EAAE;YACV,IAAI,CAAC/E,KAAK,CAACvI,MAAM,CAAC2B,OAAO,CAAC8L,KAAK,CAACF,oBAAoB,CAAChF,KAAK,CAACmF,MAAM,CAAC,CAAC,EAAEJ,QAAQ,CAACxE,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAEhJ,CAAC,CAAC,GAAG,EAAE;cAAE,aAAa,EAAE;YAAO,CAAC,EAAEwN,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE3L,OAAO,CAACgM,KAAK,CAACJ,oBAAoB,CAAChF,KAAK,CAACmF,MAAM,CAACJ,QAAQ,CAACxE,KAAK,GAAGwE,QAAQ,CAAC,CAAC,CAAC,CAAC5I,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;UAC7O,CAAC,MACI;YACD,IAAI,CAAC6D,KAAK,CAACqF,SAAS,GAAGL,oBAAoB,CAAChF,KAAK,CAAC,CAACsF,IAAI,CAAC,CAAC;UAC7D;UACA,IAAI,CAAClF,IAAI,EAAEnG,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAACiJ,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,EAAEpH,iBAAiB,CAAC,CAAC,CAAC;QAC9G,CAAC,MACI;UACD,IAAI,CAACkE,KAAK,CAACqF,SAAS,GAAGrF,KAAK,CAAC4E,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACU,IAAI,CAAC,CAAC;QAC3D;MACJ;IACJ;EACJ;EACApB,aAAaA,CAAA,EAAG;IACZ;EAAA;EAEJF,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACf,QAAQ,IAAI,IAAI,CAAC7C,IAAI,EAAE;MAC5B,IAAI,CAACA,IAAI,CAACrG,SAAS,CAACwL,MAAM,CAAC,GAAG,IAAI,CAACtC,QAAQ,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3D;IACA,IAAI,IAAI,CAAC3L,OAAO,CAACkG,IAAI,IAAI,IAAI,CAACC,KAAK,EAAE;MACjC,IAAI,CAACiD,QAAQ,GAAG,IAAI,CAAC1I,MAAM,CAACkL,KAAK,IAAI,EAAE;MACvC,IAAI,CAACzF,KAAK,CAACjG,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;MAChC,IAAI,IAAI,CAACiJ,QAAQ,EAAE;QACf,IAAI,CAACjD,KAAK,CAACjG,SAAS,CAACC,GAAG,CAAC,GAAG,IAAI,CAACiJ,QAAQ,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC;MACzD;MACA,IAAI,CAACrB,aAAa,CAAC,CAAC;IACxB,CAAC,MACI,IAAI,IAAI,CAACnE,KAAK,EAAE;MACjB,IAAI,CAACA,KAAK,CAACjG,SAAS,CAACwL,MAAM,CAAC,MAAM,CAAC;IACvC;EACJ;EACApB,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC5J,MAAM,CAACmL,OAAO,EAAE;MACrB,IAAI,IAAI,CAAC9D,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAAC7H,SAAS,CAACwL,MAAM,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC3D,OAAO,CAAC+D,eAAe,CAAC,eAAe,CAAC;MACjD;MACA,IAAI,IAAI,CAACvF,IAAI,EAAE;QACX,IAAI,CAACA,IAAI,CAACrG,SAAS,CAACwL,MAAM,CAAC,UAAU,CAAC;QACtC,IAAI,CAACnF,IAAI,CAACuF,eAAe,CAAC,eAAe,CAAC;QAC1C,IAAI,CAACvF,IAAI,CAACjF,QAAQ,GAAG,CAAC;MAC1B;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAACyG,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAAC7H,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;QACtC,IAAI,CAAC4H,OAAO,CAAC3H,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MACtD;MACA,IAAI,IAAI,CAACmG,IAAI,EAAE;QACX,IAAI,CAACA,IAAI,CAACrG,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;QACnC,IAAI,CAACoG,IAAI,CAACnG,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MACnD;IACJ;EACJ;EACAmK,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAAChE,IAAI,EAAE;MACZ;IACJ;IACA,MAAMwF,OAAO,GAAG,IAAI,CAACrL,MAAM,CAACqL,OAAO;IACnC,IAAI,CAACxF,IAAI,CAACrG,SAAS,CAAC8L,MAAM,CAAC,SAAS,EAAE,CAAC,CAACD,OAAO,CAAC;IAChD,IAAIA,OAAO,KAAK3I,SAAS,EAAE;MACvB,IAAI,CAACmD,IAAI,CAACnG,YAAY,CAAC,MAAM,EAAE,kBAAkB,CAAC;MAClD,IAAI,CAACmG,IAAI,CAACnG,YAAY,CAAC,cAAc,EAAE2L,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;IACtE,CAAC,MACI;MACD,IAAI,CAACxF,IAAI,CAACnG,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MAC1C,IAAI,CAACmG,IAAI,CAACnG,YAAY,CAAC,cAAc,EAAE,EAAE,CAAC;IAC9C;EACJ;EACAoI,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,QAAQ;EACxB;EACAiC,UAAUA,CAAA,EAAG;IACT,MAAMyB,UAAU,GAAG,IAAI,CAAClE,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC7H,SAAS,CAACwD,QAAQ,CAAC,SAAS,CAAC;IAC7E,MAAMuD,OAAO,GAAGgF,UAAU,IAAI,IAAI,CAAC9C,SAAS,CAAC+C,wBAAwB,GAAG,IAAI,CAAC/C,SAAS,CAAC+C,wBAAwB,GAAG,IAAI,CAAC/C,SAAS,CAACjC,eAAe;IAChJ,MAAMC,OAAO,GAAG8E,UAAU,IAAI,IAAI,CAAC9C,SAAS,CAACgD,wBAAwB,GAAG,IAAI,CAAChD,SAAS,CAACgD,wBAAwB,GAAG/I,SAAS;IAC3H,MAAMsE,OAAO,GAAGuE,UAAU,IAAI,IAAI,CAAC9C,SAAS,CAACiD,oBAAoB,GAAG,aAAa,IAAI,CAACjD,SAAS,CAACiD,oBAAoB,EAAE,GAAG,EAAE;IAC3H,MAAMC,aAAa,GAAGJ,UAAU,IAAI,IAAI,CAAC9C,SAAS,CAACiD,oBAAoB,GAAG,MAAM,GAAG,EAAE;IACrF,IAAI,IAAI,CAAC7F,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAAC7B,KAAK,CAACiD,KAAK,GAAGV,OAAO,IAAI,EAAE;MACrC,IAAI,CAACV,IAAI,CAAC7B,KAAK,CAAC0C,eAAe,GAAGD,OAAO,IAAI,EAAE;MAC/C,IAAI,CAACZ,IAAI,CAAC7B,KAAK,CAACgD,OAAO,GAAGA,OAAO;MACjC,IAAI,CAACnB,IAAI,CAAC7B,KAAK,CAAC2H,aAAa,GAAGA,aAAa;IACjD;IACA,IAAI,IAAI,CAACtC,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACrF,KAAK,CAACiD,KAAK,GAAGV,OAAO,IAAI,EAAE;IAC1C;EACJ;AACJ;AACA,MAAM1E,yBAAyB,SAAS0G,sBAAsB,CAAC;EAC3DpJ,WAAWA,CAACa,MAAM,EAAE4L,cAAc,EAAE1L,UAAU,EAAE2L,cAAc,EAAEtM,UAAU,EAAE;IACxE,KAAK,CAACS,MAAM,EAAEA,MAAM,EAAE6L,cAAc,EAAEtM,UAAU,CAAC;IACjD,IAAI,CAACqM,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC1L,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC2L,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACjL,SAAS,CAAC,IAAIpC,eAAe,CAAC,CAAC,CAAC;IAC/D,IAAI,CAACsN,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,eAAe,GAAGJ,cAAc,IAAIA,cAAc,CAACI,eAAe,KAAKvJ,SAAS,GAAGmJ,cAAc,CAACI,eAAe,GAAG;MAAExI,UAAU,EAAEzE,mBAAmB,CAACkN,KAAK;MAAExI,QAAQ,EAAEzE,iBAAiB,CAACkN;IAAM,CAAC;IACrM,IAAI,CAACC,aAAa,GAAG,IAAI/N,gBAAgB,CAAC,MAAM;MAC5C,IAAI,IAAI,CAAC2N,SAAS,EAAE;QAChB,IAAI,CAACK,sBAAsB,CAAC,KAAK,CAAC;QAClC,IAAI,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7B;IACJ,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACC,aAAa,GAAG,IAAIlO,gBAAgB,CAAC,MAAM;MAC5C,IAAI,IAAI,CAACgJ,OAAO,IAAK,CAAC5J,UAAU,CAACF,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC8J,OAAO,CAAC,IAAI,IAAI,CAACnH,UAAU,CAACsM,OAAO,KAAK,IAAI,CAACV,SAAU,EAAE;QAC/G,IAAI,CAAC5L,UAAU,CAACmD,MAAM,CAAC2G,KAAK,CAAC,KAAK,CAAC;QACnC,IAAI,CAACqC,sBAAsB,CAAC,IAAI,CAAC;MACrC;IACJ,CAAC,EAAE,GAAG,CAAC;EACX;EACAnD,MAAMA,CAAC9J,SAAS,EAAE;IACd,KAAK,CAAC8J,MAAM,CAAC9J,SAAS,CAAC;IACvB,IAAI,CAAC,IAAI,CAACiI,OAAO,EAAE;MACf;IACJ;IACA,IAAI,IAAI,CAACxB,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACrG,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAC9C,IAAI,CAACoG,IAAI,CAACjF,QAAQ,GAAG,CAAC;MACtB,IAAI,CAACiF,IAAI,CAACnG,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MAC/C,IAAI,CAAC+M,kBAAkB,CAAC,OAAO,CAAC;MAChC,IAAI,CAACC,gBAAgB,GAAGxP,MAAM,CAAC,IAAI,CAAC2I,IAAI,EAAE7I,CAAC,CAAC,wBAAwB,GAAGwB,SAAS,CAAC8K,aAAa,CAAChL,OAAO,CAACqO,WAAW,CAAC,CAAC,CAAC;MACrH,IAAI,CAACD,gBAAgB,CAAChN,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC7D;IACA,IAAI,CAACoB,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAACoK,OAAO,EAAExK,SAAS,CAAC+P,MAAM,EAAE3L,CAAC,IAAI;MACtE,MAAMC,KAAK,GAAG,IAAIvD,qBAAqB,CAACsD,CAAC,CAAC;MAC1C,IAAIC,KAAK,CAACC,MAAM,CAAC,EAAE,CAAC,wBAAwB,CAAC,IAAID,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE;QAClF7D,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;QACzB,IAAI,CAACqL,aAAa,CAAC,IAAI,CAAC;MAC5B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACxL,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAACoK,OAAO,EAAExK,SAAS,CAACmE,QAAQ,EAAEC,CAAC,IAAI;MACxE,MAAMC,KAAK,GAAG,IAAIvD,qBAAqB,CAACsD,CAAC,CAAC;MAC1C,IAAI1D,gBAAgB,CAAC,CAAC,KAAK,IAAI,CAACsI,IAAI,EAAE;QAClC,IAAI3E,KAAK,CAACC,MAAM,CAAC,EAAE,CAAC,wBAAwB,CAAC,IAAID,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE;UAClF7D,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;QAC7B;MACJ;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACH,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAACoK,OAAO,EAAExK,SAAS,CAACgG,UAAU,EAAE5B,CAAC,IAAI;MAC1E,IAAI,CAAC,IAAI,CAAC+K,SAAS,EAAE;QACjB,IAAI,CAACA,SAAS,GAAG,IAAI;QACrB,IAAI,CAACI,aAAa,CAAC5C,QAAQ,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC1I,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAACoK,OAAO,EAAExK,SAAS,CAACgQ,WAAW,EAAE5L,CAAC,IAAI;MAC3E,IAAI,CAAC+K,SAAS,GAAG,KAAK;IAC1B,CAAC,CAAC,CAAC;IACH,IAAI,CAAClL,SAAS,CAAC7D,qBAAqB,CAAC,IAAI,CAACoK,OAAO,EAAExK,SAAS,CAACiQ,SAAS,EAAE7L,CAAC,IAAI;MACzE,IAAI,IAAI,CAACoG,OAAO,IAAI,CAAC5J,UAAU,CAACF,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC8J,OAAO,CAAC,EAAE;QAC/D,IAAI,CAACkF,aAAa,CAAC/C,QAAQ,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC1I,SAAS,CAAC,IAAI,CAACZ,UAAU,CAACmD,MAAM,CAAC+D,QAAQ,CAAC,MAAM;MACjD,IAAI,IAAI,CAAClH,UAAU,CAACsM,OAAO,KAAK,IAAI,CAACV,SAAS,EAAE;QAC5C,IAAI,CAAC5L,UAAU,CAACmD,MAAM,CAAC2G,KAAK,CAAC,KAAK,CAAC;QACnC,IAAI,CAACqC,sBAAsB,CAAC,IAAI,CAAC;MACrC;IACJ,CAAC,CAAC,CAAC;EACP;EACAzC,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;EAAA;EAEJ7H,OAAOA,CAACd,CAAC,EAAE;IACP;IACA3D,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;IACzB,IAAI,CAACoL,sBAAsB,CAAC,KAAK,CAAC;IAClC,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAC5B;EACAD,sBAAsBA,CAACU,KAAK,EAAE;IAC1B,IAAI,IAAI,CAAC7M,UAAU,CAACsM,OAAO,KAAKO,KAAK,IAAK,IAAI,CAAC7M,UAAU,CAACsM,OAAO,KAAK,IAAI,CAACV,SAAU,CAAC,EAAE;MACpF;MACA,IAAI;QACA,IAAI,CAAC5L,UAAU,CAACsM,OAAO,CAACQ,OAAO,CAAC,CAAC;MACrC,CAAC,CACD,MAAM,CAAE;MACR,IAAI,CAAC9M,UAAU,CAACsM,OAAO,GAAG9J,SAAS;MACnC,IAAI,CAAC+J,kBAAkB,CAAC,OAAO,CAAC;MAChC,IAAI,IAAI,CAACQ,gBAAgB,EAAE;QACvB,IAAI,CAAClB,kBAAkB,CAACmB,KAAK,CAAC,CAAC;QAC/B,IAAI,CAACD,gBAAgB,GAAGvK,SAAS;MACrC;IACJ;EACJ;EACAyK,0BAA0BA,CAACC,gBAAgB,EAAEZ,OAAO,EAAEa,KAAK,EAAEpB,eAAe,EAAE;IAC1E,MAAMqB,GAAG,GAAG;MAAEvI,GAAG,EAAE,CAAC;MAAEwI,IAAI,EAAE;IAAE,CAAC;IAC/B;IACAD,GAAG,CAACC,IAAI,GAAGvP,MAAM,CAACoP,gBAAgB,CAACI,KAAK,EAAEhB,OAAO,CAACgB,KAAK,EAAE;MAAEvJ,QAAQ,EAAEgI,eAAe,CAACxI,UAAU,KAAKzE,mBAAmB,CAACkN,KAAK,GAAG,CAAC,CAAC,oCAAoC,CAAC,CAAC;MAAkCuB,MAAM,EAAEJ,KAAK,CAACE,IAAI;MAAEG,IAAI,EAAEL,KAAK,CAACG;IAAM,CAAC,CAAC;IAClP;IACA,IAAIF,GAAG,CAACC,IAAI,IAAIF,KAAK,CAACE,IAAI,IAAID,GAAG,CAACC,IAAI,GAAGF,KAAK,CAACE,IAAI,GAAGF,KAAK,CAACG,KAAK,EAAE;MAC/D,IAAIH,KAAK,CAACE,IAAI,GAAG,EAAE,GAAGf,OAAO,CAACgB,KAAK,IAAIJ,gBAAgB,CAACI,KAAK,EAAE;QAC3DF,GAAG,CAACC,IAAI,GAAGF,KAAK,CAACE,IAAI,GAAG,EAAE;MAC9B;MACAF,KAAK,CAACtI,GAAG,IAAI,EAAE;MACfsI,KAAK,CAACM,MAAM,GAAG,CAAC;IACpB;IACA;IACAL,GAAG,CAACvI,GAAG,GAAG/G,MAAM,CAACoP,gBAAgB,CAACO,MAAM,EAAEnB,OAAO,CAACmB,MAAM,EAAE;MAAE1J,QAAQ,EAAE,CAAC,CAAC;MAAmCwJ,MAAM,EAAEJ,KAAK,CAACtI,GAAG;MAAE2I,IAAI,EAAE;IAAE,CAAC,CAAC;IACxI;IACA,IAAIJ,GAAG,CAACvI,GAAG,GAAGyH,OAAO,CAACmB,MAAM,KAAKN,KAAK,CAACtI,GAAG,IAAIuI,GAAG,CAACvI,GAAG,GAAGsI,KAAK,CAACM,MAAM,GAAGnB,OAAO,CAACmB,MAAM,IAAIP,gBAAgB,CAACO,MAAM,EAAE;MAC9GL,GAAG,CAACvI,GAAG,IAAIsI,KAAK,CAACM,MAAM;IAC3B;IACA,OAAOL,GAAG;EACd;EACAhB,aAAaA,CAACsB,eAAe,GAAG,IAAI,EAAE;IAClC,IAAI,CAAC,IAAI,CAACvG,OAAO,EAAE;MACf;IACJ;IACA,IAAI,CAAC,IAAI,CAACnH,UAAU,CAACsM,OAAO,EAAE;MAC1B,IAAI,CAACC,kBAAkB,CAAC,MAAM,CAAC;MAC/B,IAAI,CAACQ,gBAAgB,GAAG/P,MAAM,CAAC,IAAI,CAACmK,OAAO,EAAErK,CAAC,CAAC,oBAAoB,CAAC,CAAC;MACrE,IAAI,CAACiQ,gBAAgB,CAACzN,SAAS,CAACC,GAAG,CAAC,2BAA2B,EAAE,cAAc,CAAC;MAChF;MACA;MACA,MAAMoO,cAAc,GAAGrQ,SAAS,CAAC,IAAI,CAAC0C,UAAU,CAACmD,MAAM,CAACd,OAAO,CAAC,CAACuL,gBAAgB,CAAC,IAAI,CAAC5N,UAAU,CAACmD,MAAM,CAACd,OAAO,CAAC;MACjH,MAAMwL,UAAU,GAAGC,UAAU,CAACH,cAAc,CAACE,UAAU,IAAI,GAAG,CAAC,IAAI,CAAC;MACpE;MACA,IAAI,CAACd,gBAAgB,CAACjJ,KAAK,CAACiK,MAAM,GAAG,GAAG;MACxC,IAAI,CAAChB,gBAAgB,CAACjJ,KAAK,CAACC,QAAQ,GAAG,OAAO;MAC9C,IAAI,CAACgJ,gBAAgB,CAACjJ,KAAK,CAACe,GAAG,GAAG,GAAG;MACrC,IAAI,CAACkI,gBAAgB,CAACjJ,KAAK,CAACuJ,IAAI,GAAG,GAAG;MACtC,IAAI,CAACrN,UAAU,CAACsM,OAAO,GAAG,IAAItN,IAAI,CAAC,IAAI,CAAC+N,gBAAgB,EAAE,IAAI,CAACrB,cAAc,CAAChK,MAAM,GAAG,IAAI,CAACgK,cAAc,GAAG,CAAC,IAAI1N,kBAAkB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC2N,cAAc,EAAE,IAAI,CAACpD,SAAS,CAAC;MAC7K;MACA,MAAMyF,QAAQ,GAAG,IAAI,CAAC7G,OAAO,CAACvC,qBAAqB,CAAC,CAAC;MACrD,MAAMqJ,eAAe,GAAG;QACpBpJ,GAAG,EAAEmJ,QAAQ,CAACnJ,GAAG,GAAGgJ,UAAU;QAC9BR,IAAI,EAAEW,QAAQ,CAACX,IAAI;QACnBI,MAAM,EAAEO,QAAQ,CAACP,MAAM,GAAG,CAAC,GAAGI,UAAU;QACxCP,KAAK,EAAEU,QAAQ,CAACV;MACpB,CAAC;MACD,MAAMY,OAAO,GAAG,IAAI,CAACnB,gBAAgB,CAACnI,qBAAqB,CAAC,CAAC;MAC7D,MAAML,MAAM,GAAGjH,SAAS,CAAC,IAAI,CAAC6J,OAAO,CAAC;MACtC,MAAM;QAAEtC,GAAG;QAAEwI;MAAK,CAAC,GAAG,IAAI,CAACJ,0BAA0B,CAAC,IAAI9P,SAAS,CAACoH,MAAM,CAAC4J,UAAU,EAAE5J,MAAM,CAACI,WAAW,CAAC,EAAExH,SAAS,CAACiR,IAAI,CAACF,OAAO,CAAC,EAAED,eAAe,EAAE,IAAI,CAAClC,eAAe,CAAC;MAC3K;MACA,IAAI,CAACgB,gBAAgB,CAACjJ,KAAK,CAACuJ,IAAI,GAAG,GAAGA,IAAI,GAAGa,OAAO,CAACb,IAAI,IAAI;MAC7D,IAAI,CAACN,gBAAgB,CAACjJ,KAAK,CAACe,GAAG,GAAG,GAAGA,GAAG,GAAGqJ,OAAO,CAACrJ,GAAG,IAAI;MAC1D,IAAI,CAACgH,kBAAkB,CAACtM,GAAG,CAACxC,qBAAqB,CAAC,IAAI,CAACgQ,gBAAgB,EAAEpQ,SAAS,CAAC+P,MAAM,EAAE3L,CAAC,IAAI;QAC5F,MAAMC,KAAK,GAAG,IAAIvD,qBAAqB,CAACsD,CAAC,CAAC;QAC1C,IAAIC,KAAK,CAACC,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,EAAE;UAC1C7D,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;UACzB,IAAI,CAACf,UAAU,CAACmD,MAAM,CAAC2G,KAAK,CAAC,CAAC;UAC9B,IAAI,CAACqC,sBAAsB,CAAC,IAAI,CAAC;QACrC;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACN,kBAAkB,CAACtM,GAAG,CAACxC,qBAAqB,CAAC,IAAI,CAACgQ,gBAAgB,EAAEpQ,SAAS,CAACmE,QAAQ,EAAEC,CAAC,IAAI;QAC9F,MAAMC,KAAK,GAAG,IAAIvD,qBAAqB,CAACsD,CAAC,CAAC;QAC1C,IAAIC,KAAK,CAACC,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,EAAE;UAC1C7D,WAAW,CAACoE,IAAI,CAACT,CAAC,EAAE,IAAI,CAAC;QAC7B;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAAC8K,kBAAkB,CAACtM,GAAG,CAAC,IAAI,CAACS,UAAU,CAACsM,OAAO,CAAC+B,WAAW,CAAC,MAAM;QAClE,IAAI,CAACrO,UAAU,CAACmD,MAAM,CAAC2G,KAAK,CAAC,CAAC;QAC9B,IAAI,CAACqC,sBAAsB,CAAC,IAAI,CAAC;MACrC,CAAC,CAAC,CAAC;MACH,IAAI,CAACnM,UAAU,CAACsM,OAAO,CAACxC,KAAK,CAAC4D,eAAe,CAAC;MAC9C,IAAI,CAAC9B,SAAS,GAAG,IAAI,CAAC5L,UAAU,CAACsM,OAAO;IAC5C,CAAC,MACI;MACD,IAAI,CAACtM,UAAU,CAACsM,OAAO,CAACxC,KAAK,CAAC,KAAK,CAAC;IACxC;EACJ;EACAyC,kBAAkBA,CAAC+B,KAAK,EAAE;IACtB,IAAI,IAAI,CAAC3I,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,EAAEnG,YAAY,CAAC,eAAe,EAAE8O,KAAK,CAAC;IACnD;EACJ;EACA1E,UAAUA,CAAA,EAAG;IACT,KAAK,CAACA,UAAU,CAAC,CAAC;IAClB,MAAMyB,UAAU,GAAG,IAAI,CAAClE,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC7H,SAAS,CAACwD,QAAQ,CAAC,SAAS,CAAC;IAC7E,MAAMuD,OAAO,GAAGgF,UAAU,IAAI,IAAI,CAAC9C,SAAS,CAAC+C,wBAAwB,GAAG,IAAI,CAAC/C,SAAS,CAAC+C,wBAAwB,GAAG,IAAI,CAAC/C,SAAS,CAACjC,eAAe;IAChJ,IAAI,IAAI,CAACkG,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAAC1I,KAAK,CAACiD,KAAK,GAAGV,OAAO,IAAI,EAAE;IACrD;EACJ;EACAyG,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACT,aAAa,CAACS,OAAO,CAAC,CAAC;IAC5B,IAAI,IAAI,CAAClB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACkB,OAAO,CAAC,CAAC;MACxB,IAAI,CAAClB,SAAS,GAAG,IAAI;IACzB;IACA,IAAI,IAAI,CAACmB,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,GAAGvK,SAAS;IACrC;EACJ;AACJ;AACA,MAAMoD,2BAA2B,SAAShI,cAAc,CAAC;EACrDqB,WAAWA,CAACgB,OAAO,EAAEH,MAAM,EAAEV,OAAO,EAAEC,UAAU,EAAE;IAC9C,KAAK,CAACY,OAAO,EAAEH,MAAM,EAAEV,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACA2J,MAAMA,CAAC9J,SAAS,EAAE;IACd,KAAK,CAAC8J,MAAM,CAAC9J,SAAS,CAAC;IACvB,IAAI,IAAI,CAACqG,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACzB,KAAK,CAACyK,iBAAiB,GAAG,IAAI,CAAClP,UAAU,CAACmP,cAAc,GAAG,GAAG,IAAI,CAACnP,UAAU,CAACmP,cAAc,EAAE,GAAG,EAAE;IAClH;EACJ;AACJ;AACA,OAAO,SAAStE,aAAaA,CAAC3E,KAAK,EAAE;EACjC,MAAMkJ,KAAK,GAAG7P,mBAAmB;EACjC,MAAM6J,OAAO,GAAGgG,KAAK,CAAC/F,IAAI,CAACnD,KAAK,CAAC;EACjC,IAAI,CAACkD,OAAO,EAAE;IACV,OAAOlD,KAAK;EAChB;EACA,MAAMmJ,cAAc,GAAG,CAACjG,OAAO,CAAC,CAAC,CAAC;EAClC,OAAOlD,KAAK,CAAC4E,OAAO,CAACsE,KAAK,EAAEC,cAAc,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC7D,IAAI,CAAC,CAAC;AACpE;AACA,OAAO,SAAS8D,UAAUA,CAACC,CAAC,EAAE;EAC1B,MAAMC,aAAa,GAAGxQ,wBAAwB,CAAC,CAAC,CAACuQ,CAAC,CAAC1J,EAAE,CAAC;EACtD,OAAO,YAAY0J,CAAC,CAAC1J,EAAE,yBAAyB2J,aAAa,CAACC,QAAQ,CAAC,EAAE,CAAC,MAAM;AACpF;AACA,SAAS1I,gBAAgBA,CAACtC,KAAK,EAAEiL,cAAc,EAAE;EAC7C,IAAIC,MAAM,GAAG,SAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,EAAEL,UAAU,CAACvQ,OAAO,CAACiL,aAAa,CAAC;AACnC,EAAEsF,UAAU,CAACvQ,OAAO,CAACqO,WAAW,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;EACE,IAAIsC,cAAc,EAAE;IAChB;IACA;IACAC,MAAM,IAAI;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;IACK;IACA,MAAMC,oBAAoB,GAAGnL,KAAK,CAACoL,eAAe;IAClD,IAAID,oBAAoB,EAAE;MACtBD,MAAM,IAAI;AACtB;AACA,mBAAmBC,oBAAoB;AACvC;AACA;AACA;AACA,mBAAmBA,oBAAoB;AACvC;AACA;AACA;AACA,mBAAmBA,oBAAoB;AACvC;AACA,IAAI;IACI;IACA,MAAME,8BAA8B,GAAGrL,KAAK,CAACsL,yBAAyB;IACtE,IAAID,8BAA8B,EAAE;MAChCH,MAAM,IAAI;AACtB;AACA,mBAAmBG,8BAA8B;AACjD;AACA,IAAI;IACI;IACA,MAAME,mCAAmC,GAAGvL,KAAK,CAACwL,8BAA8B;IAChF,IAAID,mCAAmC,EAAE;MACrCL,MAAM,IAAI;AACtB;AACA,mBAAmBK,mCAAmC;AACtD;AACA,IAAI;IACI;IACA,MAAME,oCAAoC,GAAGzL,KAAK,CAAC0L,+BAA+B;IAClF,IAAID,oCAAoC,EAAE;MACtCP,MAAM,IAAI;AACtB;AACA,mBAAmBO,oCAAoC;AACvD;AACA,IAAI;IACI;EACJ;EACA,OAAOP,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}