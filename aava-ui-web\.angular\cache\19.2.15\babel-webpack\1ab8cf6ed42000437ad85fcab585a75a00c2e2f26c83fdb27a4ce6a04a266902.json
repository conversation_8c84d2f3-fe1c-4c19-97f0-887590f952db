{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"fileInput\"];\nfunction AgentStepperCardComponent_ava_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ava-icon\", 15);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"iconSize\", 16);\n  }\n}\nfunction AgentStepperCardComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCompleted);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const i_r5 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onFileInputClick(i_r5));\n    });\n    i0.ɵɵelement(1, \"ava-icon\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"title\", input_r4.inputType === \"image\" ? \"Attach Image\" : \"Attach File\")(\"disabled\", input_r4.inputType === \"text\" && ctx_r1.isInputDisabled(input_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 18);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"span\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template_button_click_3_listener() {\n      const fileIndex_r8 = i0.ɵɵrestoreView(_r7).index;\n      const i_r5 = i0.ɵɵnextContext(2).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r5, fileIndex_r8));\n    });\n    i0.ɵɵelement(4, \"ava-icon\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"iconSize\", 12);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template, 5, 2, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", input_r4.files);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"textarea\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_ngModelChange_4_listener($event) {\n      const input_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(input_r4.value, $event) || (input_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_input_4_listener($event) {\n      const i_r5 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onInputChange(i_r5, $event));\n    })(\"keydown.enter\", function AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_keydown_enter_4_listener($event) {\n      const i_r5 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.handleSendMessage(i_r5);\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(5, \"              \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template, 2, 3, \"button\", 27);\n    i0.ɵɵelementStart(7, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_div_14_div_1_div_2_Template_button_click_7_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleSendMessage(i_r5));\n    });\n    i0.ɵɵelement(8, \"ava-icon\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, AgentStepperCardComponent_div_14_div_1_div_2_div_9_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(input_r4.inputName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.isInputDisabled(input_r4));\n    i0.ɵɵtwoWayProperty(\"ngModel\", input_r4.value);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isInputDisabled(input_r4))(\"placeholder\", input_r4.inputType === \"image\" ? \"Please attach an image file\" : \"Enter \" + input_r4.inputName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showFileUploadButton(input_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !input_r4.value && (!input_r4.files || input_r4.files.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 18);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", input_r4.files && input_r4.files.length > 0);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_div_14_div_1_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleShowAllInputs());\n    });\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"ava-icon\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.showAllInputs ? \"Show Less\" : \"Show \" + ctx_r1.hiddenInputsCount + \" More\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconName\", ctx_r1.showAllInputs ? \"chevron-up\" : \"chevron-down\")(\"iconSize\", 14);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵtemplate(2, AgentStepperCardComponent_div_14_div_1_div_2_Template, 10, 10, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentStepperCardComponent_div_14_div_1_div_3_Template, 5, 3, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"scrollable\", ctx_r1.agent.inputs.length > ctx_r1.maxVisibleInputs);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.visibleInputs)(\"ngForTrackBy\", ctx_r1.trackByIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hiddenInputsCount > 0);\n  }\n}\nfunction AgentStepperCardComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, AgentStepperCardComponent_div_14_div_1_Template, 4, 5, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@slideDown\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agent.hasInputs && ctx_r1.agent.inputs);\n  }\n}\nexport let AgentStepperCardComponent = /*#__PURE__*/(() => {\n  class AgentStepperCardComponent {\n    agent;\n    stepNumber = 1;\n    isFirst = false;\n    isLast = false;\n    isActive = false;\n    isCompleted = false;\n    inputChanged = new EventEmitter();\n    fileSelected = new EventEmitter();\n    messageSent = new EventEmitter();\n    stepCompleted = new EventEmitter();\n    fileInput;\n    isExpanded = false;\n    showAllInputs = false;\n    maxVisibleInputs = 2;\n    get visibleInputs() {\n      if (!this.agent.inputs) return [];\n      if (this.showAllInputs || this.agent.inputs.length <= this.maxVisibleInputs) {\n        return this.agent.inputs;\n      }\n      return this.agent.inputs.slice(0, this.maxVisibleInputs);\n    }\n    get hiddenInputsCount() {\n      if (!this.agent.inputs) return 0;\n      return Math.max(0, this.agent.inputs.length - this.maxVisibleInputs);\n    }\n    get stepperClass() {\n      const classes = ['stepper-circle'];\n      if (this.isCompleted) classes.push('completed');else if (this.isActive) classes.push('active');else classes.push('inactive');\n      return classes.join(' ');\n    }\n    toggleExpanded() {\n      if (this.agent.hasInputs) {\n        this.isExpanded = !this.isExpanded;\n      }\n    }\n    toggleShowAllInputs() {\n      this.showAllInputs = !this.showAllInputs;\n    }\n    onInputChange(inputIndex, event) {\n      const target = event.target;\n      this.inputChanged.emit({\n        inputIndex,\n        value: target.value\n      });\n    }\n    onFileInputClick(inputIndex) {\n      // Store the input index for file selection\n      this.fileInput.nativeElement.dataset['inputIndex'] = inputIndex.toString();\n      this.fileInput.nativeElement.click();\n    }\n    onFileSelected(event) {\n      const target = event.target;\n      const files = target.files;\n      const inputIndex = parseInt(target.dataset['inputIndex'] || '0');\n      if (files && files.length > 0) {\n        const fileArray = Array.from(files);\n        this.fileSelected.emit({\n          inputIndex,\n          files: fileArray\n        });\n      }\n    }\n    removeFile(inputIndex, fileIndex) {\n      if (this.agent.inputs && this.agent.inputs[inputIndex] && this.agent.inputs[inputIndex].files) {\n        this.agent.inputs[inputIndex].files.splice(fileIndex, 1);\n        this.fileSelected.emit({\n          inputIndex,\n          files: this.agent.inputs[inputIndex].files\n        });\n      }\n    }\n    handleSendMessage(inputIndex) {\n      if (!this.agent.inputs || !this.agent.inputs[inputIndex]) return;\n      const input = this.agent.inputs[inputIndex];\n      const hasValue = input.value && input.value.trim().length > 0;\n      const hasFiles = input.files && input.files.length > 0;\n      if (!hasValue && !hasFiles) return;\n      // Create payload with input value\n      const payload = {\n        inputIndex,\n        value: input.value || '',\n        files: input.files\n      };\n      // Emit the message sent event\n      this.messageSent.emit(payload);\n      // Emit completion event (parent will handle marking as completed)\n      this.stepCompleted.emit();\n      // Don't clear the input after sending - keep the value visible\n      // input.value = '';\n      // if (input.files) {\n      //   input.files = [];\n      // }\n    }\n    getAcceptedFileType(input) {\n      return input.inputType === 'image' ? '.png,.jpg,.jpeg,.gif,.bmp,.svg' : '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n    }\n    isInputDisabled(input) {\n      return input.inputType === 'image';\n    }\n    showFileUploadButton(input) {\n      return input.inputType === 'image' || input.inputType === 'text';\n    }\n    trackByIndex(index, item) {\n      return index;\n    }\n    static ɵfac = function AgentStepperCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentStepperCardComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentStepperCardComponent,\n      selectors: [[\"app-agent-stepper-card\"]],\n      viewQuery: function AgentStepperCardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      inputs: {\n        agent: \"agent\",\n        stepNumber: \"stepNumber\",\n        isFirst: \"isFirst\",\n        isLast: \"isLast\",\n        isActive: \"isActive\",\n        isCompleted: \"isCompleted\"\n      },\n      outputs: {\n        inputChanged: \"inputChanged\",\n        fileSelected: \"fileSelected\",\n        messageSent: \"messageSent\",\n        stepCompleted: \"stepCompleted\"\n      },\n      decls: 17,\n      vars: 18,\n      consts: [[\"fileInput\", \"\"], [1, \"agent-stepper-card\"], [1, \"stepper-section\"], [\"iconName\", \"check\", \"iconColor\", \"white\", 3, \"iconSize\", 4, \"ngIf\"], [\"class\", \"stepper-line\", 3, \"active\", 4, \"ngIf\"], [1, \"card-content\"], [1, \"agent-header\", 3, \"click\"], [1, \"agent-info\"], [1, \"bot-icon\"], [\"iconName\", \"bot\", \"iconColor\", \"#0078E8\", 3, \"iconSize\"], [1, \"agent-name\", 3, \"title\"], [1, \"header-actions\"], [\"iconColor\", \"#444653\", 3, \"iconName\", \"iconSize\", \"title\", \"disabled\"], [\"class\", \"agent-details\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"change\"], [\"iconName\", \"check\", \"iconColor\", \"white\", 3, \"iconSize\"], [1, \"stepper-line\"], [1, \"agent-details\"], [\"class\", \"input-section\", 4, \"ngIf\"], [1, \"input-section\"], [1, \"inputs-container\"], [\"class\", \"input-field-container\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"show-more-section\", 4, \"ngIf\"], [1, \"input-field-container\"], [1, \"input-label\"], [1, \"input-container\"], [1, \"input-textarea\", 3, \"ngModelChange\", \"input\", \"keydown.enter\", \"ngModel\", \"disabled\", \"placeholder\"], [\"class\", \"attach-btn\", 3, \"title\", \"disabled\", \"click\", 4, \"ngIf\"], [\"title\", \"Send\", 1, \"send-btn\", 3, \"click\", \"disabled\"], [\"iconName\", \"send-horizontal\", \"iconColor\", \"#03ACC1\", 3, \"iconSize\"], [\"class\", \"uploaded-files\", 4, \"ngIf\"], [1, \"attach-btn\", 3, \"click\", \"title\", \"disabled\"], [\"iconName\", \"paperclip\", \"iconColor\", \"#03ACC1\", 3, \"iconSize\"], [1, \"uploaded-files\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [1, \"file-name\"], [\"title\", \"Remove file\", 1, \"remove-file\", 3, \"click\"], [\"iconName\", \"x\", \"iconColor\", \"#e53e3e\", 3, \"iconSize\"], [1, \"show-more-section\"], [1, \"show-more-btn\", 3, \"click\"], [\"iconColor\", \"#1A46A7\", 3, \"iconName\", \"iconSize\"]],\n      template: function AgentStepperCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\");\n          i0.ɵɵtemplate(3, AgentStepperCardComponent_ava_icon_3_Template, 1, 1, \"ava-icon\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, AgentStepperCardComponent_div_4_Template, 1, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_Template_div_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.agent.hasInputs ? ctx.toggleExpanded() : null);\n          });\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelement(9, \"ava-icon\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"h4\", 10);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 11);\n          i0.ɵɵelement(13, \"ava-icon\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, AgentStepperCardComponent_div_14_Template, 2, 2, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 14, 0);\n          i0.ɵɵlistener(\"change\", function AgentStepperCardComponent_Template_input_change_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"active\", ctx.isActive);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.stepperClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCompleted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLast);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"iconSize\", 20);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"title\", ctx.agent.name);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.agent.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"expanded\", ctx.isExpanded)(\"disabled\", !ctx.agent.hasInputs);\n          i0.ɵɵproperty(\"iconName\", ctx.isExpanded ? \"chevron-up\" : \"chevron-down\")(\"iconSize\", 16)(\"title\", !ctx.agent.hasInputs ? \"No Input Required\" : \"\")(\"disabled\", !ctx.agent.hasInputs);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExpanded);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, IconComponent],\n      styles: [\".agent-stepper-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: stretch;\\n  gap: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n.agent-stepper-card[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 2rem;\\n}\\n\\n.stepper-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 40px;\\n  flex-shrink: 0;\\n  min-height: 100%;\\n  padding-top: 24px;\\n  position: relative;\\n}\\n\\n.stepper-circle[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #0084FF;\\n  color: white;\\n  border: 2px solid #0084FF;\\n  box-shadow: 0 0 0 4px rgba(0, 132, 255, 0.1);\\n}\\n.stepper-circle.active[_ngcontent-%COMP%], .stepper-circle.completed[_ngcontent-%COMP%] {\\n  background: #0084ff;\\n  color: white;\\n  border-color: #0084ff;\\n  box-shadow: 0 0 0 4px rgba(0, 132, 255, 0.25);\\n}\\n.stepper-circle.inactive[_ngcontent-%COMP%] {\\n  background: #e5e7eb;\\n  color: #6b7280;\\n  border: 2px solid #d1d5db;\\n}\\n.stepper-circle[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n}\\n\\n.stepper-line[_ngcontent-%COMP%] {\\n  width: 2px;\\n  position: absolute;\\n  top: 56px; \\n\\n  left: 50%;\\n  transform: translateX(-50%);\\n  bottom: -24px; \\n\\n  background: linear-gradient(180deg, #0084FF 0%, #E6F3FF 100%);\\n  border: none;\\n}\\n.stepper-line.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #0084FF 0%, #0084FF 100%);\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  background: #e6f3ff;\\n  border: 1px solid #e6f3ff;\\n  border-radius: 16px;\\n  padding: 0;\\n  display: flex;\\n  flex-direction: column;\\n  transition: all 0.3s ease-in-out;\\n  overflow: hidden;\\n}\\n.card-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n.agent-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  padding: 16px;\\n  box-sizing: border-box;\\n}\\n.agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  flex: 1;\\n  min-width: 0;\\n  max-width: calc(100% - 40px);\\n}\\n.agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .bot-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: #fff;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n  border: 2px solid transparent;\\n  transition: border-color 0.3s ease;\\n}\\n.agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-weight: 600;\\n  font-size: 22px;\\n  line-height: 100%;\\n  letter-spacing: 0%;\\n  color: #000000;\\n  margin: 0;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  flex: 1;\\n  min-width: 0;\\n}\\n.agent-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.agent-stepper-card.active[_ngcontent-%COMP%]   .bot-icon[_ngcontent-%COMP%] {\\n  border-color: #0084ff;\\n}\\n\\n.agent-details[_ngcontent-%COMP%] {\\n  position: static;\\n  padding: 0 16px 16px 16px;\\n  background: #e6f3ff;\\n  animation: _ngcontent-%COMP%_slideDown 0.4s ease-in-out;\\n}\\n\\n.input-section[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.inputs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f7fafc;\\n  border-radius: 2px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e0;\\n  border-radius: 2px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a0aec0;\\n}\\n\\n.input-field-container[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1a202c;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 2px solid;\\n  border: 2px solid #03acc1;\\n  border-radius: 16px;\\n  padding: 12px;\\n  margin: 1rem 0;\\n  box-sizing: border-box;\\n  min-height: 80px;\\n  transition: border-color 0.2s ease;\\n}\\n.input-container[_ngcontent-%COMP%]:focus-within {\\n  border-color: #03ACC1;\\n  box-shadow: 0 0 0 3px rgba(3, 172, 193, 0.1);\\n}\\n.input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: none;\\n  resize: none;\\n  background: transparent;\\n  font-size: 14px;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  line-height: 1.4;\\n  outline: none;\\n  padding: 0;\\n  padding-right: 48px;\\n  box-sizing: border-box;\\n  min-height: 3em;\\n  max-height: 4.2em;\\n  overflow-y: auto;\\n  color: #1a202c;\\n}\\n.input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n.input-container[_ngcontent-%COMP%]   .input-textarea.disabled[_ngcontent-%COMP%] {\\n  background: #f7fafc;\\n  color: #a0aec0;\\n  cursor: not-allowed;\\n}\\n.input-container[_ngcontent-%COMP%]   .attach-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  left: 12px;\\n  background: none;\\n  border: none;\\n  padding: 4px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.input-container[_ngcontent-%COMP%]   .attach-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: scale(1.1);\\n}\\n.input-container[_ngcontent-%COMP%]   .attach-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  right: 12px;\\n  background: none;\\n  border: none;\\n  padding: 4px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: scale(1.1);\\n}\\n.input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.uploaded-files[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n.uploaded-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: #f7fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 6px;\\n  padding: 6px 10px;\\n  font-size: 12px;\\n}\\n.uploaded-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  color: #718096;\\n  max-width: 150px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.uploaded-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 16px;\\n  height: 16px;\\n  border: none;\\n  background: transparent;\\n  cursor: pointer;\\n  border-radius: 2px;\\n  transition: background-color 0.2s ease;\\n}\\n.uploaded-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%]:hover {\\n  background: #fed7d7;\\n}\\n\\n.show-more-section[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  text-align: center;\\n}\\n.show-more-section[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 6px;\\n  background: none;\\n  border: none;\\n  color: #1A46A7;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.show-more-section[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%]:hover {\\n  background: #ebf4ff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .agent-stepper-card[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 20px;\\n  }\\n  .stepper-section[_ngcontent-%COMP%] {\\n    width: 32px;\\n  }\\n  .stepper-circle[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    font-size: 12px;\\n  }\\n  .agent-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n  .agent-details[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .input-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%] {\\n    min-height: 36px;\\n    font-size: 13px;\\n  }\\n  .input-container[_ngcontent-%COMP%]   .attach-btn[_ngcontent-%COMP%], \\n   .input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      data: {\n        animation: [trigger('slideDown', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(-10px)'\n        }), animate('300ms ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0)'\n        }))])])]\n      }\n    });\n  }\n  return AgentStepperCardComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "FormsModule", "IconComponent", "trigger", "style", "transition", "animate", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵclassProp", "ctx_r1", "isCompleted", "ɵɵelementStart", "ɵɵlistener", "AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r6", "i_r5", "ɵɵnextContext", "index", "ɵɵresetView", "onFileInputClick", "ɵɵelementEnd", "input_r4", "inputType", "isInputDisabled", "ɵɵadvance", "ɵɵtext", "AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template_button_click_3_listener", "fileIndex_r8", "_r7", "removeFile", "ɵɵtextInterpolate", "file_r9", "name", "ɵɵtemplate", "AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template", "files", "ɵɵtwoWayListener", "AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_ngModelChange_4_listener", "$event", "_r3", "$implicit", "ɵɵtwoWayBindingSet", "value", "AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_input_4_listener", "onInputChange", "AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_keydown_enter_4_listener", "handleSendMessage", "preventDefault", "AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template", "AgentStepperCardComponent_div_14_div_1_div_2_Template_button_click_7_listener", "AgentStepperCardComponent_div_14_div_1_div_2_div_9_Template", "inputName", "ɵɵtwoWayProperty", "showFileUploadButton", "length", "AgentStepperCardComponent_div_14_div_1_div_3_Template_button_click_1_listener", "_r10", "toggleShowAllInputs", "showAllInputs", "hiddenInputsCount", "AgentStepperCardComponent_div_14_div_1_div_2_Template", "AgentStepperCardComponent_div_14_div_1_div_3_Template", "agent", "inputs", "maxVisibleInputs", "visibleInputs", "trackByIndex", "AgentStepperCardComponent_div_14_div_1_Template", "undefined", "hasInputs", "AgentStepperCardComponent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "isActive", "inputChanged", "fileSelected", "messageSent", "stepCompleted", "fileInput", "isExpanded", "slice", "Math", "max", "stepperClass", "classes", "push", "join", "toggleExpanded", "inputIndex", "event", "target", "emit", "nativeElement", "dataset", "toString", "click", "onFileSelected", "parseInt", "fileArray", "Array", "from", "fileIndex", "splice", "input", "hasValue", "trim", "hasFiles", "payload", "getAcceptedFileType", "item", "selectors", "viewQuery", "AgentStepperCardComponent_Query", "rf", "ctx", "AgentStepperCardComponent_ava_icon_3_Template", "AgentStepperCardComponent_div_4_Template", "AgentStepperCardComponent_Template_div_click_6_listener", "_r1", "AgentStepperCardComponent_div_14_Template", "AgentStepperCardComponent_Template_input_change_15_listener", "ɵɵclassMap", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles", "data", "animation", "opacity", "transform"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\agent-stepper-card\\agent-stepper-card.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\agent-stepper-card\\agent-stepper-card.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, EventEmitter, Input, Output, ViewChild, ElementRef } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\nimport { AgentData, AgentInput } from '../workflow-playground/workflow-playground.component';\r\nimport { trigger, state, style, transition, animate } from '@angular/animations';\r\n\r\n@Component({\r\n  selector: 'app-agent-stepper-card',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IconComponent\r\n  ],\r\n  templateUrl: './agent-stepper-card.component.html',\r\n  styleUrls: ['./agent-stepper-card.component.scss'],\r\n  animations: [\r\n    trigger('slideDown', [\r\n      transition(':enter', [\r\n        style({ opacity: 0, transform: 'translateY(-10px)' }),\r\n        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\r\n      ])\r\n    ])\r\n  ]\r\n})\r\nexport class AgentStepperCardComponent {\r\n  @Input() agent!: AgentData;\r\n  @Input() stepNumber: number = 1;\r\n  @Input() isFirst: boolean = false;\r\n  @Input() isLast: boolean = false;\r\n  @Input() isActive: boolean = false;\r\n  @Input() isCompleted: boolean = false;\r\n  \r\n  @Output() inputChanged = new EventEmitter<{inputIndex: number, value: string}>();\r\n  @Output() fileSelected = new EventEmitter<{inputIndex: number, files: File[]}>();\r\n  @Output() messageSent = new EventEmitter<{inputIndex: number, value: string, files?: File[]}>();\r\n  @Output() stepCompleted = new EventEmitter<void>();\r\n\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n\r\n  isExpanded: boolean = false;\r\n  showAllInputs: boolean = false;\r\n  maxVisibleInputs: number = 2;\r\n\r\n  get visibleInputs(): AgentInput[] {\r\n    if (!this.agent.inputs) return [];\r\n    if (this.showAllInputs || this.agent.inputs.length <= this.maxVisibleInputs) {\r\n      return this.agent.inputs;\r\n    }\r\n    return this.agent.inputs.slice(0, this.maxVisibleInputs);\r\n  }\r\n\r\n  get hiddenInputsCount(): number {\r\n    if (!this.agent.inputs) return 0;\r\n    return Math.max(0, this.agent.inputs.length - this.maxVisibleInputs);\r\n  }\r\n\r\n  get stepperClass(): string {\r\n    const classes = ['stepper-circle'];\r\n    if (this.isCompleted) classes.push('completed');\r\n    else if (this.isActive) classes.push('active');\r\n    else classes.push('inactive');\r\n    return classes.join(' ');\r\n  }\r\n\r\n  toggleExpanded(): void {\r\n    if (this.agent.hasInputs) {\r\n      this.isExpanded = !this.isExpanded;\r\n    }\r\n  }\r\n\r\n  toggleShowAllInputs(): void {\r\n    this.showAllInputs = !this.showAllInputs;\r\n  }\r\n\r\n  onInputChange(inputIndex: number, event: Event): void {\r\n    const target = event.target as HTMLTextAreaElement;\r\n    this.inputChanged.emit({ inputIndex, value: target.value });\r\n  }\r\n\r\n  onFileInputClick(inputIndex: number): void {\r\n    // Store the input index for file selection\r\n    this.fileInput.nativeElement.dataset['inputIndex'] = inputIndex.toString();\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const target = event.target as HTMLInputElement;\r\n    const files = target.files;\r\n    const inputIndex = parseInt(target.dataset['inputIndex'] || '0');\r\n    \r\n    if (files && files.length > 0) {\r\n      const fileArray = Array.from(files);\r\n      this.fileSelected.emit({ inputIndex, files: fileArray });\r\n    }\r\n  }\r\n\r\n  removeFile(inputIndex: number, fileIndex: number): void {\r\n    if (this.agent.inputs && this.agent.inputs[inputIndex] && this.agent.inputs[inputIndex].files) {\r\n      this.agent.inputs[inputIndex].files!.splice(fileIndex, 1);\r\n      this.fileSelected.emit({ inputIndex, files: this.agent.inputs[inputIndex].files! });\r\n    }\r\n  }\r\n\r\n  handleSendMessage(inputIndex: number): void {\r\n    if (!this.agent.inputs || !this.agent.inputs[inputIndex]) return;\r\n\r\n    const input = this.agent.inputs[inputIndex];\r\n    const hasValue = input.value && input.value.trim().length > 0;\r\n    const hasFiles = input.files && input.files.length > 0;\r\n\r\n    if (!hasValue && !hasFiles) return;\r\n\r\n    // Create payload with input value\r\n    const payload = {\r\n      inputIndex,\r\n      value: input.value || '',\r\n      files: input.files\r\n    };\r\n\r\n    // Emit the message sent event\r\n    this.messageSent.emit(payload);\r\n\r\n    // Emit completion event (parent will handle marking as completed)\r\n    this.stepCompleted.emit();\r\n\r\n    // Don't clear the input after sending - keep the value visible\r\n    // input.value = '';\r\n    // if (input.files) {\r\n    //   input.files = [];\r\n    // }\r\n  }\r\n\r\n  getAcceptedFileType(input: AgentInput): string {\r\n    return input.inputType === 'image' \r\n      ? '.png,.jpg,.jpeg,.gif,.bmp,.svg'\r\n      : '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\r\n  }\r\n\r\n  isInputDisabled(input: AgentInput): boolean {\r\n    return input.inputType === 'image';\r\n  }\r\n\r\n  showFileUploadButton(input: AgentInput): boolean {\r\n    return input.inputType === 'image' || input.inputType === 'text';\r\n  }\r\n\r\n  trackByIndex(index: number, item: any): number {\r\n    return index;\r\n  }\r\n}\r\n", "<div class=\"agent-stepper-card\" [class.active]=\"isActive\">\r\n  <!-- Stepper Section -->\r\n  <div class=\"stepper-section\">\r\n    <!-- Stepper Circle -->\r\n    <div [class]=\"stepperClass\">\r\n      <ava-icon \r\n        *ngIf=\"isCompleted\" \r\n        iconName=\"check\" \r\n        [iconSize]=\"16\" \r\n        iconColor=\"white\">\r\n      </ava-icon>\r\n    </div>\r\n    \r\n    <!-- Connector Line -->\r\n    <div *ngIf=\"!isLast\" class=\"stepper-line\" [class.active]=\"isCompleted\"></div>\r\n  </div>\r\n\r\n  <!-- Card Content -->\r\n  <div class=\"card-content\">\r\n    <!-- Agent Header -->\r\n    <div class=\"agent-header\" (click)=\"agent.hasInputs ? toggleExpanded() : null\">\r\n      <div class=\"agent-info\">\r\n        <div class=\"bot-icon\">\r\n          <ava-icon\r\n            iconName=\"bot\"\r\n            [iconSize]=\"20\"\r\n            iconColor=\"#0078E8\">\r\n          </ava-icon>\r\n        </div>\r\n        <h4 class=\"agent-name\" [title]=\"agent.name\">{{ agent.name }}</h4>\r\n      </div>\r\n\r\n      <div class=\"header-actions\">\r\n        <ava-icon\r\n          [class.expanded]=\"isExpanded\"\r\n          [iconName]=\"isExpanded ? 'chevron-up' : 'chevron-down'\"\r\n          [iconSize]=\"16\"\r\n          [title]=\"!agent.hasInputs ? 'No Input Required' : ''\"\r\n          [disabled]=\"!agent.hasInputs\"\r\n          [class.disabled]=\"!agent.hasInputs\"\r\n          iconColor=\"#444653\">\r\n        </ava-icon>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Expanded Content -->\r\n    <div class=\"agent-details\" *ngIf=\"isExpanded\" [@slideDown]>\r\n      <!-- Input Fields Only -->\r\n      <div class=\"input-section\" *ngIf=\"agent.hasInputs && agent.inputs\">\r\n        <div class=\"inputs-container\" [class.scrollable]=\"agent.inputs.length > maxVisibleInputs\">\r\n          <div \r\n            class=\"input-field-container\" \r\n            *ngFor=\"let input of visibleInputs; let i = index; trackBy: trackByIndex\">\r\n            \r\n            <label class=\"input-label\">{{ input.inputName }}</label>\r\n            \r\n            <div class=\"input-container\">\r\n              <textarea\r\n                [(ngModel)]=\"input.value\"\r\n                [disabled]=\"isInputDisabled(input)\"\r\n                (input)=\"onInputChange(i, $event)\"\r\n                (keydown.enter)=\"handleSendMessage(i); $event.preventDefault()\"\r\n                [placeholder]=\"input.inputType === 'image' ? 'Please attach an image file' : 'Enter ' + input.inputName\"\r\n                class=\"input-textarea\"\r\n                [class.disabled]=\"isInputDisabled(input)\">\r\n              </textarea>\r\n\r\n              <button\r\n                *ngIf=\"showFileUploadButton(input)\"\r\n                class=\"attach-btn\"\r\n                [title]=\"input.inputType === 'image' ? 'Attach Image' : 'Attach File'\"\r\n                [disabled]=\"input.inputType === 'text' && isInputDisabled(input)\"\r\n                (click)=\"onFileInputClick(i)\">\r\n                <ava-icon\r\n                  iconName=\"paperclip\"\r\n                  [iconSize]=\"18\"\r\n                  iconColor=\"#03ACC1\">\r\n                </ava-icon>\r\n              </button>\r\n\r\n              <button\r\n                class=\"send-btn\"\r\n                title=\"Send\"\r\n                (click)=\"handleSendMessage(i)\"\r\n                [disabled]=\"!input.value && (!input.files || input.files.length === 0)\">\r\n                <ava-icon\r\n                  iconName=\"send-horizontal\"\r\n                  [iconSize]=\"18\"\r\n                  iconColor=\"#03ACC1\">\r\n                </ava-icon>\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Display uploaded files -->\r\n            <div class=\"uploaded-files\" *ngIf=\"input.files && input.files.length > 0\">\r\n              <div\r\n                class=\"file-item\"\r\n                *ngFor=\"let file of input.files; let fileIndex = index\">\r\n                <span class=\"file-name\">{{ file.name }}</span>\r\n                <button\r\n                  class=\"remove-file\"\r\n                  (click)=\"removeFile(i, fileIndex)\"\r\n                  title=\"Remove file\">\r\n                  <ava-icon iconName=\"x\" [iconSize]=\"12\" iconColor=\"#e53e3e\"></ava-icon>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Show More/Less Toggle -->\r\n        <div class=\"show-more-section\" *ngIf=\"hiddenInputsCount > 0\">\r\n          <button class=\"show-more-btn\" (click)=\"toggleShowAllInputs()\">\r\n            <span>{{ showAllInputs ? 'Show Less' : 'Show ' + hiddenInputsCount + ' More' }}</span>\r\n            <ava-icon\r\n              [iconName]=\"showAllInputs ? 'chevron-up' : 'chevron-down'\"\r\n              [iconSize]=\"14\"\r\n              iconColor=\"#1A46A7\">\r\n            </ava-icon>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Hidden file input -->\r\n  <input\r\n    #fileInput\r\n    type=\"file\"\r\n    style=\"display: none\"\r\n    multiple\r\n    (change)=\"onFileSelected($event)\">\r\n</div>\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAoBC,YAAY,QAA8C,eAAe;AAC7F,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,OAAO,EAASC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;;;;;;;ICA1EC,EAAA,CAAAC,SAAA,mBAKW;;;IAFTD,EAAA,CAAAE,UAAA,gBAAe;;;;;IAMnBF,EAAA,CAAAC,SAAA,cAA6E;;;;IAAnCD,EAAA,CAAAG,WAAA,WAAAC,MAAA,CAAAC,WAAA,CAA4B;;;;;;IAqD5DL,EAAA,CAAAM,cAAA,iBAKgC;IAA9BN,EAAA,CAAAO,UAAA,mBAAAC,uFAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAW,gBAAA,CAAAJ,IAAA,CAAmB;IAAA,EAAC;IAC7BX,EAAA,CAAAC,SAAA,mBAIW;IACbD,EAAA,CAAAgB,YAAA,EAAS;;;;;IAPPhB,EADA,CAAAE,UAAA,UAAAe,QAAA,CAAAC,SAAA,8CAAsE,aAAAD,QAAA,CAAAC,SAAA,eAAAd,MAAA,CAAAe,eAAA,CAAAF,QAAA,EACL;IAI/DjB,EAAA,CAAAoB,SAAA,EAAe;IAAfpB,EAAA,CAAAE,UAAA,gBAAe;;;;;;IAuBjBF,EAHF,CAAAM,cAAA,cAE0D,eAChC;IAAAN,EAAA,CAAAqB,MAAA,GAAe;IAAArB,EAAA,CAAAgB,YAAA,EAAO;IAC9ChB,EAAA,CAAAM,cAAA,iBAGsB;IADpBN,EAAA,CAAAO,UAAA,mBAAAe,0FAAA;MAAA,MAAAC,YAAA,GAAAvB,EAAA,CAAAS,aAAA,CAAAe,GAAA,EAAAX,KAAA;MAAA,MAAAF,IAAA,GAAAX,EAAA,CAAAY,aAAA,IAAAC,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAqB,UAAA,CAAAd,IAAA,EAAAY,YAAA,CAAwB;IAAA,EAAC;IAElCvB,EAAA,CAAAC,SAAA,mBAAsE;IAE1ED,EADE,CAAAgB,YAAA,EAAS,EACL;;;;IAPoBhB,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAC,IAAA,CAAe;IAKd5B,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAAE,UAAA,gBAAe;;;;;IAT5CF,EAAA,CAAAM,cAAA,cAA0E;IACxEN,EAAA,CAAA6B,UAAA,IAAAC,iEAAA,kBAE0D;IAS5D9B,EAAA,CAAAgB,YAAA,EAAM;;;;IATehB,EAAA,CAAAoB,SAAA,EAAgB;IAAhBpB,EAAA,CAAAE,UAAA,YAAAe,QAAA,CAAAc,KAAA,CAAgB;;;;;;IA3CrC/B,EAJF,CAAAM,cAAA,cAE4E,gBAE/C;IAAAN,EAAA,CAAAqB,MAAA,GAAqB;IAAArB,EAAA,CAAAgB,YAAA,EAAQ;IAGtDhB,EADF,CAAAM,cAAA,cAA6B,mBAQiB;IAN1CN,EAAA,CAAAgC,gBAAA,2BAAAC,wFAAAC,MAAA;MAAA,MAAAjB,QAAA,GAAAjB,EAAA,CAAAS,aAAA,CAAA0B,GAAA,EAAAC,SAAA;MAAApC,EAAA,CAAAqC,kBAAA,CAAApB,QAAA,CAAAqB,KAAA,EAAAJ,MAAA,MAAAjB,QAAA,CAAAqB,KAAA,GAAAJ,MAAA;MAAA,OAAAlC,EAAA,CAAAc,WAAA,CAAAoB,MAAA;IAAA,EAAyB;IAGzBlC,EADA,CAAAO,UAAA,mBAAAgC,gFAAAL,MAAA;MAAA,MAAAvB,IAAA,GAAAX,EAAA,CAAAS,aAAA,CAAA0B,GAAA,EAAAtB,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAoC,aAAA,CAAA7B,IAAA,EAAAuB,MAAA,CAAwB;IAAA,EAAC,2BAAAO,wFAAAP,MAAA;MAAA,MAAAvB,IAAA,GAAAX,EAAA,CAAAS,aAAA,CAAA0B,GAAA,EAAAtB,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MACjBR,MAAA,CAAAsC,iBAAA,CAAA/B,IAAA,CAAoB;MAAA,OAAAX,EAAA,CAAAc,WAAA,CAAEoB,MAAA,CAAAS,cAAA,EAAuB;IAAA,EAAC;IAIjE3C,EAAA,CAAAqB,MAAA;IAAArB,EAAA,CAAAgB,YAAA,EAAW;IAEXhB,EAAA,CAAA6B,UAAA,IAAAe,8DAAA,qBAKgC;IAQhC5C,EAAA,CAAAM,cAAA,iBAI0E;IADxEN,EAAA,CAAAO,UAAA,mBAAAsC,8EAAA;MAAA,MAAAlC,IAAA,GAAAX,EAAA,CAAAS,aAAA,CAAA0B,GAAA,EAAAtB,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAsC,iBAAA,CAAA/B,IAAA,CAAoB;IAAA,EAAC;IAE9BX,EAAA,CAAAC,SAAA,mBAIW;IAEfD,EADE,CAAAgB,YAAA,EAAS,EACL;IAGNhB,EAAA,CAAA6B,UAAA,IAAAiB,2DAAA,kBAA0E;IAa5E9C,EAAA,CAAAgB,YAAA,EAAM;;;;;IArDuBhB,EAAA,CAAAoB,SAAA,GAAqB;IAArBpB,EAAA,CAAA0B,iBAAA,CAAAT,QAAA,CAAA8B,SAAA,CAAqB;IAU5C/C,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAAG,WAAA,aAAAC,MAAA,CAAAe,eAAA,CAAAF,QAAA,EAAyC;IANzCjB,EAAA,CAAAgD,gBAAA,YAAA/B,QAAA,CAAAqB,KAAA,CAAyB;IAIzBtC,EAHA,CAAAE,UAAA,aAAAE,MAAA,CAAAe,eAAA,CAAAF,QAAA,EAAmC,gBAAAA,QAAA,CAAAC,SAAA,0DAAAD,QAAA,CAAA8B,SAAA,CAGqE;IAMvG/C,EAAA,CAAAoB,SAAA,GAAiC;IAAjCpB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAA6C,oBAAA,CAAAhC,QAAA,EAAiC;IAgBlCjB,EAAA,CAAAoB,SAAA,EAAuE;IAAvEpB,EAAA,CAAAE,UAAA,cAAAe,QAAA,CAAAqB,KAAA,MAAArB,QAAA,CAAAc,KAAA,IAAAd,QAAA,CAAAc,KAAA,CAAAmB,MAAA,QAAuE;IAGrElD,EAAA,CAAAoB,SAAA,EAAe;IAAfpB,EAAA,CAAAE,UAAA,gBAAe;IAOQF,EAAA,CAAAoB,SAAA,EAA2C;IAA3CpB,EAAA,CAAAE,UAAA,SAAAe,QAAA,CAAAc,KAAA,IAAAd,QAAA,CAAAc,KAAA,CAAAmB,MAAA,KAA2C;;;;;;IAkB1ElD,EADF,CAAAM,cAAA,cAA6D,iBACG;IAAhCN,EAAA,CAAAO,UAAA,mBAAA4C,8EAAA;MAAAnD,EAAA,CAAAS,aAAA,CAAA2C,IAAA;MAAA,MAAAhD,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAiD,mBAAA,EAAqB;IAAA,EAAC;IAC3DrD,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAqB,MAAA,GAAyE;IAAArB,EAAA,CAAAgB,YAAA,EAAO;IACtFhB,EAAA,CAAAC,SAAA,mBAIW;IAEfD,EADE,CAAAgB,YAAA,EAAS,EACL;;;;IAPIhB,EAAA,CAAAoB,SAAA,GAAyE;IAAzEpB,EAAA,CAAA0B,iBAAA,CAAAtB,MAAA,CAAAkD,aAAA,2BAAAlD,MAAA,CAAAmD,iBAAA,WAAyE;IAE7EvD,EAAA,CAAAoB,SAAA,EAA0D;IAC1DpB,EADA,CAAAE,UAAA,aAAAE,MAAA,CAAAkD,aAAA,iCAA0D,gBAC3C;;;;;IAnErBtD,EADF,CAAAM,cAAA,cAAmE,cACyB;IACxFN,EAAA,CAAA6B,UAAA,IAAA2B,qDAAA,oBAE4E;IAwD9ExD,EAAA,CAAAgB,YAAA,EAAM;IAGNhB,EAAA,CAAA6B,UAAA,IAAA4B,qDAAA,kBAA6D;IAU/DzD,EAAA,CAAAgB,YAAA,EAAM;;;;IAxE0BhB,EAAA,CAAAoB,SAAA,EAA2D;IAA3DpB,EAAA,CAAAG,WAAA,eAAAC,MAAA,CAAAsD,KAAA,CAAAC,MAAA,CAAAT,MAAA,GAAA9C,MAAA,CAAAwD,gBAAA,CAA2D;IAGnE5D,EAAA,CAAAoB,SAAA,EAAkB;IAAepB,EAAjC,CAAAE,UAAA,YAAAE,MAAA,CAAAyD,aAAA,CAAkB,iBAAAzD,MAAA,CAAA0D,YAAA,CAAoC;IA2D5C9D,EAAA,CAAAoB,SAAA,EAA2B;IAA3BpB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAmD,iBAAA,KAA2B;;;;;IAjE/DvD,EAAA,CAAAM,cAAA,cAA2D;IAEzDN,EAAA,CAAA6B,UAAA,IAAAkC,+CAAA,kBAAmE;IA0ErE/D,EAAA,CAAAgB,YAAA,EAAM;;;;IA5EwChB,EAAA,CAAAE,UAAA,eAAA8D,SAAA,CAAY;IAE5BhE,EAAA,CAAAoB,SAAA,EAAqC;IAArCpB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAsD,KAAA,CAAAO,SAAA,IAAA7D,MAAA,CAAAsD,KAAA,CAAAC,MAAA,CAAqC;;;ADtBvE,WAAaO,yBAAyB;EAAhC,MAAOA,yBAAyB;IAC3BR,KAAK;IACLS,UAAU,GAAW,CAAC;IACtBC,OAAO,GAAY,KAAK;IACxBC,MAAM,GAAY,KAAK;IACvBC,QAAQ,GAAY,KAAK;IACzBjE,WAAW,GAAY,KAAK;IAE3BkE,YAAY,GAAG,IAAI9E,YAAY,EAAuC;IACtE+E,YAAY,GAAG,IAAI/E,YAAY,EAAuC;IACtEgF,WAAW,GAAG,IAAIhF,YAAY,EAAuD;IACrFiF,aAAa,GAAG,IAAIjF,YAAY,EAAQ;IAE1BkF,SAAS;IAEjCC,UAAU,GAAY,KAAK;IAC3BtB,aAAa,GAAY,KAAK;IAC9BM,gBAAgB,GAAW,CAAC;IAE5B,IAAIC,aAAaA,CAAA;MACf,IAAI,CAAC,IAAI,CAACH,KAAK,CAACC,MAAM,EAAE,OAAO,EAAE;MACjC,IAAI,IAAI,CAACL,aAAa,IAAI,IAAI,CAACI,KAAK,CAACC,MAAM,CAACT,MAAM,IAAI,IAAI,CAACU,gBAAgB,EAAE;QAC3E,OAAO,IAAI,CAACF,KAAK,CAACC,MAAM;MAC1B;MACA,OAAO,IAAI,CAACD,KAAK,CAACC,MAAM,CAACkB,KAAK,CAAC,CAAC,EAAE,IAAI,CAACjB,gBAAgB,CAAC;IAC1D;IAEA,IAAIL,iBAAiBA,CAAA;MACnB,IAAI,CAAC,IAAI,CAACG,KAAK,CAACC,MAAM,EAAE,OAAO,CAAC;MAChC,OAAOmB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACrB,KAAK,CAACC,MAAM,CAACT,MAAM,GAAG,IAAI,CAACU,gBAAgB,CAAC;IACtE;IAEA,IAAIoB,YAAYA,CAAA;MACd,MAAMC,OAAO,GAAG,CAAC,gBAAgB,CAAC;MAClC,IAAI,IAAI,CAAC5E,WAAW,EAAE4E,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAAC,KAC3C,IAAI,IAAI,CAACZ,QAAQ,EAAEW,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAC1CD,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;MAC7B,OAAOD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;IAC1B;IAEAC,cAAcA,CAAA;MACZ,IAAI,IAAI,CAAC1B,KAAK,CAACO,SAAS,EAAE;QACxB,IAAI,CAACW,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MACpC;IACF;IAEAvB,mBAAmBA,CAAA;MACjB,IAAI,CAACC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IAC1C;IAEAd,aAAaA,CAAC6C,UAAkB,EAAEC,KAAY;MAC5C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAA6B;MAClD,IAAI,CAAChB,YAAY,CAACiB,IAAI,CAAC;QAAEH,UAAU;QAAE/C,KAAK,EAAEiD,MAAM,CAACjD;MAAK,CAAE,CAAC;IAC7D;IAEAvB,gBAAgBA,CAACsE,UAAkB;MACjC;MACA,IAAI,CAACV,SAAS,CAACc,aAAa,CAACC,OAAO,CAAC,YAAY,CAAC,GAAGL,UAAU,CAACM,QAAQ,EAAE;MAC1E,IAAI,CAAChB,SAAS,CAACc,aAAa,CAACG,KAAK,EAAE;IACtC;IAEAC,cAAcA,CAACP,KAAY;MACzB,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAA0B;MAC/C,MAAMxD,KAAK,GAAGwD,MAAM,CAACxD,KAAK;MAC1B,MAAMsD,UAAU,GAAGS,QAAQ,CAACP,MAAM,CAACG,OAAO,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC;MAEhE,IAAI3D,KAAK,IAAIA,KAAK,CAACmB,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAM6C,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAClE,KAAK,CAAC;QACnC,IAAI,CAACyC,YAAY,CAACgB,IAAI,CAAC;UAAEH,UAAU;UAAEtD,KAAK,EAAEgE;QAAS,CAAE,CAAC;MAC1D;IACF;IAEAtE,UAAUA,CAAC4D,UAAkB,EAAEa,SAAiB;MAC9C,IAAI,IAAI,CAACxC,KAAK,CAACC,MAAM,IAAI,IAAI,CAACD,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,IAAI,IAAI,CAAC3B,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,CAACtD,KAAK,EAAE;QAC7F,IAAI,CAAC2B,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,CAACtD,KAAM,CAACoE,MAAM,CAACD,SAAS,EAAE,CAAC,CAAC;QACzD,IAAI,CAAC1B,YAAY,CAACgB,IAAI,CAAC;UAAEH,UAAU;UAAEtD,KAAK,EAAE,IAAI,CAAC2B,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,CAACtD;QAAM,CAAE,CAAC;MACrF;IACF;IAEAW,iBAAiBA,CAAC2C,UAAkB;MAClC,IAAI,CAAC,IAAI,CAAC3B,KAAK,CAACC,MAAM,IAAI,CAAC,IAAI,CAACD,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,EAAE;MAE1D,MAAMe,KAAK,GAAG,IAAI,CAAC1C,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC;MAC3C,MAAMgB,QAAQ,GAAGD,KAAK,CAAC9D,KAAK,IAAI8D,KAAK,CAAC9D,KAAK,CAACgE,IAAI,EAAE,CAACpD,MAAM,GAAG,CAAC;MAC7D,MAAMqD,QAAQ,GAAGH,KAAK,CAACrE,KAAK,IAAIqE,KAAK,CAACrE,KAAK,CAACmB,MAAM,GAAG,CAAC;MAEtD,IAAI,CAACmD,QAAQ,IAAI,CAACE,QAAQ,EAAE;MAE5B;MACA,MAAMC,OAAO,GAAG;QACdnB,UAAU;QACV/C,KAAK,EAAE8D,KAAK,CAAC9D,KAAK,IAAI,EAAE;QACxBP,KAAK,EAAEqE,KAAK,CAACrE;OACd;MAED;MACA,IAAI,CAAC0C,WAAW,CAACe,IAAI,CAACgB,OAAO,CAAC;MAE9B;MACA,IAAI,CAAC9B,aAAa,CAACc,IAAI,EAAE;MAEzB;MACA;MACA;MACA;MACA;IACF;IAEAiB,mBAAmBA,CAACL,KAAiB;MACnC,OAAOA,KAAK,CAAClF,SAAS,KAAK,OAAO,GAC9B,gCAAgC,GAChC,gFAAgF;IACtF;IAEAC,eAAeA,CAACiF,KAAiB;MAC/B,OAAOA,KAAK,CAAClF,SAAS,KAAK,OAAO;IACpC;IAEA+B,oBAAoBA,CAACmD,KAAiB;MACpC,OAAOA,KAAK,CAAClF,SAAS,KAAK,OAAO,IAAIkF,KAAK,CAAClF,SAAS,KAAK,MAAM;IAClE;IAEA4C,YAAYA,CAACjD,KAAa,EAAE6F,IAAS;MACnC,OAAO7F,KAAK;IACd;;uCA5HWqD,yBAAyB;IAAA;;YAAzBA,yBAAyB;MAAAyC,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCtBlC9G,EAJJ,CAAAM,cAAA,aAA0D,aAE3B,UAEC;UAC1BN,EAAA,CAAA6B,UAAA,IAAAmF,6CAAA,sBAIoB;UAEtBhH,EAAA,CAAAgB,YAAA,EAAM;UAGNhB,EAAA,CAAA6B,UAAA,IAAAoF,wCAAA,iBAAuE;UACzEjH,EAAA,CAAAgB,YAAA,EAAM;UAKJhB,EAFF,CAAAM,cAAA,aAA0B,aAEsD;UAApDN,EAAA,CAAAO,UAAA,mBAAA2G,wDAAA;YAAAlH,EAAA,CAAAS,aAAA,CAAA0G,GAAA;YAAA,OAAAnH,EAAA,CAAAc,WAAA,CAAAiG,GAAA,CAAArD,KAAA,CAAAO,SAAA,GAA2B8C,GAAA,CAAA3B,cAAA,EAAgB,GAAG,IAAI;UAAA,EAAC;UAEzEpF,EADF,CAAAM,cAAA,aAAwB,aACA;UACpBN,EAAA,CAAAC,SAAA,kBAIW;UACbD,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAM,cAAA,cAA4C;UAAAN,EAAA,CAAAqB,MAAA,IAAgB;UAC9DrB,EAD8D,CAAAgB,YAAA,EAAK,EAC7D;UAENhB,EAAA,CAAAM,cAAA,eAA4B;UAC1BN,EAAA,CAAAC,SAAA,oBAQW;UAEfD,EADE,CAAAgB,YAAA,EAAM,EACF;UAGNhB,EAAA,CAAA6B,UAAA,KAAAuF,yCAAA,kBAA2D;UA6E7DpH,EAAA,CAAAgB,YAAA,EAAM;UAGNhB,EAAA,CAAAM,cAAA,oBAKoC;UAAlCN,EAAA,CAAAO,UAAA,oBAAA8G,4DAAAnF,MAAA;YAAAlC,EAAA,CAAAS,aAAA,CAAA0G,GAAA;YAAA,OAAAnH,EAAA,CAAAc,WAAA,CAAUiG,GAAA,CAAAlB,cAAA,CAAA3D,MAAA,CAAsB;UAAA,EAAC;UACrClC,EANE,CAAAgB,YAAA,EAKoC,EAChC;;;UApI0BhB,EAAA,CAAAG,WAAA,WAAA4G,GAAA,CAAAzC,QAAA,CAAyB;UAIhDtE,EAAA,CAAAoB,SAAA,GAAsB;UAAtBpB,EAAA,CAAAsH,UAAA,CAAAP,GAAA,CAAA/B,YAAA,CAAsB;UAEtBhF,EAAA,CAAAoB,SAAA,EAAiB;UAAjBpB,EAAA,CAAAE,UAAA,SAAA6G,GAAA,CAAA1G,WAAA,CAAiB;UAQhBL,EAAA,CAAAoB,SAAA,EAAa;UAAbpB,EAAA,CAAAE,UAAA,UAAA6G,GAAA,CAAA1C,MAAA,CAAa;UAWXrE,EAAA,CAAAoB,SAAA,GAAe;UAAfpB,EAAA,CAAAE,UAAA,gBAAe;UAIIF,EAAA,CAAAoB,SAAA,EAAoB;UAApBpB,EAAA,CAAAE,UAAA,UAAA6G,GAAA,CAAArD,KAAA,CAAA9B,IAAA,CAAoB;UAAC5B,EAAA,CAAAoB,SAAA,EAAgB;UAAhBpB,EAAA,CAAA0B,iBAAA,CAAAqF,GAAA,CAAArD,KAAA,CAAA9B,IAAA,CAAgB;UAK1D5B,EAAA,CAAAoB,SAAA,GAA6B;UAK7BpB,EALA,CAAAG,WAAA,aAAA4G,GAAA,CAAAnC,UAAA,CAA6B,cAAAmC,GAAA,CAAArD,KAAA,CAAAO,SAAA,CAKM;UADnCjE,EAHA,CAAAE,UAAA,aAAA6G,GAAA,CAAAnC,UAAA,iCAAuD,gBACxC,WAAAmC,GAAA,CAAArD,KAAA,CAAAO,SAAA,4BACsC,cAAA8C,GAAA,CAAArD,KAAA,CAAAO,SAAA,CACxB;UAQPjE,EAAA,CAAAoB,SAAA,EAAgB;UAAhBpB,EAAA,CAAAE,UAAA,SAAA6G,GAAA,CAAAnC,UAAA,CAAgB;;;qBDnC5CpF,YAAY,EAAA+H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ/H,WAAW,EAAAgI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXlI,aAAa;MAAAmI,MAAA;MAAAC,IAAA;QAAAC,SAAA,EAIH,CACVpI,OAAO,CAAC,WAAW,EAAE,CACnBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;UAAEoI,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAE,CAAC,EACrDnI,OAAO,CAAC,gBAAgB,EAAEF,KAAK,CAAC;UAAEoI,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC7E,CAAC,CACH,CAAC;MACH;IAAA;;SAEUhE,yBAAyB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}