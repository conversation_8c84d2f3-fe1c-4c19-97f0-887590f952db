{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createSingleCallFunction } from '../../../../base/common/functional.js';\nconst charTable = {\n  '0': 0,\n  '1': 1,\n  '2': 2,\n  '3': 3,\n  '4': 4,\n  '5': 5,\n  '6': 6,\n  '7': 7,\n  '8': 8,\n  '9': 9,\n  A: 10,\n  B: 11,\n  C: 12,\n  D: 13,\n  E: 14,\n  F: 15\n};\nconst decodeData = str => {\n  const output = new Uint8ClampedArray(str.length / 2);\n  for (let i = 0; i < str.length; i += 2) {\n    output[i >> 1] = charTable[str[i]] << 4 | charTable[str[i + 1]] & 0xF;\n  }\n  return output;\n};\n/*\nconst encodeData = (data: Uint8ClampedArray, length: string) => {\n    const chars = '0123456789ABCDEF';\n    let output = '';\n    for (let i = 0; i < data.length; i++) {\n        output += chars[data[i] >> 4] + chars[data[i] & 0xf];\n    }\n    return output;\n};\n*/\n/**\n * Map of minimap scales to prebaked sample data at those scales. We don't\n * sample much larger data, because then font family becomes visible, which\n * is use-configurable.\n */\nexport const prebakedMiniMaps = {\n  1: createSingleCallFunction(() => decodeData('0000511D6300CF609C709645A78432005642574171487021003C451900274D35D762755E8B629C5BA856AF57BA649530C167D1512A272A3F6038604460398526BCA2A968DB6F8957C768BE5FBE2FB467CF5D8D5B795DC7625B5DFF50DE64C466DB2FC47CD860A65E9A2EB96CB54CE06DA763AB2EA26860524D3763536601005116008177A8705E53AB738E6A982F88BAA35B5F5B626D9C636B449B737E5B7B678598869A662F6B5B8542706C704C80736A607578685B70594A49715A4522E792')),\n  2: createSingleCallFunction(() => decodeData('000000000000000055394F383D2800008B8B1F210002000081B1CBCBCC820000847AAF6B9AAF2119BE08B8881AD60000A44FD07DCCF107015338130C00000000385972265F390B406E2437634B4B48031B12B8A0847000001E15B29A402F0000000000004B33460B00007A752C2A0000000000004D3900000084394B82013400ABA5CFC7AD9C0302A45A3E5A98AB000089A43382D97900008BA54AA087A70A0248A6A7AE6DBE0000BF6F94987EA40A01A06DCFA7A7A9030496C32F77891D0000A99FB1A0AFA80603B29AB9CA75930D010C0948354D3900000C0948354F37460D0028BE673D8400000000AF9D7B6E00002B007AA8933400007AA642675C2700007984CFB9C3985B768772A8A6B7B20000CAAECAAFC4B700009F94A6009F840009D09F9BA4CA9C0000CC8FC76DC87F0000C991C472A2000000A894A48CA7B501079BA2C9C69BA20000B19A5D3FA89000005CA6009DA2960901B0A7F0669FB200009D009E00B7890000DAD0F5D092820000D294D4C48BD10000B5A7A4A3B1A50402CAB6CBA6A2000000B5A7A4A3B1A8044FCDADD19D9CB00000B7778F7B8AAE0803C9AB5D3F5D3F00009EA09EA0BAB006039EA0989A8C7900009B9EF4D6B7C00000A9A7816CACA80000ABAC84705D3F000096DA635CDC8C00006F486F266F263D4784006124097B00374F6D2D6D2D6D4A3A95872322000000030000000000008D8939130000000000002E22A5C9CBC70600AB25C0B5C9B400061A2DB04CA67001082AA6BEBEBFC606002321DACBC19E03087AA08B6768380000282FBAC0B8CA7A88AD25BBA5A29900004C396C5894A6000040485A6E356E9442A32CD17EADA70000B4237923628600003E2DE9C1D7B500002F25BBA5A2990000231DB6AFB4A804023025C0B5CAB588062B2CBDBEC0C706882435A75CA20000002326BD6A82A908048B4B9A5A668000002423A09CB4BB060025259C9D8A7900001C1FCAB2C7C700002A2A9387ABA200002626A4A47D6E9D14333163A0C87500004B6F9C2D643A257049364936493647358A34438355497F1A0000A24C1D590000D38DFFBDD4CD3126'))\n};", "map": {"version": 3, "names": ["createSingleCallFunction", "charTable", "A", "B", "C", "D", "E", "F", "decodeData", "str", "output", "Uint8ClampedArray", "length", "i", "prebakedMiniMaps"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapPreBaked.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createSingleCallFunction } from '../../../../base/common/functional.js';\nconst charTable = {\n    '0': 0,\n    '1': 1,\n    '2': 2,\n    '3': 3,\n    '4': 4,\n    '5': 5,\n    '6': 6,\n    '7': 7,\n    '8': 8,\n    '9': 9,\n    A: 10,\n    B: 11,\n    C: 12,\n    D: 13,\n    E: 14,\n    F: 15\n};\nconst decodeData = (str) => {\n    const output = new Uint8ClampedArray(str.length / 2);\n    for (let i = 0; i < str.length; i += 2) {\n        output[i >> 1] = (charTable[str[i]] << 4) | (charTable[str[i + 1]] & 0xF);\n    }\n    return output;\n};\n/*\nconst encodeData = (data: Uint8ClampedArray, length: string) => {\n    const chars = '0123456789ABCDEF';\n    let output = '';\n    for (let i = 0; i < data.length; i++) {\n        output += chars[data[i] >> 4] + chars[data[i] & 0xf];\n    }\n    return output;\n};\n*/\n/**\n * Map of minimap scales to prebaked sample data at those scales. We don't\n * sample much larger data, because then font family becomes visible, which\n * is use-configurable.\n */\nexport const prebakedMiniMaps = {\n    1: createSingleCallFunction(() => decodeData('0000511D6300CF609C709645A78432005642574171487021003C451900274D35D762755E8B629C5BA856AF57BA649530C167D1512A272A3F6038604460398526BCA2A968DB6F8957C768BE5FBE2FB467CF5D8D5B795DC7625B5DFF50DE64C466DB2FC47CD860A65E9A2EB96CB54CE06DA763AB2EA26860524D3763536601005116008177A8705E53AB738E6A982F88BAA35B5F5B626D9C636B449B737E5B7B678598869A662F6B5B8542706C704C80736A607578685B70594A49715A4522E792')),\n    2: createSingleCallFunction(() => decodeData('000000000000000055394F383D2800008B8B1F210002000081B1CBCBCC820000847AAF6B9AAF2119BE08B8881AD60000A44FD07DCCF107015338130C00000000385972265F390B406E2437634B4B48031B12B8A0847000001E15B29A402F0000000000004B33460B00007A752C2A0000000000004D3900000084394B82013400ABA5CFC7AD9C0302A45A3E5A98AB000089A43382D97900008BA54AA087A70A0248A6A7AE6DBE0000BF6F94987EA40A01A06DCFA7A7A9030496C32F77891D0000A99FB1A0AFA80603B29AB9CA75930D010C0948354D3900000C0948354F37460D0028BE673D8400000000AF9D7B6E00002B007AA8933400007AA642675C2700007984CFB9C3985B768772A8A6B7B20000CAAECAAFC4B700009F94A6009F840009D09F9BA4CA9C0000CC8FC76DC87F0000C991C472A2000000A894A48CA7B501079BA2C9C69BA20000B19A5D3FA89000005CA6009DA2960901B0A7F0669FB200009D009E00B7890000DAD0F5D092820000D294D4C48BD10000B5A7A4A3B1A50402CAB6CBA6A2000000B5A7A4A3B1A8044FCDADD19D9CB00000B7778F7B8AAE0803C9AB5D3F5D3F00009EA09EA0BAB006039EA0989A8C7900009B9EF4D6B7C00000A9A7816CACA80000ABAC84705D3F000096DA635CDC8C00006F486F266F263D4784006124097B00374F6D2D6D2D6D4A3A95872322000000030000000000008D8939130000000000002E22A5C9CBC70600AB25C0B5C9B400061A2DB04CA67001082AA6BEBEBFC606002321DACBC19E03087AA08B6768380000282FBAC0B8CA7A88AD25BBA5A29900004C396C5894A6000040485A6E356E9442A32CD17EADA70000B4237923628600003E2DE9C1D7B500002F25BBA5A2990000231DB6AFB4A804023025C0B5CAB588062B2CBDBEC0C706882435A75CA20000002326BD6A82A908048B4B9A5A668000002423A09CB4BB060025259C9D8A7900001C1FCAB2C7C700002A2A9387ABA200002626A4A47D6E9D14333163A0C87500004B6F9C2D643A257049364936493647358A34438355497F1A0000A24C1D590000D38DFFBDD4CD3126'))\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,uCAAuC;AAChF,MAAMC,SAAS,GAAG;EACd,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACNC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE;AACP,CAAC;AACD,MAAMC,UAAU,GAAIC,GAAG,IAAK;EACxB,MAAMC,MAAM,GAAG,IAAIC,iBAAiB,CAACF,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC;EACpD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACG,MAAM,EAAEC,CAAC,IAAI,CAAC,EAAE;IACpCH,MAAM,CAACG,CAAC,IAAI,CAAC,CAAC,GAAIZ,SAAS,CAACQ,GAAG,CAACI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAKZ,SAAS,CAACQ,GAAG,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAI;EAC7E;EACA,OAAOH,MAAM;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,gBAAgB,GAAG;EAC5B,CAAC,EAAEd,wBAAwB,CAAC,MAAMQ,UAAU,CAAC,kYAAkY,CAAC,CAAC;EACjb,CAAC,EAAER,wBAAwB,CAAC,MAAMQ,UAAU,CAAC,kgDAAkgD,CAAC;AACpjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}