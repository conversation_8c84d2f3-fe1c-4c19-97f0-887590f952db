{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../base/common/event.js';\nimport * as platform from '../../registry/common/platform.js';\nexport const Extensions = {\n  JSONContribution: 'base.contributions.json'\n};\nfunction normalizeId(id) {\n  if (id.length > 0 && id.charAt(id.length - 1) === '#') {\n    return id.substring(0, id.length - 1);\n  }\n  return id;\n}\nclass JSONContributionRegistry {\n  constructor() {\n    this._onDidChangeSchema = new Emitter();\n    this.schemasById = {};\n  }\n  registerSchema(uri, unresolvedSchemaContent) {\n    this.schemasById[normalizeId(uri)] = unresolvedSchemaContent;\n    this._onDidChangeSchema.fire(uri);\n  }\n  notifySchemaChanged(uri) {\n    this._onDidChangeSchema.fire(uri);\n  }\n}\nconst jsonContributionRegistry = new JSONContributionRegistry();\nplatform.Registry.add(Extensions.JSONContribution, jsonContributionRegistry);", "map": {"version": 3, "names": ["Emitter", "platform", "Extensions", "JSONContribution", "normalizeId", "id", "length", "char<PERSON>t", "substring", "JSONContributionRegistry", "constructor", "_onDidChangeSchema", "schemasById", "registerSchema", "uri", "unresolvedSchemaContent", "fire", "notifySchemaChanged", "jsonContributionRegistry", "Registry", "add"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/jsonschemas/common/jsonContributionRegistry.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../base/common/event.js';\nimport * as platform from '../../registry/common/platform.js';\nexport const Extensions = {\n    JSONContribution: 'base.contributions.json'\n};\nfunction normalizeId(id) {\n    if (id.length > 0 && id.charAt(id.length - 1) === '#') {\n        return id.substring(0, id.length - 1);\n    }\n    return id;\n}\nclass JSONContributionRegistry {\n    constructor() {\n        this._onDidChangeSchema = new Emitter();\n        this.schemasById = {};\n    }\n    registerSchema(uri, unresolvedSchemaContent) {\n        this.schemasById[normalizeId(uri)] = unresolvedSchemaContent;\n        this._onDidChangeSchema.fire(uri);\n    }\n    notifySchemaChanged(uri) {\n        this._onDidChangeSchema.fire(uri);\n    }\n}\nconst jsonContributionRegistry = new JSONContributionRegistry();\nplatform.Registry.add(Extensions.JSONContribution, jsonContributionRegistry);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,+BAA+B;AACvD,OAAO,KAAKC,QAAQ,MAAM,mCAAmC;AAC7D,OAAO,MAAMC,UAAU,GAAG;EACtBC,gBAAgB,EAAE;AACtB,CAAC;AACD,SAASC,WAAWA,CAACC,EAAE,EAAE;EACrB,IAAIA,EAAE,CAACC,MAAM,GAAG,CAAC,IAAID,EAAE,CAACE,MAAM,CAACF,EAAE,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;IACnD,OAAOD,EAAE,CAACG,SAAS,CAAC,CAAC,EAAEH,EAAE,CAACC,MAAM,GAAG,CAAC,CAAC;EACzC;EACA,OAAOD,EAAE;AACb;AACA,MAAMI,wBAAwB,CAAC;EAC3BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,kBAAkB,GAAG,IAAIX,OAAO,CAAC,CAAC;IACvC,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;EACzB;EACAC,cAAcA,CAACC,GAAG,EAAEC,uBAAuB,EAAE;IACzC,IAAI,CAACH,WAAW,CAACR,WAAW,CAACU,GAAG,CAAC,CAAC,GAAGC,uBAAuB;IAC5D,IAAI,CAACJ,kBAAkB,CAACK,IAAI,CAACF,GAAG,CAAC;EACrC;EACAG,mBAAmBA,CAACH,GAAG,EAAE;IACrB,IAAI,CAACH,kBAAkB,CAACK,IAAI,CAACF,GAAG,CAAC;EACrC;AACJ;AACA,MAAMI,wBAAwB,GAAG,IAAIT,wBAAwB,CAAC,CAAC;AAC/DR,QAAQ,CAACkB,QAAQ,CAACC,GAAG,CAAClB,UAAU,CAACC,gBAAgB,EAAEe,wBAAwB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}