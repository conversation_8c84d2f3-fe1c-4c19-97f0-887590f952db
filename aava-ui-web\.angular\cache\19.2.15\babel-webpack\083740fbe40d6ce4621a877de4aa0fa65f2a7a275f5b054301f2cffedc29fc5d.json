{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar InlineSuggestionHintsContentWidget_1;\nimport { h } from '../../../../../base/browser/dom.js';\nimport { ActionViewItem } from '../../../../../base/browser/ui/actionbar/actionViewItems.js';\nimport { KeybindingLabel, unthemedKeybindingLabelOptions } from '../../../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\nimport { Action, Separator } from '../../../../../base/common/actions.js';\nimport { equals } from '../../../../../base/common/arrays.js';\nimport { RunOnceScheduler } from '../../../../../base/common/async.js';\nimport { Codicon } from '../../../../../base/common/codicons.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, derivedObservableWithCache, observableFromEvent } from '../../../../../base/common/observable.js';\nimport { derivedWithStore } from '../../../../../base/common/observableInternal/derived.js';\nimport { OS } from '../../../../../base/common/platform.js';\nimport { ThemeIcon } from '../../../../../base/common/themables.js';\nimport './inlineCompletionsHintsWidget.css';\nimport { Position } from '../../../../common/core/position.js';\nimport { InlineCompletionTriggerKind } from '../../../../common/languages.js';\nimport { showNextInlineSuggestionActionId, showPreviousInlineSuggestionActionId } from '../controller/commandIds.js';\nimport { localize } from '../../../../../nls.js';\nimport { MenuEntryActionViewItem, createAndFillInActionBarActions } from '../../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { WorkbenchToolBar } from '../../../../../platform/actions/browser/toolbar.js';\nimport { IMenuService, MenuId, MenuItemAction } from '../../../../../platform/actions/common/actions.js';\nimport { ICommandService } from '../../../../../platform/commands/common/commands.js';\nimport { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { IContextMenuService } from '../../../../../platform/contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';\nimport { ITelemetryService } from '../../../../../platform/telemetry/common/telemetry.js';\nimport { registerIcon } from '../../../../../platform/theme/common/iconRegistry.js';\nlet InlineCompletionsHintsWidget = class InlineCompletionsHintsWidget extends Disposable {\n  constructor(editor, model, instantiationService) {\n    super();\n    this.editor = editor;\n    this.model = model;\n    this.instantiationService = instantiationService;\n    this.alwaysShowToolbar = observableFromEvent(this, this.editor.onDidChangeConfiguration, () => this.editor.getOption(62 /* EditorOption.inlineSuggest */).showToolbar === 'always');\n    this.sessionPosition = undefined;\n    this.position = derived(this, reader => {\n      const ghostText = this.model.read(reader)?.primaryGhostText.read(reader);\n      if (!this.alwaysShowToolbar.read(reader) || !ghostText || ghostText.parts.length === 0) {\n        this.sessionPosition = undefined;\n        return null;\n      }\n      const firstColumn = ghostText.parts[0].column;\n      if (this.sessionPosition && this.sessionPosition.lineNumber !== ghostText.lineNumber) {\n        this.sessionPosition = undefined;\n      }\n      const position = new Position(ghostText.lineNumber, Math.min(firstColumn, this.sessionPosition?.column ?? Number.MAX_SAFE_INTEGER));\n      this.sessionPosition = position;\n      return position;\n    });\n    this._register(autorunWithStore((reader, store) => {\n      /** @description setup content widget */\n      const model = this.model.read(reader);\n      if (!model || !this.alwaysShowToolbar.read(reader)) {\n        return;\n      }\n      const contentWidgetValue = derivedWithStore((reader, store) => {\n        const contentWidget = store.add(this.instantiationService.createInstance(InlineSuggestionHintsContentWidget, this.editor, true, this.position, model.selectedInlineCompletionIndex, model.inlineCompletionsCount, model.activeCommands));\n        editor.addContentWidget(contentWidget);\n        store.add(toDisposable(() => editor.removeContentWidget(contentWidget)));\n        store.add(autorun(reader => {\n          /** @description request explicit */\n          const position = this.position.read(reader);\n          if (!position) {\n            return;\n          }\n          if (model.lastTriggerKind.read(reader) !== InlineCompletionTriggerKind.Explicit) {\n            model.triggerExplicitly();\n          }\n        }));\n        return contentWidget;\n      });\n      const hadPosition = derivedObservableWithCache(this, (reader, lastValue) => !!this.position.read(reader) || !!lastValue);\n      store.add(autorun(reader => {\n        if (hadPosition.read(reader)) {\n          contentWidgetValue.read(reader);\n        }\n      }));\n    }));\n  }\n};\nInlineCompletionsHintsWidget = __decorate([__param(2, IInstantiationService)], InlineCompletionsHintsWidget);\nexport { InlineCompletionsHintsWidget };\nconst inlineSuggestionHintsNextIcon = registerIcon('inline-suggestion-hints-next', Codicon.chevronRight, localize('parameterHintsNextIcon', 'Icon for show next parameter hint.'));\nconst inlineSuggestionHintsPreviousIcon = registerIcon('inline-suggestion-hints-previous', Codicon.chevronLeft, localize('parameterHintsPreviousIcon', 'Icon for show previous parameter hint.'));\nlet InlineSuggestionHintsContentWidget = /*#__PURE__*/(() => {\n  let InlineSuggestionHintsContentWidget = class InlineSuggestionHintsContentWidget extends Disposable {\n    static {\n      InlineSuggestionHintsContentWidget_1 = this;\n    }\n    static {\n      this._dropDownVisible = false;\n    }\n    static get dropDownVisible() {\n      return this._dropDownVisible;\n    }\n    static {\n      this.id = 0;\n    }\n    createCommandAction(commandId, label, iconClassName) {\n      const action = new Action(commandId, label, iconClassName, true, () => this._commandService.executeCommand(commandId));\n      const kb = this.keybindingService.lookupKeybinding(commandId, this._contextKeyService);\n      let tooltip = label;\n      if (kb) {\n        tooltip = localize({\n          key: 'content',\n          comment: ['A label', 'A keybinding']\n        }, '{0} ({1})', label, kb.getLabel());\n      }\n      action.tooltip = tooltip;\n      return action;\n    }\n    constructor(editor, withBorder, _position, _currentSuggestionIdx, _suggestionCount, _extraCommands, _commandService, instantiationService, keybindingService, _contextKeyService, _menuService) {\n      super();\n      this.editor = editor;\n      this.withBorder = withBorder;\n      this._position = _position;\n      this._currentSuggestionIdx = _currentSuggestionIdx;\n      this._suggestionCount = _suggestionCount;\n      this._extraCommands = _extraCommands;\n      this._commandService = _commandService;\n      this.keybindingService = keybindingService;\n      this._contextKeyService = _contextKeyService;\n      this._menuService = _menuService;\n      this.id = `InlineSuggestionHintsContentWidget${InlineSuggestionHintsContentWidget_1.id++}`;\n      this.allowEditorOverflow = true;\n      this.suppressMouseDown = false;\n      this.nodes = h('div.inlineSuggestionsHints', {\n        className: this.withBorder ? '.withBorder' : ''\n      }, [h('div@toolBar')]);\n      this.previousAction = this.createCommandAction(showPreviousInlineSuggestionActionId, localize('previous', 'Previous'), ThemeIcon.asClassName(inlineSuggestionHintsPreviousIcon));\n      this.availableSuggestionCountAction = new Action('inlineSuggestionHints.availableSuggestionCount', '', undefined, false);\n      this.nextAction = this.createCommandAction(showNextInlineSuggestionActionId, localize('next', 'Next'), ThemeIcon.asClassName(inlineSuggestionHintsNextIcon));\n      // TODO@hediet: deprecate MenuId.InlineCompletionsActions\n      this.inlineCompletionsActionsMenus = this._register(this._menuService.createMenu(MenuId.InlineCompletionsActions, this._contextKeyService));\n      this.clearAvailableSuggestionCountLabelDebounced = this._register(new RunOnceScheduler(() => {\n        this.availableSuggestionCountAction.label = '';\n      }, 100));\n      this.disableButtonsDebounced = this._register(new RunOnceScheduler(() => {\n        this.previousAction.enabled = this.nextAction.enabled = false;\n      }, 100));\n      this.toolBar = this._register(instantiationService.createInstance(CustomizedMenuWorkbenchToolBar, this.nodes.toolBar, MenuId.InlineSuggestionToolbar, {\n        menuOptions: {\n          renderShortTitle: true\n        },\n        toolbarOptions: {\n          primaryGroup: g => g.startsWith('primary')\n        },\n        actionViewItemProvider: (action, options) => {\n          if (action instanceof MenuItemAction) {\n            return instantiationService.createInstance(StatusBarViewItem, action, undefined);\n          }\n          if (action === this.availableSuggestionCountAction) {\n            const a = new ActionViewItemWithClassName(undefined, action, {\n              label: true,\n              icon: false\n            });\n            a.setClass('availableSuggestionCount');\n            return a;\n          }\n          return undefined;\n        },\n        telemetrySource: 'InlineSuggestionToolbar'\n      }));\n      this.toolBar.setPrependedPrimaryActions([this.previousAction, this.availableSuggestionCountAction, this.nextAction]);\n      this._register(this.toolBar.onDidChangeDropdownVisibility(e => {\n        InlineSuggestionHintsContentWidget_1._dropDownVisible = e;\n      }));\n      this._register(autorun(reader => {\n        /** @description update position */\n        this._position.read(reader);\n        this.editor.layoutContentWidget(this);\n      }));\n      this._register(autorun(reader => {\n        /** @description counts */\n        const suggestionCount = this._suggestionCount.read(reader);\n        const currentSuggestionIdx = this._currentSuggestionIdx.read(reader);\n        if (suggestionCount !== undefined) {\n          this.clearAvailableSuggestionCountLabelDebounced.cancel();\n          this.availableSuggestionCountAction.label = `${currentSuggestionIdx + 1}/${suggestionCount}`;\n        } else {\n          this.clearAvailableSuggestionCountLabelDebounced.schedule();\n        }\n        if (suggestionCount !== undefined && suggestionCount > 1) {\n          this.disableButtonsDebounced.cancel();\n          this.previousAction.enabled = this.nextAction.enabled = true;\n        } else {\n          this.disableButtonsDebounced.schedule();\n        }\n      }));\n      this._register(autorun(reader => {\n        /** @description extra commands */\n        const extraCommands = this._extraCommands.read(reader);\n        const extraActions = extraCommands.map(c => ({\n          class: undefined,\n          id: c.id,\n          enabled: true,\n          tooltip: c.tooltip || '',\n          label: c.title,\n          run: event => {\n            return this._commandService.executeCommand(c.id);\n          }\n        }));\n        for (const [_, group] of this.inlineCompletionsActionsMenus.getActions()) {\n          for (const action of group) {\n            if (action instanceof MenuItemAction) {\n              extraActions.push(action);\n            }\n          }\n        }\n        if (extraActions.length > 0) {\n          extraActions.unshift(new Separator());\n        }\n        this.toolBar.setAdditionalSecondaryActions(extraActions);\n      }));\n    }\n    getId() {\n      return this.id;\n    }\n    getDomNode() {\n      return this.nodes.root;\n    }\n    getPosition() {\n      return {\n        position: this._position.get(),\n        preference: [1 /* ContentWidgetPositionPreference.ABOVE */, 2 /* ContentWidgetPositionPreference.BELOW */],\n        positionAffinity: 3 /* PositionAffinity.LeftOfInjectedText */\n      };\n    }\n  };\n  return InlineSuggestionHintsContentWidget;\n})();\nInlineSuggestionHintsContentWidget = InlineSuggestionHintsContentWidget_1 = __decorate([__param(6, ICommandService), __param(7, IInstantiationService), __param(8, IKeybindingService), __param(9, IContextKeyService), __param(10, IMenuService)], InlineSuggestionHintsContentWidget);\nexport { InlineSuggestionHintsContentWidget };\nclass ActionViewItemWithClassName extends ActionViewItem {\n  constructor() {\n    super(...arguments);\n    this._className = undefined;\n  }\n  setClass(className) {\n    this._className = className;\n  }\n  render(container) {\n    super.render(container);\n    if (this._className) {\n      container.classList.add(this._className);\n    }\n  }\n  updateTooltip() {\n    // NOOP, disable tooltip\n  }\n}\nclass StatusBarViewItem extends MenuEntryActionViewItem {\n  updateLabel() {\n    const kb = this._keybindingService.lookupKeybinding(this._action.id, this._contextKeyService);\n    if (!kb) {\n      return super.updateLabel();\n    }\n    if (this.label) {\n      const div = h('div.keybinding').root;\n      const k = this._register(new KeybindingLabel(div, OS, {\n        disableTitle: true,\n        ...unthemedKeybindingLabelOptions\n      }));\n      k.set(kb);\n      this.label.textContent = this._action.label;\n      this.label.appendChild(div);\n      this.label.classList.add('inlineSuggestionStatusBarItemLabel');\n    }\n  }\n  updateTooltip() {\n    // NOOP, disable tooltip\n  }\n}\nlet CustomizedMenuWorkbenchToolBar = class CustomizedMenuWorkbenchToolBar extends WorkbenchToolBar {\n  constructor(container, menuId, options2, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService) {\n    super(container, {\n      resetMenu: menuId,\n      ...options2\n    }, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService);\n    this.menuId = menuId;\n    this.options2 = options2;\n    this.menuService = menuService;\n    this.contextKeyService = contextKeyService;\n    this.menu = this._store.add(this.menuService.createMenu(this.menuId, this.contextKeyService, {\n      emitEventsForSubmenuChanges: true\n    }));\n    this.additionalActions = [];\n    this.prependedPrimaryActions = [];\n    this._store.add(this.menu.onDidChange(() => this.updateToolbar()));\n    this.updateToolbar();\n  }\n  updateToolbar() {\n    const primary = [];\n    const secondary = [];\n    createAndFillInActionBarActions(this.menu, this.options2?.menuOptions, {\n      primary,\n      secondary\n    }, this.options2?.toolbarOptions?.primaryGroup, this.options2?.toolbarOptions?.shouldInlineSubmenu, this.options2?.toolbarOptions?.useSeparatorsInPrimaryActions);\n    secondary.push(...this.additionalActions);\n    primary.unshift(...this.prependedPrimaryActions);\n    this.setActions(primary, secondary);\n  }\n  setPrependedPrimaryActions(actions) {\n    if (equals(this.prependedPrimaryActions, actions, (a, b) => a === b)) {\n      return;\n    }\n    this.prependedPrimaryActions = actions;\n    this.updateToolbar();\n  }\n  setAdditionalSecondaryActions(actions) {\n    if (equals(this.additionalActions, actions, (a, b) => a === b)) {\n      return;\n    }\n    this.additionalActions = actions;\n    this.updateToolbar();\n  }\n};\nCustomizedMenuWorkbenchToolBar = __decorate([__param(3, IMenuService), __param(4, IContextKeyService), __param(5, IContextMenuService), __param(6, IKeybindingService), __param(7, ICommandService), __param(8, ITelemetryService)], CustomizedMenuWorkbenchToolBar);\nexport { CustomizedMenuWorkbenchToolBar };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "InlineSuggestionHintsContentWidget_1", "h", "ActionViewItem", "KeybindingLabel", "unthemedKeybindingLabelOptions", "Action", "Separator", "equals", "RunOnceScheduler", "Codicon", "Disposable", "toDisposable", "autorun", "autorunWithStore", "derived", "derivedObservableWithCache", "observableFromEvent", "derivedWithStore", "OS", "ThemeIcon", "Position", "InlineCompletionTriggerKind", "showNextInlineSuggestionActionId", "showPreviousInlineSuggestionActionId", "localize", "MenuEntryActionViewItem", "createAndFillInActionBarActions", "WorkbenchToolBar", "IMenuService", "MenuId", "MenuItemAction", "ICommandService", "IContextKeyService", "IContextMenuService", "IInstantiationService", "IKeybindingService", "ITelemetryService", "registerIcon", "InlineCompletionsHintsWidget", "constructor", "editor", "model", "instantiationService", "alwaysShowToolbar", "onDidChangeConfiguration", "getOption", "showToolbar", "sessionPosition", "undefined", "position", "reader", "ghostText", "read", "primaryGhostText", "parts", "firstColumn", "column", "lineNumber", "Math", "min", "Number", "MAX_SAFE_INTEGER", "_register", "store", "contentWidgetValue", "contentWidget", "add", "createInstance", "InlineSuggestionHintsContentWidget", "selectedInlineCompletionIndex", "inlineCompletionsCount", "activeCommands", "addContentWidget", "removeContentWidget", "lastTriggerKind", "Explicit", "triggerExplicitly", "hadPosition", "lastValue", "inlineSuggestionHintsNextIcon", "chevronRight", "inlineSuggestionHintsPreviousIcon", "chevronLeft", "_dropDownVisible", "dropDownVisible", "id", "createCommandAction", "commandId", "label", "iconClassName", "action", "_commandService", "executeCommand", "kb", "keybindingService", "lookupKeybinding", "_contextKeyService", "tooltip", "comment", "get<PERSON><PERSON><PERSON>", "withB<PERSON>er", "_position", "_currentSuggestionIdx", "_suggestionCount", "_extraCommands", "_menuService", "allowEditorOverflow", "suppressMouseDown", "nodes", "className", "previousAction", "asClassName", "availableSuggestionCountAction", "nextAction", "inlineCompletionsActionsMenus", "createMenu", "InlineCompletionsActions", "clearAvailableSuggestionCountLabelDebounced", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled", "toolBar", "CustomizedMenuWorkbenchToolBar", "InlineSuggestionToolbar", "menuOptions", "renderShortTitle", "toolbarOptions", "primaryGroup", "g", "startsWith", "actionViewItemProvider", "options", "StatusBarViewItem", "a", "ActionViewItemWithClassName", "icon", "setClass", "telemetrySource", "setPrependedPrimaryActions", "onDidChangeDropdownVisibility", "e", "layoutContentWidget", "suggestionCount", "currentSuggestionIdx", "cancel", "schedule", "extraCommands", "extraActions", "map", "class", "title", "run", "event", "_", "group", "getActions", "push", "unshift", "setAdditionalSecondaryActions", "getId", "getDomNode", "root", "getPosition", "get", "preference", "positionAffinity", "_className", "render", "container", "classList", "updateTooltip", "updateLabel", "_keybindingService", "_action", "div", "k", "disable<PERSON><PERSON>le", "set", "textContent", "append<PERSON><PERSON><PERSON>", "menuId", "options2", "menuService", "contextKeyService", "contextMenuService", "commandService", "telemetryService", "resetMenu", "menu", "_store", "emitEventsForSubmenuChanges", "additionalActions", "prependedPrimaryActions", "onDidChange", "updateToolbar", "primary", "secondary", "shouldInlineSubmenu", "useSeparatorsInPrimaryActions", "setActions", "actions", "b"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar InlineSuggestionHintsContentWidget_1;\nimport { h } from '../../../../../base/browser/dom.js';\nimport { ActionViewItem } from '../../../../../base/browser/ui/actionbar/actionViewItems.js';\nimport { KeybindingLabel, unthemedKeybindingLabelOptions } from '../../../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\nimport { Action, Separator } from '../../../../../base/common/actions.js';\nimport { equals } from '../../../../../base/common/arrays.js';\nimport { RunOnceScheduler } from '../../../../../base/common/async.js';\nimport { Codicon } from '../../../../../base/common/codicons.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, derivedObservableWithCache, observableFromEvent } from '../../../../../base/common/observable.js';\nimport { derivedWithStore } from '../../../../../base/common/observableInternal/derived.js';\nimport { OS } from '../../../../../base/common/platform.js';\nimport { ThemeIcon } from '../../../../../base/common/themables.js';\nimport './inlineCompletionsHintsWidget.css';\nimport { Position } from '../../../../common/core/position.js';\nimport { InlineCompletionTriggerKind } from '../../../../common/languages.js';\nimport { showNextInlineSuggestionActionId, showPreviousInlineSuggestionActionId } from '../controller/commandIds.js';\nimport { localize } from '../../../../../nls.js';\nimport { MenuEntryActionViewItem, createAndFillInActionBarActions } from '../../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { WorkbenchToolBar } from '../../../../../platform/actions/browser/toolbar.js';\nimport { IMenuService, MenuId, MenuItemAction } from '../../../../../platform/actions/common/actions.js';\nimport { ICommandService } from '../../../../../platform/commands/common/commands.js';\nimport { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { IContextMenuService } from '../../../../../platform/contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';\nimport { ITelemetryService } from '../../../../../platform/telemetry/common/telemetry.js';\nimport { registerIcon } from '../../../../../platform/theme/common/iconRegistry.js';\nlet InlineCompletionsHintsWidget = class InlineCompletionsHintsWidget extends Disposable {\n    constructor(editor, model, instantiationService) {\n        super();\n        this.editor = editor;\n        this.model = model;\n        this.instantiationService = instantiationService;\n        this.alwaysShowToolbar = observableFromEvent(this, this.editor.onDidChangeConfiguration, () => this.editor.getOption(62 /* EditorOption.inlineSuggest */).showToolbar === 'always');\n        this.sessionPosition = undefined;\n        this.position = derived(this, reader => {\n            const ghostText = this.model.read(reader)?.primaryGhostText.read(reader);\n            if (!this.alwaysShowToolbar.read(reader) || !ghostText || ghostText.parts.length === 0) {\n                this.sessionPosition = undefined;\n                return null;\n            }\n            const firstColumn = ghostText.parts[0].column;\n            if (this.sessionPosition && this.sessionPosition.lineNumber !== ghostText.lineNumber) {\n                this.sessionPosition = undefined;\n            }\n            const position = new Position(ghostText.lineNumber, Math.min(firstColumn, this.sessionPosition?.column ?? Number.MAX_SAFE_INTEGER));\n            this.sessionPosition = position;\n            return position;\n        });\n        this._register(autorunWithStore((reader, store) => {\n            /** @description setup content widget */\n            const model = this.model.read(reader);\n            if (!model || !this.alwaysShowToolbar.read(reader)) {\n                return;\n            }\n            const contentWidgetValue = derivedWithStore((reader, store) => {\n                const contentWidget = store.add(this.instantiationService.createInstance(InlineSuggestionHintsContentWidget, this.editor, true, this.position, model.selectedInlineCompletionIndex, model.inlineCompletionsCount, model.activeCommands));\n                editor.addContentWidget(contentWidget);\n                store.add(toDisposable(() => editor.removeContentWidget(contentWidget)));\n                store.add(autorun(reader => {\n                    /** @description request explicit */\n                    const position = this.position.read(reader);\n                    if (!position) {\n                        return;\n                    }\n                    if (model.lastTriggerKind.read(reader) !== InlineCompletionTriggerKind.Explicit) {\n                        model.triggerExplicitly();\n                    }\n                }));\n                return contentWidget;\n            });\n            const hadPosition = derivedObservableWithCache(this, (reader, lastValue) => !!this.position.read(reader) || !!lastValue);\n            store.add(autorun(reader => {\n                if (hadPosition.read(reader)) {\n                    contentWidgetValue.read(reader);\n                }\n            }));\n        }));\n    }\n};\nInlineCompletionsHintsWidget = __decorate([\n    __param(2, IInstantiationService)\n], InlineCompletionsHintsWidget);\nexport { InlineCompletionsHintsWidget };\nconst inlineSuggestionHintsNextIcon = registerIcon('inline-suggestion-hints-next', Codicon.chevronRight, localize('parameterHintsNextIcon', 'Icon for show next parameter hint.'));\nconst inlineSuggestionHintsPreviousIcon = registerIcon('inline-suggestion-hints-previous', Codicon.chevronLeft, localize('parameterHintsPreviousIcon', 'Icon for show previous parameter hint.'));\nlet InlineSuggestionHintsContentWidget = class InlineSuggestionHintsContentWidget extends Disposable {\n    static { InlineSuggestionHintsContentWidget_1 = this; }\n    static { this._dropDownVisible = false; }\n    static get dropDownVisible() { return this._dropDownVisible; }\n    static { this.id = 0; }\n    createCommandAction(commandId, label, iconClassName) {\n        const action = new Action(commandId, label, iconClassName, true, () => this._commandService.executeCommand(commandId));\n        const kb = this.keybindingService.lookupKeybinding(commandId, this._contextKeyService);\n        let tooltip = label;\n        if (kb) {\n            tooltip = localize({ key: 'content', comment: ['A label', 'A keybinding'] }, '{0} ({1})', label, kb.getLabel());\n        }\n        action.tooltip = tooltip;\n        return action;\n    }\n    constructor(editor, withBorder, _position, _currentSuggestionIdx, _suggestionCount, _extraCommands, _commandService, instantiationService, keybindingService, _contextKeyService, _menuService) {\n        super();\n        this.editor = editor;\n        this.withBorder = withBorder;\n        this._position = _position;\n        this._currentSuggestionIdx = _currentSuggestionIdx;\n        this._suggestionCount = _suggestionCount;\n        this._extraCommands = _extraCommands;\n        this._commandService = _commandService;\n        this.keybindingService = keybindingService;\n        this._contextKeyService = _contextKeyService;\n        this._menuService = _menuService;\n        this.id = `InlineSuggestionHintsContentWidget${InlineSuggestionHintsContentWidget_1.id++}`;\n        this.allowEditorOverflow = true;\n        this.suppressMouseDown = false;\n        this.nodes = h('div.inlineSuggestionsHints', { className: this.withBorder ? '.withBorder' : '' }, [\n            h('div@toolBar'),\n        ]);\n        this.previousAction = this.createCommandAction(showPreviousInlineSuggestionActionId, localize('previous', 'Previous'), ThemeIcon.asClassName(inlineSuggestionHintsPreviousIcon));\n        this.availableSuggestionCountAction = new Action('inlineSuggestionHints.availableSuggestionCount', '', undefined, false);\n        this.nextAction = this.createCommandAction(showNextInlineSuggestionActionId, localize('next', 'Next'), ThemeIcon.asClassName(inlineSuggestionHintsNextIcon));\n        // TODO@hediet: deprecate MenuId.InlineCompletionsActions\n        this.inlineCompletionsActionsMenus = this._register(this._menuService.createMenu(MenuId.InlineCompletionsActions, this._contextKeyService));\n        this.clearAvailableSuggestionCountLabelDebounced = this._register(new RunOnceScheduler(() => {\n            this.availableSuggestionCountAction.label = '';\n        }, 100));\n        this.disableButtonsDebounced = this._register(new RunOnceScheduler(() => {\n            this.previousAction.enabled = this.nextAction.enabled = false;\n        }, 100));\n        this.toolBar = this._register(instantiationService.createInstance(CustomizedMenuWorkbenchToolBar, this.nodes.toolBar, MenuId.InlineSuggestionToolbar, {\n            menuOptions: { renderShortTitle: true },\n            toolbarOptions: { primaryGroup: g => g.startsWith('primary') },\n            actionViewItemProvider: (action, options) => {\n                if (action instanceof MenuItemAction) {\n                    return instantiationService.createInstance(StatusBarViewItem, action, undefined);\n                }\n                if (action === this.availableSuggestionCountAction) {\n                    const a = new ActionViewItemWithClassName(undefined, action, { label: true, icon: false });\n                    a.setClass('availableSuggestionCount');\n                    return a;\n                }\n                return undefined;\n            },\n            telemetrySource: 'InlineSuggestionToolbar',\n        }));\n        this.toolBar.setPrependedPrimaryActions([\n            this.previousAction,\n            this.availableSuggestionCountAction,\n            this.nextAction,\n        ]);\n        this._register(this.toolBar.onDidChangeDropdownVisibility(e => {\n            InlineSuggestionHintsContentWidget_1._dropDownVisible = e;\n        }));\n        this._register(autorun(reader => {\n            /** @description update position */\n            this._position.read(reader);\n            this.editor.layoutContentWidget(this);\n        }));\n        this._register(autorun(reader => {\n            /** @description counts */\n            const suggestionCount = this._suggestionCount.read(reader);\n            const currentSuggestionIdx = this._currentSuggestionIdx.read(reader);\n            if (suggestionCount !== undefined) {\n                this.clearAvailableSuggestionCountLabelDebounced.cancel();\n                this.availableSuggestionCountAction.label = `${currentSuggestionIdx + 1}/${suggestionCount}`;\n            }\n            else {\n                this.clearAvailableSuggestionCountLabelDebounced.schedule();\n            }\n            if (suggestionCount !== undefined && suggestionCount > 1) {\n                this.disableButtonsDebounced.cancel();\n                this.previousAction.enabled = this.nextAction.enabled = true;\n            }\n            else {\n                this.disableButtonsDebounced.schedule();\n            }\n        }));\n        this._register(autorun(reader => {\n            /** @description extra commands */\n            const extraCommands = this._extraCommands.read(reader);\n            const extraActions = extraCommands.map(c => ({\n                class: undefined,\n                id: c.id,\n                enabled: true,\n                tooltip: c.tooltip || '',\n                label: c.title,\n                run: (event) => {\n                    return this._commandService.executeCommand(c.id);\n                },\n            }));\n            for (const [_, group] of this.inlineCompletionsActionsMenus.getActions()) {\n                for (const action of group) {\n                    if (action instanceof MenuItemAction) {\n                        extraActions.push(action);\n                    }\n                }\n            }\n            if (extraActions.length > 0) {\n                extraActions.unshift(new Separator());\n            }\n            this.toolBar.setAdditionalSecondaryActions(extraActions);\n        }));\n    }\n    getId() { return this.id; }\n    getDomNode() {\n        return this.nodes.root;\n    }\n    getPosition() {\n        return {\n            position: this._position.get(),\n            preference: [1 /* ContentWidgetPositionPreference.ABOVE */, 2 /* ContentWidgetPositionPreference.BELOW */],\n            positionAffinity: 3 /* PositionAffinity.LeftOfInjectedText */,\n        };\n    }\n};\nInlineSuggestionHintsContentWidget = InlineSuggestionHintsContentWidget_1 = __decorate([\n    __param(6, ICommandService),\n    __param(7, IInstantiationService),\n    __param(8, IKeybindingService),\n    __param(9, IContextKeyService),\n    __param(10, IMenuService)\n], InlineSuggestionHintsContentWidget);\nexport { InlineSuggestionHintsContentWidget };\nclass ActionViewItemWithClassName extends ActionViewItem {\n    constructor() {\n        super(...arguments);\n        this._className = undefined;\n    }\n    setClass(className) {\n        this._className = className;\n    }\n    render(container) {\n        super.render(container);\n        if (this._className) {\n            container.classList.add(this._className);\n        }\n    }\n    updateTooltip() {\n        // NOOP, disable tooltip\n    }\n}\nclass StatusBarViewItem extends MenuEntryActionViewItem {\n    updateLabel() {\n        const kb = this._keybindingService.lookupKeybinding(this._action.id, this._contextKeyService);\n        if (!kb) {\n            return super.updateLabel();\n        }\n        if (this.label) {\n            const div = h('div.keybinding').root;\n            const k = this._register(new KeybindingLabel(div, OS, { disableTitle: true, ...unthemedKeybindingLabelOptions }));\n            k.set(kb);\n            this.label.textContent = this._action.label;\n            this.label.appendChild(div);\n            this.label.classList.add('inlineSuggestionStatusBarItemLabel');\n        }\n    }\n    updateTooltip() {\n        // NOOP, disable tooltip\n    }\n}\nlet CustomizedMenuWorkbenchToolBar = class CustomizedMenuWorkbenchToolBar extends WorkbenchToolBar {\n    constructor(container, menuId, options2, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService) {\n        super(container, { resetMenu: menuId, ...options2 }, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService);\n        this.menuId = menuId;\n        this.options2 = options2;\n        this.menuService = menuService;\n        this.contextKeyService = contextKeyService;\n        this.menu = this._store.add(this.menuService.createMenu(this.menuId, this.contextKeyService, { emitEventsForSubmenuChanges: true }));\n        this.additionalActions = [];\n        this.prependedPrimaryActions = [];\n        this._store.add(this.menu.onDidChange(() => this.updateToolbar()));\n        this.updateToolbar();\n    }\n    updateToolbar() {\n        const primary = [];\n        const secondary = [];\n        createAndFillInActionBarActions(this.menu, this.options2?.menuOptions, { primary, secondary }, this.options2?.toolbarOptions?.primaryGroup, this.options2?.toolbarOptions?.shouldInlineSubmenu, this.options2?.toolbarOptions?.useSeparatorsInPrimaryActions);\n        secondary.push(...this.additionalActions);\n        primary.unshift(...this.prependedPrimaryActions);\n        this.setActions(primary, secondary);\n    }\n    setPrependedPrimaryActions(actions) {\n        if (equals(this.prependedPrimaryActions, actions, (a, b) => a === b)) {\n            return;\n        }\n        this.prependedPrimaryActions = actions;\n        this.updateToolbar();\n    }\n    setAdditionalSecondaryActions(actions) {\n        if (equals(this.additionalActions, actions, (a, b) => a === b)) {\n            return;\n        }\n        this.additionalActions = actions;\n        this.updateToolbar();\n    }\n};\nCustomizedMenuWorkbenchToolBar = __decorate([\n    __param(3, IMenuService),\n    __param(4, IContextKeyService),\n    __param(5, IContextMenuService),\n    __param(6, IKeybindingService),\n    __param(7, ICommandService),\n    __param(8, ITelemetryService)\n], CustomizedMenuWorkbenchToolBar);\nexport { CustomizedMenuWorkbenchToolBar };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,oCAAoC;AACxC,SAASC,CAAC,QAAQ,oCAAoC;AACtD,SAASC,cAAc,QAAQ,6DAA6D;AAC5F,SAASC,eAAe,EAAEC,8BAA8B,QAAQ,mEAAmE;AACnI,SAASC,MAAM,EAAEC,SAAS,QAAQ,uCAAuC;AACzE,SAASC,MAAM,QAAQ,sCAAsC;AAC7D,SAASC,gBAAgB,QAAQ,qCAAqC;AACtE,SAASC,OAAO,QAAQ,wCAAwC;AAChE,SAASC,UAAU,EAAEC,YAAY,QAAQ,yCAAyC;AAClF,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,0BAA0B,EAAEC,mBAAmB,QAAQ,0CAA0C;AAC9I,SAASC,gBAAgB,QAAQ,0DAA0D;AAC3F,SAASC,EAAE,QAAQ,wCAAwC;AAC3D,SAASC,SAAS,QAAQ,yCAAyC;AACnE,OAAO,oCAAoC;AAC3C,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,gCAAgC,EAAEC,oCAAoC,QAAQ,6BAA6B;AACpH,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,uBAAuB,EAAEC,+BAA+B,QAAQ,oEAAoE;AAC7I,SAASC,gBAAgB,QAAQ,oDAAoD;AACrF,SAASC,YAAY,EAAEC,MAAM,EAAEC,cAAc,QAAQ,mDAAmD;AACxG,SAASC,eAAe,QAAQ,qDAAqD;AACrF,SAASC,kBAAkB,QAAQ,yDAAyD;AAC5F,SAASC,mBAAmB,QAAQ,4DAA4D;AAChG,SAASC,qBAAqB,QAAQ,+DAA+D;AACrG,SAASC,kBAAkB,QAAQ,yDAAyD;AAC5F,SAASC,iBAAiB,QAAQ,uDAAuD;AACzF,SAASC,YAAY,QAAQ,sDAAsD;AACnF,IAAIC,4BAA4B,GAAG,MAAMA,4BAA4B,SAAS5B,UAAU,CAAC;EACrF6B,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,oBAAoB,EAAE;IAC7C,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,iBAAiB,GAAG3B,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACwB,MAAM,CAACI,wBAAwB,EAAE,MAAM,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,EAAE,CAAC,gCAAgC,CAAC,CAACC,WAAW,KAAK,QAAQ,CAAC;IACnL,IAAI,CAACC,eAAe,GAAGC,SAAS;IAChC,IAAI,CAACC,QAAQ,GAAGnC,OAAO,CAAC,IAAI,EAAEoC,MAAM,IAAI;MACpC,MAAMC,SAAS,GAAG,IAAI,CAACV,KAAK,CAACW,IAAI,CAACF,MAAM,CAAC,EAAEG,gBAAgB,CAACD,IAAI,CAACF,MAAM,CAAC;MACxE,IAAI,CAAC,IAAI,CAACP,iBAAiB,CAACS,IAAI,CAACF,MAAM,CAAC,IAAI,CAACC,SAAS,IAAIA,SAAS,CAACG,KAAK,CAAClE,MAAM,KAAK,CAAC,EAAE;QACpF,IAAI,CAAC2D,eAAe,GAAGC,SAAS;QAChC,OAAO,IAAI;MACf;MACA,MAAMO,WAAW,GAAGJ,SAAS,CAACG,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM;MAC7C,IAAI,IAAI,CAACT,eAAe,IAAI,IAAI,CAACA,eAAe,CAACU,UAAU,KAAKN,SAAS,CAACM,UAAU,EAAE;QAClF,IAAI,CAACV,eAAe,GAAGC,SAAS;MACpC;MACA,MAAMC,QAAQ,GAAG,IAAI7B,QAAQ,CAAC+B,SAAS,CAACM,UAAU,EAAEC,IAAI,CAACC,GAAG,CAACJ,WAAW,EAAE,IAAI,CAACR,eAAe,EAAES,MAAM,IAAII,MAAM,CAACC,gBAAgB,CAAC,CAAC;MACnI,IAAI,CAACd,eAAe,GAAGE,QAAQ;MAC/B,OAAOA,QAAQ;IACnB,CAAC,CAAC;IACF,IAAI,CAACa,SAAS,CAACjD,gBAAgB,CAAC,CAACqC,MAAM,EAAEa,KAAK,KAAK;MAC/C;MACA,MAAMtB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACW,IAAI,CAACF,MAAM,CAAC;MACrC,IAAI,CAACT,KAAK,IAAI,CAAC,IAAI,CAACE,iBAAiB,CAACS,IAAI,CAACF,MAAM,CAAC,EAAE;QAChD;MACJ;MACA,MAAMc,kBAAkB,GAAG/C,gBAAgB,CAAC,CAACiC,MAAM,EAAEa,KAAK,KAAK;QAC3D,MAAME,aAAa,GAAGF,KAAK,CAACG,GAAG,CAAC,IAAI,CAACxB,oBAAoB,CAACyB,cAAc,CAACC,kCAAkC,EAAE,IAAI,CAAC5B,MAAM,EAAE,IAAI,EAAE,IAAI,CAACS,QAAQ,EAAER,KAAK,CAAC4B,6BAA6B,EAAE5B,KAAK,CAAC6B,sBAAsB,EAAE7B,KAAK,CAAC8B,cAAc,CAAC,CAAC;QACxO/B,MAAM,CAACgC,gBAAgB,CAACP,aAAa,CAAC;QACtCF,KAAK,CAACG,GAAG,CAACvD,YAAY,CAAC,MAAM6B,MAAM,CAACiC,mBAAmB,CAACR,aAAa,CAAC,CAAC,CAAC;QACxEF,KAAK,CAACG,GAAG,CAACtD,OAAO,CAACsC,MAAM,IAAI;UACxB;UACA,MAAMD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACG,IAAI,CAACF,MAAM,CAAC;UAC3C,IAAI,CAACD,QAAQ,EAAE;YACX;UACJ;UACA,IAAIR,KAAK,CAACiC,eAAe,CAACtB,IAAI,CAACF,MAAM,CAAC,KAAK7B,2BAA2B,CAACsD,QAAQ,EAAE;YAC7ElC,KAAK,CAACmC,iBAAiB,CAAC,CAAC;UAC7B;QACJ,CAAC,CAAC,CAAC;QACH,OAAOX,aAAa;MACxB,CAAC,CAAC;MACF,MAAMY,WAAW,GAAG9D,0BAA0B,CAAC,IAAI,EAAE,CAACmC,MAAM,EAAE4B,SAAS,KAAK,CAAC,CAAC,IAAI,CAAC7B,QAAQ,CAACG,IAAI,CAACF,MAAM,CAAC,IAAI,CAAC,CAAC4B,SAAS,CAAC;MACxHf,KAAK,CAACG,GAAG,CAACtD,OAAO,CAACsC,MAAM,IAAI;QACxB,IAAI2B,WAAW,CAACzB,IAAI,CAACF,MAAM,CAAC,EAAE;UAC1Bc,kBAAkB,CAACZ,IAAI,CAACF,MAAM,CAAC;QACnC;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;EACP;AACJ,CAAC;AACDZ,4BAA4B,GAAGzD,UAAU,CAAC,CACtCgB,OAAO,CAAC,CAAC,EAAEqC,qBAAqB,CAAC,CACpC,EAAEI,4BAA4B,CAAC;AAChC,SAASA,4BAA4B;AACrC,MAAMyC,6BAA6B,GAAG1C,YAAY,CAAC,8BAA8B,EAAE5B,OAAO,CAACuE,YAAY,EAAExD,QAAQ,CAAC,wBAAwB,EAAE,oCAAoC,CAAC,CAAC;AAClL,MAAMyD,iCAAiC,GAAG5C,YAAY,CAAC,kCAAkC,EAAE5B,OAAO,CAACyE,WAAW,EAAE1D,QAAQ,CAAC,4BAA4B,EAAE,wCAAwC,CAAC,CAAC;AACjM,IAAI4C,kCAAkC;EAAA,IAAlCA,kCAAkC,GAAG,MAAMA,kCAAkC,SAAS1D,UAAU,CAAC;IACjG;MAASV,oCAAoC,GAAG,IAAI;IAAE;IACtD;MAAS,IAAI,CAACmF,gBAAgB,GAAG,KAAK;IAAE;IACxC,WAAWC,eAAeA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACD,gBAAgB;IAAE;IAC7D;MAAS,IAAI,CAACE,EAAE,GAAG,CAAC;IAAE;IACtBC,mBAAmBA,CAACC,SAAS,EAAEC,KAAK,EAAEC,aAAa,EAAE;MACjD,MAAMC,MAAM,GAAG,IAAIrF,MAAM,CAACkF,SAAS,EAAEC,KAAK,EAAEC,aAAa,EAAE,IAAI,EAAE,MAAM,IAAI,CAACE,eAAe,CAACC,cAAc,CAACL,SAAS,CAAC,CAAC;MACtH,MAAMM,EAAE,GAAG,IAAI,CAACC,iBAAiB,CAACC,gBAAgB,CAACR,SAAS,EAAE,IAAI,CAACS,kBAAkB,CAAC;MACtF,IAAIC,OAAO,GAAGT,KAAK;MACnB,IAAIK,EAAE,EAAE;QACJI,OAAO,GAAGzE,QAAQ,CAAC;UAAExC,GAAG,EAAE,SAAS;UAAEkH,OAAO,EAAE,CAAC,SAAS,EAAE,cAAc;QAAE,CAAC,EAAE,WAAW,EAAEV,KAAK,EAAEK,EAAE,CAACM,QAAQ,CAAC,CAAC,CAAC;MACnH;MACAT,MAAM,CAACO,OAAO,GAAGA,OAAO;MACxB,OAAOP,MAAM;IACjB;IACAnD,WAAWA,CAACC,MAAM,EAAE4D,UAAU,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,cAAc,EAAEb,eAAe,EAAEjD,oBAAoB,EAAEoD,iBAAiB,EAAEE,kBAAkB,EAAES,YAAY,EAAE;MAC5L,KAAK,CAAC,CAAC;MACP,IAAI,CAACjE,MAAM,GAAGA,MAAM;MACpB,IAAI,CAAC4D,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACC,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;MAClD,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,cAAc,GAAGA,cAAc;MACpC,IAAI,CAACb,eAAe,GAAGA,eAAe;MACtC,IAAI,CAACG,iBAAiB,GAAGA,iBAAiB;MAC1C,IAAI,CAACE,kBAAkB,GAAGA,kBAAkB;MAC5C,IAAI,CAACS,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACpB,EAAE,GAAG,qCAAqCrF,oCAAoC,CAACqF,EAAE,EAAE,EAAE;MAC1F,IAAI,CAACqB,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACC,KAAK,GAAG3G,CAAC,CAAC,4BAA4B,EAAE;QAAE4G,SAAS,EAAE,IAAI,CAACT,UAAU,GAAG,aAAa,GAAG;MAAG,CAAC,EAAE,CAC9FnG,CAAC,CAAC,aAAa,CAAC,CACnB,CAAC;MACF,IAAI,CAAC6G,cAAc,GAAG,IAAI,CAACxB,mBAAmB,CAAC/D,oCAAoC,EAAEC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAEL,SAAS,CAAC4F,WAAW,CAAC9B,iCAAiC,CAAC,CAAC;MAChL,IAAI,CAAC+B,8BAA8B,GAAG,IAAI3G,MAAM,CAAC,gDAAgD,EAAE,EAAE,EAAE2C,SAAS,EAAE,KAAK,CAAC;MACxH,IAAI,CAACiE,UAAU,GAAG,IAAI,CAAC3B,mBAAmB,CAAChE,gCAAgC,EAAEE,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEL,SAAS,CAAC4F,WAAW,CAAChC,6BAA6B,CAAC,CAAC;MAC5J;MACA,IAAI,CAACmC,6BAA6B,GAAG,IAAI,CAACpD,SAAS,CAAC,IAAI,CAAC2C,YAAY,CAACU,UAAU,CAACtF,MAAM,CAACuF,wBAAwB,EAAE,IAAI,CAACpB,kBAAkB,CAAC,CAAC;MAC3I,IAAI,CAACqB,2CAA2C,GAAG,IAAI,CAACvD,SAAS,CAAC,IAAItD,gBAAgB,CAAC,MAAM;QACzF,IAAI,CAACwG,8BAA8B,CAACxB,KAAK,GAAG,EAAE;MAClD,CAAC,EAAE,GAAG,CAAC,CAAC;MACR,IAAI,CAAC8B,uBAAuB,GAAG,IAAI,CAACxD,SAAS,CAAC,IAAItD,gBAAgB,CAAC,MAAM;QACrE,IAAI,CAACsG,cAAc,CAACS,OAAO,GAAG,IAAI,CAACN,UAAU,CAACM,OAAO,GAAG,KAAK;MACjE,CAAC,EAAE,GAAG,CAAC,CAAC;MACR,IAAI,CAACC,OAAO,GAAG,IAAI,CAAC1D,SAAS,CAACpB,oBAAoB,CAACyB,cAAc,CAACsD,8BAA8B,EAAE,IAAI,CAACb,KAAK,CAACY,OAAO,EAAE3F,MAAM,CAAC6F,uBAAuB,EAAE;QAClJC,WAAW,EAAE;UAAEC,gBAAgB,EAAE;QAAK,CAAC;QACvCC,cAAc,EAAE;UAAEC,YAAY,EAAEC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,SAAS;QAAE,CAAC;QAC9DC,sBAAsB,EAAEA,CAACvC,MAAM,EAAEwC,OAAO,KAAK;UACzC,IAAIxC,MAAM,YAAY5D,cAAc,EAAE;YAClC,OAAOY,oBAAoB,CAACyB,cAAc,CAACgE,iBAAiB,EAAEzC,MAAM,EAAE1C,SAAS,CAAC;UACpF;UACA,IAAI0C,MAAM,KAAK,IAAI,CAACsB,8BAA8B,EAAE;YAChD,MAAMoB,CAAC,GAAG,IAAIC,2BAA2B,CAACrF,SAAS,EAAE0C,MAAM,EAAE;cAAEF,KAAK,EAAE,IAAI;cAAE8C,IAAI,EAAE;YAAM,CAAC,CAAC;YAC1FF,CAAC,CAACG,QAAQ,CAAC,0BAA0B,CAAC;YACtC,OAAOH,CAAC;UACZ;UACA,OAAOpF,SAAS;QACpB,CAAC;QACDwF,eAAe,EAAE;MACrB,CAAC,CAAC,CAAC;MACH,IAAI,CAAChB,OAAO,CAACiB,0BAA0B,CAAC,CACpC,IAAI,CAAC3B,cAAc,EACnB,IAAI,CAACE,8BAA8B,EACnC,IAAI,CAACC,UAAU,CAClB,CAAC;MACF,IAAI,CAACnD,SAAS,CAAC,IAAI,CAAC0D,OAAO,CAACkB,6BAA6B,CAACC,CAAC,IAAI;QAC3D3I,oCAAoC,CAACmF,gBAAgB,GAAGwD,CAAC;MAC7D,CAAC,CAAC,CAAC;MACH,IAAI,CAAC7E,SAAS,CAAClD,OAAO,CAACsC,MAAM,IAAI;QAC7B;QACA,IAAI,CAACmD,SAAS,CAACjD,IAAI,CAACF,MAAM,CAAC;QAC3B,IAAI,CAACV,MAAM,CAACoG,mBAAmB,CAAC,IAAI,CAAC;MACzC,CAAC,CAAC,CAAC;MACH,IAAI,CAAC9E,SAAS,CAAClD,OAAO,CAACsC,MAAM,IAAI;QAC7B;QACA,MAAM2F,eAAe,GAAG,IAAI,CAACtC,gBAAgB,CAACnD,IAAI,CAACF,MAAM,CAAC;QAC1D,MAAM4F,oBAAoB,GAAG,IAAI,CAACxC,qBAAqB,CAAClD,IAAI,CAACF,MAAM,CAAC;QACpE,IAAI2F,eAAe,KAAK7F,SAAS,EAAE;UAC/B,IAAI,CAACqE,2CAA2C,CAAC0B,MAAM,CAAC,CAAC;UACzD,IAAI,CAAC/B,8BAA8B,CAACxB,KAAK,GAAG,GAAGsD,oBAAoB,GAAG,CAAC,IAAID,eAAe,EAAE;QAChG,CAAC,MACI;UACD,IAAI,CAACxB,2CAA2C,CAAC2B,QAAQ,CAAC,CAAC;QAC/D;QACA,IAAIH,eAAe,KAAK7F,SAAS,IAAI6F,eAAe,GAAG,CAAC,EAAE;UACtD,IAAI,CAACvB,uBAAuB,CAACyB,MAAM,CAAC,CAAC;UACrC,IAAI,CAACjC,cAAc,CAACS,OAAO,GAAG,IAAI,CAACN,UAAU,CAACM,OAAO,GAAG,IAAI;QAChE,CAAC,MACI;UACD,IAAI,CAACD,uBAAuB,CAAC0B,QAAQ,CAAC,CAAC;QAC3C;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAAClF,SAAS,CAAClD,OAAO,CAACsC,MAAM,IAAI;QAC7B;QACA,MAAM+F,aAAa,GAAG,IAAI,CAACzC,cAAc,CAACpD,IAAI,CAACF,MAAM,CAAC;QACtD,MAAMgG,YAAY,GAAGD,aAAa,CAACE,GAAG,CAACjK,CAAC,KAAK;UACzCkK,KAAK,EAAEpG,SAAS;UAChBqC,EAAE,EAAEnG,CAAC,CAACmG,EAAE;UACRkC,OAAO,EAAE,IAAI;UACbtB,OAAO,EAAE/G,CAAC,CAAC+G,OAAO,IAAI,EAAE;UACxBT,KAAK,EAAEtG,CAAC,CAACmK,KAAK;UACdC,GAAG,EAAGC,KAAK,IAAK;YACZ,OAAO,IAAI,CAAC5D,eAAe,CAACC,cAAc,CAAC1G,CAAC,CAACmG,EAAE,CAAC;UACpD;QACJ,CAAC,CAAC,CAAC;QACH,KAAK,MAAM,CAACmE,CAAC,EAAEC,KAAK,CAAC,IAAI,IAAI,CAACvC,6BAA6B,CAACwC,UAAU,CAAC,CAAC,EAAE;UACtE,KAAK,MAAMhE,MAAM,IAAI+D,KAAK,EAAE;YACxB,IAAI/D,MAAM,YAAY5D,cAAc,EAAE;cAClCoH,YAAY,CAACS,IAAI,CAACjE,MAAM,CAAC;YAC7B;UACJ;QACJ;QACA,IAAIwD,YAAY,CAAC9J,MAAM,GAAG,CAAC,EAAE;UACzB8J,YAAY,CAACU,OAAO,CAAC,IAAItJ,SAAS,CAAC,CAAC,CAAC;QACzC;QACA,IAAI,CAACkH,OAAO,CAACqC,6BAA6B,CAACX,YAAY,CAAC;MAC5D,CAAC,CAAC,CAAC;IACP;IACAY,KAAKA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACzE,EAAE;IAAE;IAC1B0E,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAACnD,KAAK,CAACoD,IAAI;IAC1B;IACAC,WAAWA,CAAA,EAAG;MACV,OAAO;QACHhH,QAAQ,EAAE,IAAI,CAACoD,SAAS,CAAC6D,GAAG,CAAC,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,CAAC,6CAA6C,CAAC,CAAC,4CAA4C;QAC1GC,gBAAgB,EAAE,CAAC,CAAC;MACxB,CAAC;IACL;EACJ,CAAC;EAAA,OAjIGhG,kCAAkC;AAAA,IAiIrC;AACDA,kCAAkC,GAAGpE,oCAAoC,GAAGnB,UAAU,CAAC,CACnFgB,OAAO,CAAC,CAAC,EAAEkC,eAAe,CAAC,EAC3BlC,OAAO,CAAC,CAAC,EAAEqC,qBAAqB,CAAC,EACjCrC,OAAO,CAAC,CAAC,EAAEsC,kBAAkB,CAAC,EAC9BtC,OAAO,CAAC,CAAC,EAAEmC,kBAAkB,CAAC,EAC9BnC,OAAO,CAAC,EAAE,EAAE+B,YAAY,CAAC,CAC5B,EAAEwC,kCAAkC,CAAC;AACtC,SAASA,kCAAkC;AAC3C,MAAMiE,2BAA2B,SAASnI,cAAc,CAAC;EACrDqC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGpD,SAAS,CAAC;IACnB,IAAI,CAACkL,UAAU,GAAGrH,SAAS;EAC/B;EACAuF,QAAQA,CAAC1B,SAAS,EAAE;IAChB,IAAI,CAACwD,UAAU,GAAGxD,SAAS;EAC/B;EACAyD,MAAMA,CAACC,SAAS,EAAE;IACd,KAAK,CAACD,MAAM,CAACC,SAAS,CAAC;IACvB,IAAI,IAAI,CAACF,UAAU,EAAE;MACjBE,SAAS,CAACC,SAAS,CAACtG,GAAG,CAAC,IAAI,CAACmG,UAAU,CAAC;IAC5C;EACJ;EACAI,aAAaA,CAAA,EAAG;IACZ;EAAA;AAER;AACA,MAAMtC,iBAAiB,SAAS1G,uBAAuB,CAAC;EACpDiJ,WAAWA,CAAA,EAAG;IACV,MAAM7E,EAAE,GAAG,IAAI,CAAC8E,kBAAkB,CAAC5E,gBAAgB,CAAC,IAAI,CAAC6E,OAAO,CAACvF,EAAE,EAAE,IAAI,CAACW,kBAAkB,CAAC;IAC7F,IAAI,CAACH,EAAE,EAAE;MACL,OAAO,KAAK,CAAC6E,WAAW,CAAC,CAAC;IAC9B;IACA,IAAI,IAAI,CAAClF,KAAK,EAAE;MACZ,MAAMqF,GAAG,GAAG5K,CAAC,CAAC,gBAAgB,CAAC,CAAC+J,IAAI;MACpC,MAAMc,CAAC,GAAG,IAAI,CAAChH,SAAS,CAAC,IAAI3D,eAAe,CAAC0K,GAAG,EAAE3J,EAAE,EAAE;QAAE6J,YAAY,EAAE,IAAI;QAAE,GAAG3K;MAA+B,CAAC,CAAC,CAAC;MACjH0K,CAAC,CAACE,GAAG,CAACnF,EAAE,CAAC;MACT,IAAI,CAACL,KAAK,CAACyF,WAAW,GAAG,IAAI,CAACL,OAAO,CAACpF,KAAK;MAC3C,IAAI,CAACA,KAAK,CAAC0F,WAAW,CAACL,GAAG,CAAC;MAC3B,IAAI,CAACrF,KAAK,CAACgF,SAAS,CAACtG,GAAG,CAAC,oCAAoC,CAAC;IAClE;EACJ;EACAuG,aAAaA,CAAA,EAAG;IACZ;EAAA;AAER;AACA,IAAIhD,8BAA8B,GAAG,MAAMA,8BAA8B,SAAS9F,gBAAgB,CAAC;EAC/FY,WAAWA,CAACgI,SAAS,EAAEY,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEzF,iBAAiB,EAAE0F,cAAc,EAAEC,gBAAgB,EAAE;IAC9I,KAAK,CAAClB,SAAS,EAAE;MAAEmB,SAAS,EAAEP,MAAM;MAAE,GAAGC;IAAS,CAAC,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEzF,iBAAiB,EAAE0F,cAAc,EAAEC,gBAAgB,CAAC;IAC7J,IAAI,CAACN,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACK,IAAI,GAAG,IAAI,CAACC,MAAM,CAAC1H,GAAG,CAAC,IAAI,CAACmH,WAAW,CAAClE,UAAU,CAAC,IAAI,CAACgE,MAAM,EAAE,IAAI,CAACG,iBAAiB,EAAE;MAAEO,2BAA2B,EAAE;IAAK,CAAC,CAAC,CAAC;IACpI,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACH,MAAM,CAAC1H,GAAG,CAAC,IAAI,CAACyH,IAAI,CAACK,WAAW,CAAC,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC;IAClE,IAAI,CAACA,aAAa,CAAC,CAAC;EACxB;EACAA,aAAaA,CAAA,EAAG;IACZ,MAAMC,OAAO,GAAG,EAAE;IAClB,MAAMC,SAAS,GAAG,EAAE;IACpBzK,+BAA+B,CAAC,IAAI,CAACiK,IAAI,EAAE,IAAI,CAACP,QAAQ,EAAEzD,WAAW,EAAE;MAAEuE,OAAO;MAAEC;IAAU,CAAC,EAAE,IAAI,CAACf,QAAQ,EAAEvD,cAAc,EAAEC,YAAY,EAAE,IAAI,CAACsD,QAAQ,EAAEvD,cAAc,EAAEuE,mBAAmB,EAAE,IAAI,CAAChB,QAAQ,EAAEvD,cAAc,EAAEwE,6BAA6B,CAAC;IAC7PF,SAAS,CAACxC,IAAI,CAAC,GAAG,IAAI,CAACmC,iBAAiB,CAAC;IACzCI,OAAO,CAACtC,OAAO,CAAC,GAAG,IAAI,CAACmC,uBAAuB,CAAC;IAChD,IAAI,CAACO,UAAU,CAACJ,OAAO,EAAEC,SAAS,CAAC;EACvC;EACA1D,0BAA0BA,CAAC8D,OAAO,EAAE;IAChC,IAAIhM,MAAM,CAAC,IAAI,CAACwL,uBAAuB,EAAEQ,OAAO,EAAE,CAACnE,CAAC,EAAEoE,CAAC,KAAKpE,CAAC,KAAKoE,CAAC,CAAC,EAAE;MAClE;IACJ;IACA,IAAI,CAACT,uBAAuB,GAAGQ,OAAO;IACtC,IAAI,CAACN,aAAa,CAAC,CAAC;EACxB;EACApC,6BAA6BA,CAAC0C,OAAO,EAAE;IACnC,IAAIhM,MAAM,CAAC,IAAI,CAACuL,iBAAiB,EAAES,OAAO,EAAE,CAACnE,CAAC,EAAEoE,CAAC,KAAKpE,CAAC,KAAKoE,CAAC,CAAC,EAAE;MAC5D;IACJ;IACA,IAAI,CAACV,iBAAiB,GAAGS,OAAO;IAChC,IAAI,CAACN,aAAa,CAAC,CAAC;EACxB;AACJ,CAAC;AACDxE,8BAA8B,GAAG5I,UAAU,CAAC,CACxCgB,OAAO,CAAC,CAAC,EAAE+B,YAAY,CAAC,EACxB/B,OAAO,CAAC,CAAC,EAAEmC,kBAAkB,CAAC,EAC9BnC,OAAO,CAAC,CAAC,EAAEoC,mBAAmB,CAAC,EAC/BpC,OAAO,CAAC,CAAC,EAAEsC,kBAAkB,CAAC,EAC9BtC,OAAO,CAAC,CAAC,EAAEkC,eAAe,CAAC,EAC3BlC,OAAO,CAAC,CAAC,EAAEuC,iBAAiB,CAAC,CAChC,EAAEqF,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}