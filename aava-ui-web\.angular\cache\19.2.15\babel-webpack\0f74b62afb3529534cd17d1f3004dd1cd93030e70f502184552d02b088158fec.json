{"ast": null, "code": "import { provideRouter } from '@angular/router';\nimport { routes } from './app.routes';\nexport const appConfig = {\n  providers: [provideRouter(routes)]\n};", "map": {"version": 3, "names": ["provideRouter", "routes", "appConfig", "providers"], "sources": ["C:\\console\\aava-ui-web\\projects\\marketing\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig } from '@angular/core';\r\nimport { provideRouter } from '@angular/router';\r\nimport { routes } from './app.routes';\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    provideRouter(routes)\r\n  ]\r\n}; "], "mappings": "AACA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,MAAM,QAAQ,cAAc;AAErC,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTH,aAAa,CAACC,MAAM,CAAC;CAExB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}